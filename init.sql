-- 创建 Nacos 数据库及表
CREATE DATABASE IF NOT EXISTS nacos DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户分组数据库
CREATE DATABASE IF NOT EXISTS flux_manager DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE usergroup;

-- 创建用户分组关系表
CREATE TABLE IF NOT EXISTS `user_group_relation` (
                                                     `id` bigint NOT NULL AUTO_INCREMENT,
                                                     `user_id` varchar(64) NOT NULL COMMENT '用户ID',
                                                     `source` varchar(32) NOT NULL COMMENT '来源标识（运营商代码：dx/lt/yd）',
                                                     `group_ids` longtext NOT NULL COMMENT '分组IDs，逗号分隔',
                                                     `platform` varchar(128) DEFAULT NULL COMMENT '平台标识',
                                                     `effective_from` datetime DEFAULT NULL COMMENT '生效时间',
                                                     `last_seen_at` datetime DEFAULT NULL COMMENT '最后更新时间',
                                                     `description` varchar(512) DEFAULT NULL COMMENT '关系描述，例如该用户加入此分组的业务背景',
                                                     `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                     `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                     PRIMARY KEY (`id`),
                                                     UNIQUE KEY `uk_user_source` (`user_id`,`source`),
                                                     KEY `idx_source` (`source`),
                                                     KEY `idx_user_id` (`user_id`),
                                                     KEY `idx_platform` (`platform`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户分组关系表';

-- 创建分组信息表
CREATE TABLE IF NOT EXISTS `group_info` (
                                            `id` bigint NOT NULL AUTO_INCREMENT,
                                            `group_id` varchar(64) NOT NULL COMMENT '分组ID',
                                            `source` varchar(32) NOT NULL COMMENT '来源标识（运营商代码：dx/lt/yd）',
                                            `strategy_id` varchar(64) NOT NULL COMMENT '策略ID',
                                            `group_name` varchar(128) NOT NULL COMMENT '分组名称',
                                            `description` varchar(512) DEFAULT NULL COMMENT '分组描述',
                                            `platform` varchar(128) DEFAULT NULL COMMENT '平台标识',
                                            `active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
                                            `file_url` varchar(512) DEFAULT NULL COMMENT '最新文件URL',
                                            `coverage` bigint DEFAULT NULL COMMENT '覆盖度（用户数量）',
                                            `generate_time` datetime DEFAULT NULL COMMENT '生成时间',
                                            `md5sum` char(32) DEFAULT NULL COMMENT 'MD5校验值',
                                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                            PRIMARY KEY (`id`),
                                            UNIQUE KEY `uk_group_id` (`group_id`),
                                            KEY `idx_strategy_id` (`strategy_id`),
                                            KEY `idx_platform` (`platform`),
                                            KEY `idx_active` (`active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分组信息表';


-- 添加数据库优化设置
SET GLOBAL max_connections = 2000;
SET GLOBAL innodb_buffer_pool_size = 4294967296; -- 4GB
SET GLOBAL innodb_flush_log_at_trx_commit = 2;
SET GLOBAL innodb_file_per_table = 1;
SET GLOBAL innodb_flush_method = 'O_DIRECT';
SET GLOBAL innodb_buffer_pool_instances = 8;

-- 黑名单和白名单表结构
-- 创建时间：2025-07-07
-- 作者：Claude 4.0 sonnet

-- 黑名单表
CREATE TABLE IF NOT EXISTS `user_blacklist` (
                                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                `user_id` varchar(64) NOT NULL COMMENT '用户ID',
                                                `source` varchar(32) NOT NULL COMMENT '来源：telecom(电信)、unicom(联通)、mobile(移动)、other(其他)',
                                                `remark` varchar(512) DEFAULT NULL COMMENT '备注（违规原因）',
                                                `add_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
                                                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                PRIMARY KEY (`id`),
                                                UNIQUE KEY `uk_user_source` (`user_id`, `source`) COMMENT '用户ID和来源的唯一索引',
                                                KEY `idx_user_id` (`user_id`) COMMENT '用户ID索引',
                                                KEY `idx_source` (`source`) COMMENT '来源索引',
                                                KEY `idx_add_time` (`add_time`) COMMENT '添加时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户黑名单表';

-- 白名单表
CREATE TABLE IF NOT EXISTS `user_whitelist` (
                                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                `user_id` varchar(64) NOT NULL COMMENT '用户ID',
                                                `source` varchar(32) NOT NULL COMMENT '来源：telecom(电信)、unicom(联通)、mobile(移动)、other(其他)',
                                                `remark` varchar(512) DEFAULT NULL COMMENT '备注（添加原因）',
                                                `add_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
                                                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                PRIMARY KEY (`id`),
                                                UNIQUE KEY `uk_user_source` (`user_id`, `source`) COMMENT '用户ID和来源的唯一索引',
                                                KEY `idx_user_id` (`user_id`) COMMENT '用户ID索引',
                                                KEY `idx_source` (`source`) COMMENT '来源索引',
                                                KEY `idx_add_time` (`add_time`) COMMENT '添加时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户白名单表';

-- 插入一些示例数据（可选，用于测试）

-- 黑名单示例数据
INSERT INTO `user_blacklist` (`user_id`, `source`, `remark`, `add_time`) VALUES
                                                                             ('blackuser001', 'telecom', '恶意刷量', NOW()),
                                                                             ('blackuser002', 'unicom', '违规操作', NOW()),
                                                                             ('blackuser003', 'mobile', '异常行为', NOW()),
                                                                             ('blackuser004', 'other', '系统检测异常', NOW());

-- 白名单示例数据
INSERT INTO `user_whitelist` (`user_id`, `source`, `remark`, `add_time`) VALUES
                                                                             ('testuser001', 'telecom', '内部测试用户', NOW()),
                                                                             ('testuser002', 'unicom', '业务测试账号', NOW()),
                                                                             ('testuser003', 'mobile', '功能验证用户', NOW()),
                                                                             ('testuser004', 'other', '开发测试账号', NOW());

-- 创建视图以便于查询统计信息

-- 黑名单统计视图
CREATE OR REPLACE VIEW `v_blacklist_stats` AS
SELECT
    source,
    COUNT(*) as user_count,
    DATE(add_time) as add_date,
    COUNT(CASE WHEN DATE(add_time) = CURDATE() THEN 1 END) as today_count
FROM user_blacklist
GROUP BY source, DATE(add_time)
ORDER BY add_date DESC, source;

-- 白名单统计视图
CREATE OR REPLACE VIEW `v_whitelist_stats` AS
SELECT
    source,
    COUNT(*) as user_count,
    DATE(add_time) as add_date,
    COUNT(CASE WHEN DATE(add_time) = CURDATE() THEN 1 END) as today_count
FROM user_whitelist
GROUP BY source, DATE(add_time)
ORDER BY add_date DESC, source;

-- 查询示例

-- 查询黑名单总数
-- SELECT COUNT(*) as total_blacklist FROM user_blacklist;

-- 查询白名单总数
-- SELECT COUNT(*) as total_whitelist FROM user_whitelist;

-- 按来源统计黑名单
-- SELECT source, COUNT(*) as count FROM user_blacklist GROUP BY source;

-- 按来源统计白名单
-- SELECT source, COUNT(*) as count FROM user_whitelist GROUP BY source;

-- 查询今日新增黑名单
-- SELECT COUNT(*) as today_blacklist FROM user_blacklist WHERE DATE(add_time) = CURDATE();

-- 查询今日新增白名单
-- SELECT COUNT(*) as today_whitelist FROM user_whitelist WHERE DATE(add_time) = CURDATE();


-- 创建数据导入日志表
USE flux_manager;

CREATE TABLE IF NOT EXISTS `data_import_log` (
                                                 `id` bigint NOT NULL AUTO_INCREMENT,
                                                 `task_id` varchar(64) NOT NULL COMMENT '任务ID',
                                                 `file_name` varchar(255) NOT NULL COMMENT '文件名',
                                                 `file_size` bigint NOT NULL COMMENT '文件大小（字节）',
                                                 `source` varchar(32) NOT NULL COMMENT '来源标识',
                                                 `status` varchar(32) NOT NULL COMMENT '状态：PENDING, PROCESSING, COMPLETED, FAILED',
                                                 `total_records` int DEFAULT 0 COMMENT '总记录数',
                                                 `processed_records` int DEFAULT 0 COMMENT '已处理记录数',
                                                 `inserted_records` int DEFAULT 0 COMMENT '成功插入记录数',
                                                 `updated_records` int DEFAULT 0 COMMENT '成功更新记录数',
                                                 `failed_records` int DEFAULT 0 COMMENT '失败记录数',
                                                 `processing_time_ms` bigint DEFAULT 0 COMMENT '处理耗时（毫秒）',
                                                 `error_message` text COMMENT '错误信息',
                                                 `description` varchar(512) COMMENT '导入描述',
                                                 `async_mode` tinyint(1) DEFAULT 1 COMMENT '是否异步模式',
                                                 `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                 `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                 `completed_at` timestamp NULL COMMENT '完成时间',
                                                 PRIMARY KEY (`id`),
                                                 UNIQUE KEY `uk_task_id` (`task_id`),
                                                 KEY `idx_status` (`status`),
                                                 KEY `idx_source` (`source`),
                                                 KEY `idx_created_at` (`created_at`),
                                                 KEY `idx_completed_at` (`completed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据导入日志表';

-- 创建索引以优化查询性能
CREATE INDEX `idx_status_created_at` ON `data_import_log` (`status`, `created_at`);
CREATE INDEX `idx_source_created_at` ON `data_import_log` (`source`, `created_at`);

-- 插入一些示例数据（可选，用于测试）
INSERT INTO `data_import_log` (
    `task_id`, `file_name`, `file_size`, `source`, `status`,
    `total_records`, `processed_records`, `inserted_records`, `updated_records`, `failed_records`,
    `processing_time_ms`, `description`, `async_mode`, `created_at`, `completed_at`
) VALUES
-- 今日成功导入记录
('import_1749782234589_test001', '用户数据_20250113.xlsx', 1024000, 'dx', 'COMPLETED',
 5000, 5000, 4800, 200, 0, 15600, '电信用户数据导入', 1,
 DATE_SUB(NOW(), INTERVAL 2 HOUR), DATE_SUB(NOW(), INTERVAL 2 HOUR)),

('import_1749782234590_test002', '联通用户_20250113.xlsx', 2048000, 'lt', 'COMPLETED',
 8000, 8000, 7500, 500, 0, 23400, '联通用户数据导入', 1,
 DATE_SUB(NOW(), INTERVAL 4 HOUR), DATE_SUB(NOW(), INTERVAL 4 HOUR)),

('import_1749782234591_test003', '移动用户_20250113.xlsx', 1536000, 'yd', 'COMPLETED',
 6000, 6000, 5800, 200, 0, 18900, '移动用户数据导入', 1,
 DATE_SUB(NOW(), INTERVAL 6 HOUR), DATE_SUB(NOW(), INTERVAL 6 HOUR)),

-- 今日失败导入记录
('import_1749782234592_test004', '错误数据.xlsx', 512000, 'dx', 'FAILED',
 0, 0, 0, 0, 0, 1200, '文件格式错误导入失败', 1,
 DATE_SUB(NOW(), INTERVAL 1 HOUR), DATE_SUB(NOW(), INTERVAL 1 HOUR)),

-- 昨日导入记录
('import_1749695834589_test005', '昨日用户数据.xlsx', 3072000, 'dx', 'COMPLETED',
 12000, 12000, 11500, 500, 0, 45600, '昨日电信用户数据导入', 1,
 DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),

('import_1749695834590_test006', '昨日联通数据.xlsx', 2560000, 'lt', 'COMPLETED',
 10000, 10000, 9800, 200, 0, 38200, '昨日联通用户数据导入', 1,
 DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),

-- 本周其他导入记录
('import_1749609434589_test007', '本周数据1.xlsx', 1800000, 'yd', 'COMPLETED',
 7000, 7000, 6900, 100, 0, 28700, '移动用户数据导入', 1,
 DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY)),

('import_1749523034589_test008', '本周数据2.xlsx', 2200000, 'dx', 'COMPLETED',
 9000, 9000, 8700, 300, 0, 35400, '电信用户数据导入', 1,
 DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY)),

-- 正在处理的任务
('import_1749782234593_test009', '正在处理.xlsx', 4096000, 'dx', 'PROCESSING',
 15000, 7500, 7200, 300, 0, 0, '大批量用户数据导入', 1,
 DATE_SUB(NOW(), INTERVAL 30 MINUTE), NULL),

-- 等待处理的任务
('import_1749782234594_test010', '等待处理.xlsx', 1024000, 'lt', 'PENDING',
 0, 0, 0, 0, 0, 0, '联通用户数据导入', 1,
 DATE_SUB(NOW(), INTERVAL 10 MINUTE), NULL);

-- 创建视图以便于查询统计数据
CREATE OR REPLACE VIEW `v_data_import_daily_stats` AS
SELECT
    DATE(created_at) as import_date,
    COUNT(*) as total_imports,
    SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as successful_imports,
    SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_imports,
    SUM(CASE WHEN status IN ('PENDING', 'PROCESSING') THEN 1 ELSE 0 END) as pending_imports,
    SUM(CASE WHEN status = 'COMPLETED' THEN total_records ELSE 0 END) as total_records_processed,
    SUM(CASE WHEN status = 'COMPLETED' THEN inserted_records ELSE 0 END) as total_inserted,
    SUM(CASE WHEN status = 'COMPLETED' THEN updated_records ELSE 0 END) as total_updated,
    SUM(CASE WHEN status = 'COMPLETED' THEN failed_records ELSE 0 END) as total_failed,
    AVG(CASE WHEN status = 'COMPLETED' AND processing_time_ms > 0 THEN processing_time_ms ELSE NULL END) as avg_processing_time_ms,
    SUM(file_size) as total_file_size
FROM data_import_log
GROUP BY DATE(created_at)
ORDER BY import_date DESC;

-- 创建视图以便于查询源别统计
CREATE OR REPLACE VIEW `v_data_import_source_stats` AS
SELECT
    source,
    DATE(created_at) as import_date,
    COUNT(*) as imports_count,
    SUM(CASE WHEN status = 'COMPLETED' THEN total_records ELSE 0 END) as records_processed,
    AVG(CASE WHEN status = 'COMPLETED' AND processing_time_ms > 0 THEN processing_time_ms ELSE NULL END) as avg_processing_time_ms
FROM data_import_log
WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY source, DATE(created_at)
ORDER BY import_date DESC, source;
