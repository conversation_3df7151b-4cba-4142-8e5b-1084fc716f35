package com.iptv.flux.service.integration;

import com.iptv.flux.service.config.FTPConfig;
import com.iptv.flux.service.util.FTPFileProcessor;
import com.iptv.flux.service.util.GroupsFileParser;
import com.iptv.flux.service.util.UserGroupFileParser;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 流量同步功能验证测试
 */
@SpringBootTest
@ActiveProfiles("test")
class TrafficSyncFunctionalTest {

    @Autowired(required = false)
    private FTPConfig.FTPProperties ftpProperties;

    @Autowired(required = false)
    private FTPFileProcessor ftpFileProcessor;

    @Autowired(required = false)
    private GroupsFileParser groupsFileParser;

    @Autowired(required = false)
    private UserGroupFileParser userGroupFileParser;

    /**
     * 测试FTP配置Bean是否正确创建
     */
    @Test
    void testFTPConfigurationLoaded() {
        assertNotNull(ftpProperties, "FTP配置应该被正确加载");
        assertEquals("localhost", ftpProperties.getHost(), "FTP主机配置应该正确");
        assertEquals(21, ftpProperties.getPort(), "FTP端口配置应该正确");
        assertEquals("test", ftpProperties.getUsername(), "FTP用户名配置应该正确");
        assertEquals("test", ftpProperties.getPassword(), "FTP密码配置应该正确");
        
        // 验证连接池配置
        assertNotNull(ftpProperties.getPool(), "FTP连接池配置应该存在");
        assertEquals(2, ftpProperties.getPool().getMaxTotal(), "连接池最大连接数应该正确");
        assertEquals(1, ftpProperties.getPool().getMaxIdle(), "连接池最大空闲数应该正确");
    }

    /**
     * 测试FTP文件处理器Bean是否正确创建
     */
    @Test
    void testFTPFileProcessorLoaded() {
        assertNotNull(ftpFileProcessor, "FTP文件处理器应该被正确创建");
    }

    /**
     * 测试文件解析器Bean是否正确创建
     */
    @Test
    void testFileParsersLoaded() {
        assertNotNull(groupsFileParser, "分组文件解析器应该被正确创建");
        assertNotNull(userGroupFileParser, "用户分组文件解析器应该被正确创建");
    }

    /**
     * 测试文件解析器基本功能
     */
    @Test
    void testFileParserBasicFunctionality() {
        // 测试空文件验证
        assertFalse(groupsFileParser.isValidGroupsFile(null), "空文件应该被识别为无效");
        assertFalse(userGroupFileParser.isValidUserGroupFile(null), "空文件应该被识别为无效");
    }

    /**
     * 测试MD5计算功能
     */
    @Test
    void testMD5Calculation() throws Exception {
        // 创建临时测试文件
        java.io.File testFile = java.nio.file.Files.createTempFile("test", ".txt").toFile();
        java.nio.file.Files.write(testFile.toPath(), "Hello World".getBytes());
        
        try {
            String md5 = ftpFileProcessor.calculateMD5(testFile);
            assertNotNull(md5, "MD5计算结果不应该为空");
            assertEquals(32, md5.length(), "MD5长度应该是32位");
            assertEquals("b10a8db164e0754105b7a99be72e3fe5", md5, "MD5值应该正确");
        } finally {
            testFile.delete();
        }
    }

    /**
     * 测试MD5验证功能
     */
    @Test
    void testMD5Verification() throws Exception {
        // 创建临时测试文件
        java.io.File testFile = java.nio.file.Files.createTempFile("test", ".txt").toFile();
        java.nio.file.Files.write(testFile.toPath(), "Hello World".getBytes());
        
        try {
            // 正确的MD5验证
            assertTrue(ftpFileProcessor.verifyMD5(testFile, "b10a8db164e0754105b7a99be72e3fe5"), 
                    "正确的MD5应该验证通过");
            
            // 错误的MD5验证
            assertFalse(ftpFileProcessor.verifyMD5(testFile, "wrong_md5"), 
                    "错误的MD5应该验证失败");
            
            // 空MD5验证（应该跳过）
            assertTrue(ftpFileProcessor.verifyMD5(testFile, null), 
                    "空MD5应该跳过验证");
            assertTrue(ftpFileProcessor.verifyMD5(testFile, ""), 
                    "空字符串MD5应该跳过验证");
        } finally {
            testFile.delete();
        }
    }

    /**
     * 测试文件清理功能
     */
    @Test
    void testFileCleanup() throws Exception {
        // 创建临时测试文件
        java.io.File testFile = java.nio.file.Files.createTempFile("test", ".txt").toFile();
        assertTrue(testFile.exists(), "测试文件应该存在");
        
        // 执行清理
        ftpFileProcessor.cleanupTempFile(testFile);
        assertFalse(testFile.exists(), "文件应该被清理");
        
        // 测试清理不存在的文件（不应该抛出异常）
        assertDoesNotThrow(() -> ftpFileProcessor.cleanupTempFile(testFile), 
                "清理不存在的文件不应该抛出异常");
        
        // 测试清理null文件（不应该抛出异常）
        assertDoesNotThrow(() -> ftpFileProcessor.cleanupTempFile(null), 
                "清理null文件不应该抛出异常");
    }
}
