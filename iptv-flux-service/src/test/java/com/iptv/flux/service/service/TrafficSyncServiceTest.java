package com.iptv.flux.service.service;

import com.iptv.flux.common.dto.TrafficSyncNotificationDTO;
import com.iptv.flux.common.dto.TrafficSyncResponseDTO;
import com.iptv.flux.service.repository.DataImportLogRepository;
import com.iptv.flux.service.repository.DataImportRepository;
import com.iptv.flux.service.util.FTPFileProcessor;
import com.iptv.flux.service.util.GroupsFileParser;
import com.iptv.flux.service.util.UserGroupFileParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TrafficSyncService单元测试
 */
@ExtendWith(MockitoExtension.class)
class TrafficSyncServiceTest {

    @Mock
    private FTPFileProcessor ftpFileProcessor;

    @Mock
    private GroupsFileParser groupsFileParser;

    @Mock
    private UserGroupFileParser userGroupFileParser;

    @Mock
    private DataImportRepository dataImportRepository;

    @Mock
    private DataImportLogRepository dataImportLogRepository;

    @Mock
    private GroupInfoService groupInfoService;

    @Mock
    private UserGroupService userGroupService;

    @Mock
    private DataImportService dataImportService;

    @InjectMocks
    private TrafficSyncService trafficSyncService;

    private TrafficSyncNotificationDTO testNotification;
    private File mockFile;

    @BeforeEach
    void setUp() throws IOException {
        // 创建测试通知对象
        testNotification = TrafficSyncNotificationDTO.builder()
                .updateTime("20250814120000")
                .groupsFileURL("******************************/groups.txt")
                .userGroupFileURL("******************************/userGroup.txt")
                .operatorFiles(Arrays.asList(
                        TrafficSyncNotificationDTO.OperatorFileInfo.builder()
                                .sysID("dx")
                                .fileURL("******************************/dx.txt")
                                .md5sum("abc123")
                                .build(),
                        TrafficSyncNotificationDTO.OperatorFileInfo.builder()
                                .sysID("lt")
                                .fileURL("******************************/lt.txt")
                                .build()
                ))
                .options(TrafficSyncNotificationDTO.ProcessingOptions.builder()
                        .async(false)
                        .batchSize(1000)
                        .overwrite(true)
                        .verifyMD5(true)
                        .build())
                .build();

        // 创建模拟文件
        mockFile = Files.createTempFile("test", ".txt").toFile();
        mockFile.deleteOnExit();
    }

    @Test
    void testProcessTrafficSyncNotification_SyncMode_Success() throws Exception {
        // 准备Mock行为
        setupSuccessfulMocks();

        // 执行测试
        TrafficSyncResponseDTO response = trafficSyncService.processTrafficSyncNotification(testNotification);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getTaskId());
        assertEquals("COMPLETED", response.getStatus());
        assertNotNull(response.getProcessTime());
        assertNotNull(response.getCompletedTime());
        assertNotNull(response.getDurationMs());
        assertEquals("数据同步处理完成", response.getMessage());

        // 验证方法调用
        verify(ftpFileProcessor, times(4)).downloadFile(anyString()); // groups + userGroup + 2个运营商文件
        verify(groupsFileParser).parseGroupsFile(eq(mockFile), any());
        verify(userGroupFileParser, times(4)).parseToUserGroupMapping(eq(mockFile), anyString()); // userGroup + 3个运营商
        verify(dataImportRepository, times(4)).batchProcessUserGroups(any(), anyString(), anyBoolean());
    }

    @Test
    void testProcessTrafficSyncNotification_AsyncMode() throws Exception {
        // 设置异步模式
        testNotification.getOptions().setAsync(true);

        // 准备Mock行为
        setupSuccessfulMocks();

        // 执行测试
        TrafficSyncResponseDTO response = trafficSyncService.processTrafficSyncNotification(testNotification);

        // 验证结果
        assertNotNull(response);
        assertEquals("PROCESSING", response.getStatus());
        assertEquals("数据同步任务已提交，正在后台处理", response.getMessage());
    }

    @Test
    void testProcessTrafficSyncNotification_ValidationError() {
        // 准备无效通知（缺少必要字段）
        TrafficSyncNotificationDTO invalidNotification = TrafficSyncNotificationDTO.builder()
                .updateTime("20250814120000")
                // 缺少groupsFileURL
                .userGroupFileURL("******************************/userGroup.txt")
                .operatorFiles(Arrays.asList())
                .build();

        // 执行测试
        TrafficSyncResponseDTO response = trafficSyncService.processTrafficSyncNotification(invalidNotification);

        // 验证结果
        assertNotNull(response);
        assertEquals("FAILED", response.getStatus());
        assertTrue(response.getErrorMessage().contains("groups.txt文件路径不能为空"));
    }

    @Test
    void testProcessTrafficSyncNotification_FTPDownloadFailure() throws Exception {
        // 准备Mock行为 - FTP下载失败
        when(ftpFileProcessor.downloadFile(anyString())).thenThrow(new IOException("FTP连接失败"));

        // 执行测试
        TrafficSyncResponseDTO response = trafficSyncService.processTrafficSyncNotification(testNotification);

        // 验证结果
        assertNotNull(response);
        assertEquals("FAILED", response.getStatus());
        assertTrue(response.getErrorMessage().contains("FTP连接失败"));
    }

    @Test
    void testProcessTrafficSyncNotification_FileParsingError() throws Exception {
        // 准备Mock行为 - 文件解析失败
        when(ftpFileProcessor.downloadFile(anyString())).thenReturn(mockFile);
        when(groupsFileParser.isValidGroupsFile(mockFile)).thenReturn(false);

        // 执行测试
        TrafficSyncResponseDTO response = trafficSyncService.processTrafficSyncNotification(testNotification);

        // 验证结果
        assertNotNull(response);
        assertEquals("FAILED", response.getStatus());
        assertTrue(response.getErrorMessage().contains("groups.txt文件格式无效"));
    }

    @Test
    void testProcessTrafficSyncNotification_MD5VerificationFailure() throws Exception {
        // 准备Mock行为 - MD5校验失败
        when(ftpFileProcessor.downloadFile(anyString())).thenReturn(mockFile);
        when(ftpFileProcessor.verifyMD5(mockFile, "abc123")).thenReturn(false);
        when(groupsFileParser.isValidGroupsFile(mockFile)).thenReturn(true);
        when(userGroupFileParser.isValidUserGroupFile(mockFile)).thenReturn(true);

        // 执行测试
        TrafficSyncResponseDTO response = trafficSyncService.processTrafficSyncNotification(testNotification);

        // 验证结果
        assertNotNull(response);
        assertEquals("FAILED", response.getStatus());
        assertTrue(response.getErrorMessage().contains("MD5校验失败"));
    }

    @Test
    void testProcessTrafficSyncNotification_PartialSuccess() throws Exception {
        // 准备Mock行为 - 部分成功
        setupPartialSuccessMocks();

        // 执行测试
        TrafficSyncResponseDTO response = trafficSyncService.processTrafficSyncNotification(testNotification);

        // 验证结果
        assertNotNull(response);
        assertEquals("PARTIAL_SUCCESS", response.getStatus());
        assertNotNull(response.getGroupsProcessed());
        assertNotNull(response.getUsersProcessed());
        assertNotNull(response.getOperatorResults());
    }

    @Test
    void testValidateNotification_ValidData() {
        // 执行测试 - 有效数据不应该抛出异常
        assertDoesNotThrow(() -> {
            trafficSyncService.processTrafficSyncNotification(testNotification);
        });
    }

    @Test
    void testValidateNotification_NullNotification() {
        // 执行测试
        TrafficSyncResponseDTO response = trafficSyncService.processTrafficSyncNotification(null);

        // 验证结果
        assertEquals("FAILED", response.getStatus());
        assertTrue(response.getErrorMessage().contains("同步通知数据不能为空"));
    }

    @Test
    void testValidateNotification_EmptyUpdateTime() {
        // 准备无效数据
        testNotification.setUpdateTime("");

        // 执行测试
        TrafficSyncResponseDTO response = trafficSyncService.processTrafficSyncNotification(testNotification);

        // 验证结果
        assertEquals("FAILED", response.getStatus());
        assertTrue(response.getErrorMessage().contains("更新时间不能为空"));
    }

    @Test
    void testValidateNotification_InvalidSysID() {
        // 准备无效的运营商ID
        testNotification.getOperatorFiles().get(0).setSysID("invalid");

        // 执行测试
        TrafficSyncResponseDTO response = trafficSyncService.processTrafficSyncNotification(testNotification);

        // 验证结果
        assertEquals("FAILED", response.getStatus());
        assertTrue(response.getErrorMessage().contains("无效的运营商系统ID"));
    }

    @Test
    void testValidateNotification_DuplicateSysID() {
        // 准备重复的运营商ID
        testNotification.getOperatorFiles().get(1).setSysID("dx"); // 与第一个相同

        // 执行测试
        TrafficSyncResponseDTO response = trafficSyncService.processTrafficSyncNotification(testNotification);

        // 验证结果
        assertEquals("FAILED", response.getStatus());
        assertTrue(response.getErrorMessage().contains("重复的运营商系统ID"));
    }

    /**
     * 设置成功的Mock行为
     */
    private void setupSuccessfulMocks() throws Exception {
        // FTP下载成功
        when(ftpFileProcessor.downloadFile(anyString())).thenReturn(mockFile);
        
        // 文件格式验证成功
        when(groupsFileParser.isValidGroupsFile(mockFile)).thenReturn(true);
        when(userGroupFileParser.isValidUserGroupFile(mockFile)).thenReturn(true);
        
        // 文件行数统计
        when(groupsFileParser.getTotalLines(mockFile)).thenReturn(100);
        when(userGroupFileParser.getTotalLines(mockFile)).thenReturn(1000);
        
        // MD5校验成功
        when(ftpFileProcessor.verifyMD5(eq(mockFile), anyString())).thenReturn(true);
        
        // 用户分组映射
        Map<String, List<String>> userGroupMapping = new HashMap<>();
        userGroupMapping.put("user1", Arrays.asList("group1", "group2"));
        when(userGroupFileParser.parseToUserGroupMapping(eq(mockFile), anyString())).thenReturn(userGroupMapping);
        
        // 批量处理结果
        DataImportRepository.BatchProcessResult batchResult = new DataImportRepository.BatchProcessResult(
                1000, 1000, 500, 500, 0);
        when(dataImportRepository.batchProcessUserGroups(any(), anyString(), anyBoolean())).thenReturn(batchResult);
        
        // 文件清理
        doNothing().when(ftpFileProcessor).cleanupTempFile(any());
    }

    /**
     * 设置部分成功的Mock行为
     */
    private void setupPartialSuccessMocks() throws Exception {
        // FTP下载成功
        when(ftpFileProcessor.downloadFile(anyString())).thenReturn(mockFile);
        
        // 文件格式验证成功
        when(groupsFileParser.isValidGroupsFile(mockFile)).thenReturn(true);
        when(userGroupFileParser.isValidUserGroupFile(mockFile)).thenReturn(true);
        
        // 文件行数统计
        when(groupsFileParser.getTotalLines(mockFile)).thenReturn(100);
        when(userGroupFileParser.getTotalLines(mockFile)).thenReturn(1000);
        
        // MD5校验成功
        when(ftpFileProcessor.verifyMD5(eq(mockFile), anyString())).thenReturn(true);
        
        // 用户分组映射
        Map<String, List<String>> userGroupMapping = new HashMap<>();
        userGroupMapping.put("user1", Arrays.asList("group1", "group2"));
        when(userGroupFileParser.parseToUserGroupMapping(eq(mockFile), anyString())).thenReturn(userGroupMapping);
        
        // 批量处理结果 - 有部分失败
        DataImportRepository.BatchProcessResult batchResult = new DataImportRepository.BatchProcessResult(
                1000, 950, 500, 450, 50); // 有50条失败记录
        when(dataImportRepository.batchProcessUserGroups(any(), anyString(), anyBoolean())).thenReturn(batchResult);
        
        // 分组信息保存 - 部分失败
        doThrow(new RuntimeException("保存失败")).when(groupInfoService).saveGroupInfo(any());
        
        // 文件清理
        doNothing().when(ftpFileProcessor).cleanupTempFile(any());
    }
}
