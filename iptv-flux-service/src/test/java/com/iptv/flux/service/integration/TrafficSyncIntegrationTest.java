package com.iptv.flux.service.integration;

import com.iptv.flux.common.dto.TrafficSyncNotificationDTO;
import com.iptv.flux.common.dto.TrafficSyncResponseDTO;
import com.iptv.flux.service.service.TrafficSyncService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 流量同步集成测试
 * 测试与现有系统的集成兼容性
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class TrafficSyncIntegrationTest {

    @Autowired
    private TrafficSyncService trafficSyncService;

    /**
     * 测试与现有数据导入功能的兼容性
     */
    @Test
    void testCompatibilityWithExistingDataImport() {
        // 创建测试通知
        TrafficSyncNotificationDTO notification = createTestNotification();
        
        // 执行同步（模拟场景）
        TrafficSyncResponseDTO response = trafficSyncService.processTrafficSyncNotification(notification);
        
        // 验证不影响现有功能
        assertNotNull(response);
        assertNotNull(response.getTaskId());
    }

    /**
     * 测试大数据量处理性能
     */
    @Test
    void testLargeDataPerformance() {
        // 创建大数据量通知
        TrafficSyncNotificationDTO notification = createLargeDataNotification();
        
        long startTime = System.currentTimeMillis();
        TrafficSyncResponseDTO response = trafficSyncService.processTrafficSyncNotification(notification);
        long duration = System.currentTimeMillis() - startTime;
        
        // 验证性能要求（应在合理时间内完成）
        assertTrue(duration < TimeUnit.MINUTES.toMillis(5), "大数据量处理应在5分钟内完成");
    }

    /**
     * 测试并发处理能力
     */
    @Test
    void testConcurrentProcessing() throws InterruptedException {
        // 创建多个并发任务
        TrafficSyncNotificationDTO notification1 = createTestNotification();
        TrafficSyncNotificationDTO notification2 = createTestNotification();
        
        // 并发执行
        Thread thread1 = new Thread(() -> {
            TrafficSyncResponseDTO response = trafficSyncService.processTrafficSyncNotification(notification1);
            assertNotNull(response);
        });
        
        Thread thread2 = new Thread(() -> {
            TrafficSyncResponseDTO response = trafficSyncService.processTrafficSyncNotification(notification2);
            assertNotNull(response);
        });
        
        thread1.start();
        thread2.start();
        
        thread1.join(30000); // 30秒超时
        thread2.join(30000);
        
        assertFalse(thread1.isAlive(), "线程1应该已完成");
        assertFalse(thread2.isAlive(), "线程2应该已完成");
    }

    private TrafficSyncNotificationDTO createTestNotification() {
        return TrafficSyncNotificationDTO.builder()
                .updateTime("20250814120000")
                .groupsFileURL("****************************/groups.txt")
                .userGroupFileURL("****************************/userGroup.txt")
                .operatorFiles(Arrays.asList(
                        TrafficSyncNotificationDTO.OperatorFileInfo.builder()
                                .sysID("dx")
                                .fileURL("****************************/dx.txt")
                                .build()
                ))
                .options(TrafficSyncNotificationDTO.ProcessingOptions.builder()
                        .async(false)
                        .batchSize(100)
                        .build())
                .build();
    }

    private TrafficSyncNotificationDTO createLargeDataNotification() {
        return TrafficSyncNotificationDTO.builder()
                .updateTime("20250814120000")
                .groupsFileURL("****************************/large_groups.txt")
                .userGroupFileURL("****************************/large_userGroup.txt")
                .operatorFiles(Arrays.asList(
                        TrafficSyncNotificationDTO.OperatorFileInfo.builder()
                                .sysID("dx")
                                .fileURL("****************************/large_dx.txt")
                                .build(),
                        TrafficSyncNotificationDTO.OperatorFileInfo.builder()
                                .sysID("lt")
                                .fileURL("****************************/large_lt.txt")
                                .build(),
                        TrafficSyncNotificationDTO.OperatorFileInfo.builder()
                                .sysID("yd")
                                .fileURL("****************************/large_yd.txt")
                                .build()
                ))
                .options(TrafficSyncNotificationDTO.ProcessingOptions.builder()
                        .async(true)
                        .batchSize(1000)
                        .build())
                .build();
    }
}
