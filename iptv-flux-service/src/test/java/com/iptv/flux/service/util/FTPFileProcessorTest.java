package com.iptv.flux.service.util;

import com.iptv.flux.service.config.FTPConfig;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * FTPFileProcessor单元测试
 */
@ExtendWith(MockitoExtension.class)
class FTPFileProcessorTest {

    @Mock
    private GenericObjectPool<FTPClient> ftpClientPool;

    @Mock
    private FTPConfig.FTPProperties ftpProperties;

    @Mock
    private FTPClient ftpClient;

    @InjectMocks
    private FTPFileProcessor ftpFileProcessor;

    @BeforeEach
    void setUp() {
        // 设置FTP属性默认值
        when(ftpProperties.getRetryAttempts()).thenReturn(3);
        when(ftpProperties.getRetryInterval()).thenReturn(1000L);
        
        FTPConfig.FTPProperties.PoolConfig poolConfig = new FTPConfig.FTPProperties.PoolConfig();
        poolConfig.setMaxWaitMillis(30000L);
        when(ftpProperties.getPool()).thenReturn(poolConfig);
    }

    @Test
    void testDownloadFile_Success() throws Exception {
        // 准备测试数据
        String ftpUrl = "******************************/path/test.txt";
        
        // Mock FTP客户端行为
        when(ftpClientPool.borrowObject(anyLong())).thenReturn(ftpClient);
        when(ftpClient.retrieveFile(anyString(), any(FileOutputStream.class))).thenReturn(true);
        
        // 执行测试
        File result = ftpFileProcessor.downloadFile(ftpUrl);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.exists());
        assertTrue(result.getName().startsWith("ftp_download_"));
        
        // 验证方法调用
        verify(ftpClientPool).borrowObject(anyLong());
        verify(ftpClientPool).returnObject(ftpClient);
        verify(ftpClient).retrieveFile(anyString(), any(FileOutputStream.class));
        
        // 清理临时文件
        Files.deleteIfExists(result.toPath());
    }

    @Test
    void testDownloadFile_FTPFailure() throws Exception {
        // 准备测试数据
        String ftpUrl = "******************************/path/test.txt";
        
        // Mock FTP客户端行为 - 下载失败
        when(ftpClientPool.borrowObject(anyLong())).thenReturn(ftpClient);
        when(ftpClient.retrieveFile(anyString(), any(FileOutputStream.class))).thenReturn(false);
        when(ftpClient.getReplyString()).thenReturn("550 File not found");
        
        // 执行测试并验证异常
        IOException exception = assertThrows(IOException.class, () -> {
            ftpFileProcessor.downloadFile(ftpUrl);
        });
        
        assertTrue(exception.getMessage().contains("FTP文件下载失败"));
        
        // 验证方法调用
        verify(ftpClientPool).borrowObject(anyLong());
        verify(ftpClientPool).returnObject(ftpClient);
    }

    @Test
    void testDownloadFile_InvalidURL() {
        // 准备无效URL
        String invalidUrl = "invalid-url";
        
        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            ftpFileProcessor.downloadFile(invalidUrl);
        });
        
        assertTrue(exception.getMessage().contains("无效的FTP URL格式") || 
                  exception instanceof IllegalArgumentException);
    }

    @Test
    void testCalculateMD5_Success() throws Exception {
        // 创建临时测试文件
        File testFile = Files.createTempFile("test", ".txt").toFile();
        Files.write(testFile.toPath(), "Hello World".getBytes());
        
        try {
            // 执行测试
            String md5 = ftpFileProcessor.calculateMD5(testFile);
            
            // 验证结果
            assertNotNull(md5);
            assertEquals(32, md5.length()); // MD5长度应该是32位
            assertEquals("b10a8db164e0754105b7a99be72e3fe5", md5); // "Hello World"的MD5值
            
        } finally {
            // 清理临时文件
            Files.deleteIfExists(testFile.toPath());
        }
    }

    @Test
    void testVerifyMD5_Success() throws Exception {
        // 创建临时测试文件
        File testFile = Files.createTempFile("test", ".txt").toFile();
        Files.write(testFile.toPath(), "Hello World".getBytes());
        
        try {
            // 执行测试 - 正确的MD5
            boolean result = ftpFileProcessor.verifyMD5(testFile, "b10a8db164e0754105b7a99be72e3fe5");
            assertTrue(result);
            
            // 执行测试 - 错误的MD5
            boolean result2 = ftpFileProcessor.verifyMD5(testFile, "wrong_md5_value");
            assertFalse(result2);
            
            // 执行测试 - 空MD5（应该跳过验证）
            boolean result3 = ftpFileProcessor.verifyMD5(testFile, null);
            assertTrue(result3);
            
            boolean result4 = ftpFileProcessor.verifyMD5(testFile, "");
            assertTrue(result4);
            
        } finally {
            // 清理临时文件
            Files.deleteIfExists(testFile.toPath());
        }
    }

    @Test
    void testCleanupTempFile() throws Exception {
        // 创建临时测试文件
        File testFile = Files.createTempFile("test", ".txt").toFile();
        assertTrue(testFile.exists());
        
        // 执行清理
        ftpFileProcessor.cleanupTempFile(testFile);
        
        // 验证文件已被删除
        assertFalse(testFile.exists());
    }

    @Test
    void testCleanupTempFile_NullFile() {
        // 测试空文件清理（不应该抛出异常）
        assertDoesNotThrow(() -> {
            ftpFileProcessor.cleanupTempFile(null);
        });
    }

    @Test
    void testCleanupTempFile_NonExistentFile() {
        // 测试不存在的文件清理（不应该抛出异常）
        File nonExistentFile = new File("/non/existent/file.txt");
        assertDoesNotThrow(() -> {
            ftpFileProcessor.cleanupTempFile(nonExistentFile);
        });
    }

    @Test
    void testDownloadFile_PoolException() throws Exception {
        // 准备测试数据
        String ftpUrl = "******************************/path/test.txt";
        
        // Mock连接池异常
        when(ftpClientPool.borrowObject(anyLong())).thenThrow(new RuntimeException("Pool exhausted"));
        
        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            ftpFileProcessor.downloadFile(ftpUrl);
        });
        
        assertTrue(exception.getMessage().contains("Pool exhausted"));
    }

    @Test
    void testDownloadFile_WithRetry() throws Exception {
        // 准备测试数据
        String ftpUrl = "******************************/path/test.txt";
        
        // Mock FTP客户端行为 - 前两次失败，第三次成功
        when(ftpClientPool.borrowObject(anyLong())).thenReturn(ftpClient);
        when(ftpClient.retrieveFile(anyString(), any(FileOutputStream.class)))
                .thenReturn(false)  // 第一次失败
                .thenReturn(false)  // 第二次失败
                .thenReturn(true);  // 第三次成功
        when(ftpClient.getReplyString()).thenReturn("450 Temporary failure");
        
        // 执行测试
        File result = ftpFileProcessor.downloadFile(ftpUrl);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.exists());
        
        // 验证重试次数（应该调用3次retrieveFile）
        verify(ftpClient, times(3)).retrieveFile(anyString(), any(FileOutputStream.class));
        
        // 清理临时文件
        Files.deleteIfExists(result.toPath());
    }
}
