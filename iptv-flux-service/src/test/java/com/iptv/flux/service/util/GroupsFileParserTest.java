package com.iptv.flux.service.util;

import com.iptv.flux.common.dto.GroupInfoDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GroupsFileParser单元测试
 */
@ExtendWith(MockitoExtension.class)
class GroupsFileParserTest {

    @InjectMocks
    private GroupsFileParser groupsFileParser;

    private File testFile;

    @BeforeEach
    void setUp() throws IOException {
        // 创建临时测试文件
        testFile = Files.createTempFile("groups_test", ".txt").toFile();
    }

    @Test
    void testParseGroupsFile_Success() throws IOException {
        // 准备测试数据
        String testContent = """
                SysID|GroupID|GroupName|Platform|Description|FileURL|UserCovering|GenerateTime|sumkey|Strategys
                dx|CG001|VIP用户组|platform1|高价值用户群体|http://example.com/file1.txt|5000|20250814120000|abc123|ST001,ST002
                lt|CG002|普通用户组|platform2|普通用户群体|http://example.com/file2.txt|10000|20250814120000|def456|ST003
                yd|CG003|新用户组|platform3|新注册用户|http://example.com/file3.txt|2000|20250814120000|ghi789|ST004
                """;
        
        Files.write(testFile.toPath(), testContent.getBytes());

        // 收集解析结果
        List<GroupInfoDTO> allGroups = new ArrayList<>();
        AtomicInteger batchCount = new AtomicInteger(0);

        // 执行解析
        groupsFileParser.parseGroupsFile(testFile, batch -> {
            allGroups.addAll(batch);
            batchCount.incrementAndGet();
        });

        // 验证结果
        assertEquals(3, allGroups.size());
        assertTrue(batchCount.get() > 0);

        // 验证第一条记录
        GroupInfoDTO firstGroup = allGroups.get(0);
        assertEquals("dx", firstGroup.getSource());
        assertEquals("CG001", firstGroup.getGroupId());
        assertEquals("VIP用户组", firstGroup.getGroupName());
        assertEquals("platform1", firstGroup.getPlatform());
        assertEquals("高价值用户群体", firstGroup.getDescription());
        assertEquals("http://example.com/file1.txt", firstGroup.getFileUrl());
        assertEquals(5000L, firstGroup.getCoverage());
        assertEquals("abc123", firstGroup.getMd5sum());
        assertEquals("ST001", firstGroup.getStrategyId()); // 取第一个策略
        assertNotNull(firstGroup.getGenerateTime());
        assertTrue(firstGroup.getActive());

        // 验证第二条记录
        GroupInfoDTO secondGroup = allGroups.get(1);
        assertEquals("lt", secondGroup.getSource());
        assertEquals("CG002", secondGroup.getGroupId());
        assertEquals("普通用户组", secondGroup.getGroupName());
    }

    @Test
    void testParseGroupsFile_WithHeader() throws IOException {
        // 准备带标题行的测试数据
        String testContent = """
                SysID|GroupID|GroupName|Platform|Description|FileURL|UserCovering|GenerateTime|sumkey|Strategys
                dx|CG001|VIP用户组|platform1|高价值用户群体|http://example.com/file1.txt|5000|20250814120000|abc123|ST001
                """;
        
        Files.write(testFile.toPath(), testContent.getBytes());

        // 收集解析结果
        List<GroupInfoDTO> allGroups = new ArrayList<>();

        // 执行解析
        groupsFileParser.parseGroupsFile(testFile, allGroups::addAll);

        // 验证结果 - 应该只有1条数据记录（标题行被跳过）
        assertEquals(1, allGroups.size());
        assertEquals("CG001", allGroups.get(0).getGroupId());
    }

    @Test
    void testParseGroupsFile_EmptyLines() throws IOException {
        // 准备包含空行的测试数据
        String testContent = """
                dx|CG001|VIP用户组|platform1|高价值用户群体|http://example.com/file1.txt|5000|20250814120000|abc123|ST001
                
                lt|CG002|普通用户组|platform2|普通用户群体|http://example.com/file2.txt|10000|20250814120000|def456|ST003
                
                """;
        
        Files.write(testFile.toPath(), testContent.getBytes());

        // 收集解析结果
        List<GroupInfoDTO> allGroups = new ArrayList<>();

        // 执行解析
        groupsFileParser.parseGroupsFile(testFile, allGroups::addAll);

        // 验证结果 - 空行应该被跳过
        assertEquals(2, allGroups.size());
    }

    @Test
    void testParseGroupsFile_InvalidData() throws IOException {
        // 准备包含无效数据的测试数据
        String testContent = """
                dx|CG001|VIP用户组|platform1|高价值用户群体|http://example.com/file1.txt|5000|20250814120000|abc123|ST001
                invalid_line_with_insufficient_fields
                lt|CG002|普通用户组|platform2|普通用户群体|http://example.com/file2.txt|10000|20250814120000|def456|ST003
                ||||||||||
                yd|CG003|新用户组|platform3|新注册用户|http://example.com/file3.txt|2000|20250814120000|ghi789|ST004
                """;
        
        Files.write(testFile.toPath(), testContent.getBytes());

        // 收集解析结果
        List<GroupInfoDTO> allGroups = new ArrayList<>();

        // 执行解析（应该跳过无效行）
        assertDoesNotThrow(() -> {
            groupsFileParser.parseGroupsFile(testFile, allGroups::addAll);
        });

        // 验证结果 - 应该只解析有效的3条记录
        assertEquals(3, allGroups.size());
        assertEquals("CG001", allGroups.get(0).getGroupId());
        assertEquals("CG002", allGroups.get(1).getGroupId());
        assertEquals("CG003", allGroups.get(2).getGroupId());
    }

    @Test
    void testParseGroupsFile_MissingOptionalFields() throws IOException {
        // 准备缺少可选字段的测试数据
        String testContent = """
                dx|CG001|VIP用户组|platform1|高价值用户群体|http://example.com/file1.txt||20250814120000|abc123
                """;
        
        Files.write(testFile.toPath(), testContent.getBytes());

        // 收集解析结果
        List<GroupInfoDTO> allGroups = new ArrayList<>();

        // 执行解析
        groupsFileParser.parseGroupsFile(testFile, allGroups::addAll);

        // 验证结果
        assertEquals(1, allGroups.size());
        GroupInfoDTO group = allGroups.get(0);
        assertEquals("CG001", group.getGroupId());
        assertNull(group.getCoverage()); // 空的UserCovering字段
        assertNull(group.getStrategyId()); // 缺少Strategys字段
    }

    @Test
    void testGetTotalLines() throws IOException {
        // 准备测试数据
        String testContent = """
                line1
                line2
                line3
                line4
                """;
        
        Files.write(testFile.toPath(), testContent.getBytes());

        // 执行测试
        int totalLines = groupsFileParser.getTotalLines(testFile);

        // 验证结果
        assertEquals(4, totalLines);
    }

    @Test
    void testIsValidGroupsFile_Valid() throws IOException {
        // 准备有效的测试数据
        String testContent = """
                SysID|GroupID|GroupName|Platform|Description|FileURL|UserCovering|GenerateTime|sumkey|Strategys
                dx|CG001|VIP用户组|platform1|高价值用户群体|http://example.com/file1.txt|5000|20250814120000|abc123|ST001
                """;
        
        Files.write(testFile.toPath(), testContent.getBytes());

        // 执行测试
        boolean isValid = groupsFileParser.isValidGroupsFile(testFile);

        // 验证结果
        assertTrue(isValid);
    }

    @Test
    void testIsValidGroupsFile_Invalid() throws IOException {
        // 准备无效的测试数据（字段不足）
        String testContent = """
                field1,field2,field3
                value1,value2,value3
                """;
        
        Files.write(testFile.toPath(), testContent.getBytes());

        // 执行测试
        boolean isValid = groupsFileParser.isValidGroupsFile(testFile);

        // 验证结果
        assertFalse(isValid);
    }

    @Test
    void testIsValidGroupsFile_EmptyFile() throws IOException {
        // 准备空文件
        Files.write(testFile.toPath(), "".getBytes());

        // 执行测试
        boolean isValid = groupsFileParser.isValidGroupsFile(testFile);

        // 验证结果
        assertFalse(isValid);
    }

    @Test
    void testIsValidGroupsFile_NullFile() {
        // 执行测试
        boolean isValid = groupsFileParser.isValidGroupsFile(null);

        // 验证结果
        assertFalse(isValid);
    }

    @Test
    void testParseGroupsFile_LargeBatch() throws IOException {
        // 准备大量测试数据（超过批次大小）
        StringBuilder contentBuilder = new StringBuilder();
        for (int i = 1; i <= 1500; i++) {
            contentBuilder.append(String.format("dx|CG%03d|用户组%d|platform1|描述%d|http://example.com/file%d.txt|%d|20250814120000|md5_%d|ST001%n", 
                    i, i, i, i, i * 100, i));
        }
        
        Files.write(testFile.toPath(), contentBuilder.toString().getBytes());

        // 收集解析结果
        List<GroupInfoDTO> allGroups = new ArrayList<>();
        AtomicInteger batchCount = new AtomicInteger(0);

        // 执行解析
        groupsFileParser.parseGroupsFile(testFile, batch -> {
            allGroups.addAll(batch);
            batchCount.incrementAndGet();
        });

        // 验证结果
        assertEquals(1500, allGroups.size());
        assertTrue(batchCount.get() > 1); // 应该分多个批次处理

        // 验证数据正确性
        assertEquals("CG001", allGroups.get(0).getGroupId());
        assertEquals("CG500", allGroups.get(499).getGroupId());
        assertEquals("CG1500", allGroups.get(1499).getGroupId());
    }

    @Test
    void testParseGroupsFile_FileNotFound() {
        // 准备不存在的文件
        File nonExistentFile = new File("/non/existent/file.txt");

        // 执行测试并验证异常
        IOException exception = assertThrows(IOException.class, () -> {
            groupsFileParser.parseGroupsFile(nonExistentFile, batch -> {});
        });

        assertNotNull(exception);
    }

    @Test
    void testParseGroupsFile_DateTimeFormat() throws IOException {
        // 准备包含不同时间格式的测试数据
        String testContent = """
                dx|CG001|VIP用户组|platform1|高价值用户群体|http://example.com/file1.txt|5000|20250814120000|abc123|ST001
                lt|CG002|普通用户组|platform2|普通用户群体|http://example.com/file2.txt|10000|invalid_date|def456|ST003
                yd|CG003|新用户组|platform3|新注册用户|http://example.com/file3.txt|2000||ghi789|ST004
                """;
        
        Files.write(testFile.toPath(), testContent.getBytes());

        // 收集解析结果
        List<GroupInfoDTO> allGroups = new ArrayList<>();

        // 执行解析
        groupsFileParser.parseGroupsFile(testFile, allGroups::addAll);

        // 验证结果
        assertEquals(3, allGroups.size());
        
        // 第一条记录应该有正确的时间
        assertNotNull(allGroups.get(0).getGenerateTime());
        
        // 第二条记录时间解析失败，应该为null
        assertNull(allGroups.get(1).getGenerateTime());
        
        // 第三条记录时间为空，应该为null
        assertNull(allGroups.get(2).getGenerateTime());
    }
}
