package com.iptv.flux.service.schedule;

import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.utils.CacheKeyBuilder;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.model.entity.UserGroupRelation;
import com.iptv.flux.service.repository.UserGroupRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBloomFilter;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 布隆过滤器同步器，确保布隆过滤器与数据库同步
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class BloomFilterSynchronizer {

    private final UserGroupRepository userGroupRepository;
    private final RedissonClient redissonClient;
    private final MetricsRegistryUtil metricsRegistry;

    @Value("${cache.bloom-filter.expected-insertions:10000000}")
    private long expectedInsertions;

    @Value("${cache.bloom-filter.false-probability:0.001}")
    private double falseProbability;

    @Value("${user-group.bloom-filter.sync.enabled:true}")
    private boolean syncEnabled;

    @Value("${user-group.bloom-filter.sync.batch-size:10000}")
    private int batchSize;

    /**
     * 定期同步布隆过滤器与数据库
     * 每周日凌晨2点执行
     */
    @Scheduled(cron = "${user-group.bloom-filter.sync.cron:0 0 2 ? * SUN}")
    public void synchronizeBloomFilter() {
        if (!syncEnabled) {
            log.info("流量平台-----> 布隆过滤器同步已禁用");
            return;
        }

        long startTime = System.currentTimeMillis();
        log.info("流量平台-----> 开始布隆过滤器同步");

        try {
            // 创建新的布隆过滤器实例
            String newFilterName = UserGroupConstants.BLOOM_FILTER_KEY + ":new";
            RBloomFilter<String> newBloomFilter = redissonClient.getBloomFilter(newFilterName);
            newBloomFilter.tryInit(expectedInsertions, falseProbability);

            // 分批从数据库加载所有用户ID
            int offset = 0;
            List<UserGroupRelation> batch;
            int total = 0;

            do {
                batch = userGroupRepository.findAllWithPagination(offset, batchSize);

                for (UserGroupRelation relation : batch) {
                    String userId = String.valueOf(relation.getUserId());
                    String source = relation.getSource();
                    String compositeKey = CacheKeyBuilder.buildUserGroupCompositeKey(source, userId);
                    newBloomFilter.add(compositeKey);
                }

                total += batch.size();
                offset += batchSize;
                log.info("Processed {} records for bloom filter sync", total);
            } while (!batch.isEmpty());

            // 原子性地替换旧的布隆过滤器
            String oldFilterName = UserGroupConstants.BLOOM_FILTER_KEY;
            String tempFilterName = UserGroupConstants.BLOOM_FILTER_KEY + ":old";

            try {
                redissonClient.getKeys().rename(oldFilterName, tempFilterName);
                redissonClient.getKeys().rename(newFilterName, oldFilterName);
                redissonClient.getKeys().delete(tempFilterName);

                log.info("Bloom filter synchronization completed. Total keys: {}, took {}ms",
                        total, System.currentTimeMillis() - startTime);

                metricsRegistry.createCounter("bloom.filter.sync.success").increment();
            } catch (Exception e) {
                log.error("Failed to rename bloom filter keys", e);
                metricsRegistry.createCounter("bloom.filter.sync.rename.error").increment();
            }
        } catch (Exception e) {
            log.error("Error during bloom filter synchronization", e);
            metricsRegistry.createCounter("bloom.filter.sync.error").increment();
        }
    }
}