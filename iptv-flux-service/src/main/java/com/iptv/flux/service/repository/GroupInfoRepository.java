package com.iptv.flux.service.repository;

import com.iptv.flux.common.dto.GroupInfoDTO;
import com.iptv.flux.common.dto.GroupQueryRequestDTO;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.model.entity.GroupInfo;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.table;

/**
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class GroupInfoRepository {

    private final DSLContext dsl;
    private final MetricsRegistryUtil metricsRegistry;
    private final UserGroupRepository userGroupRepository;
    private static final Table<?> GROUP_INFO_TABLE = table("group_info");
    private static final Field<String> GROUP_ID = field("group_id", String.class);
    private static final Field<String> STRATEGY_ID = field("strategy_id", String.class);
    private static final Field<String> PLATFORM = field("platform", String.class);
    private static final Field<String> SOURCE = field("source", String.class);
    private static final Field<Boolean> ACTIVE = field("active", Boolean.class);
    private static final Field<String> FILE_URL = field("file_url", String.class);
    private static final Field<Long> COVERAGE = field("coverage", Long.class);
    private static final Field<java.time.LocalDateTime> GENERATE_TIME = field("generate_time", java.time.LocalDateTime.class);
    private static final Field<String> MD5SUM = field("md5sum", String.class);
    private static final Field<String> GROUP_NAME = field("group_name", String.class);
    private static final Field<String> DESCRIPTION = field("description", String.class);

    @Timed(value = "repository.group.findById", percentiles = {0.5, 0.95, 0.99})
    public GroupInfo findByGroupId(String groupId) {
        long startTime = System.currentTimeMillis();

        try {
            Record record = dsl.select()
                    .from(GROUP_INFO_TABLE)
                    .where(GROUP_ID.eq(groupId))
                    .fetchOne();

            if (record == null) {
                log.debug("找不到带有groupId的组: {}", groupId);
                return null;
            }

            log.debug("找到群组，群组ID：{}，耗时{}ms", groupId, System.currentTimeMillis() - startTime);

            return GroupInfo.builder()
                    .id(record.get(field("id", Long.class)))
                    .groupId(record.get(GROUP_ID))
                    .strategyId(record.get(STRATEGY_ID))
                    .groupName(record.get(GROUP_NAME))
                    .description(record.get(DESCRIPTION))
                    .platform(record.get(PLATFORM))
                    .source(record.get(SOURCE))
                    .active(record.get(ACTIVE))
                    .fileUrl(record.get(FILE_URL))
                    .coverage(record.get(COVERAGE))
                    .generateTime(record.get(GENERATE_TIME))
                    .md5sum(record.get(MD5SUM))
                    .build();
        } catch (Exception e) {
            log.error("根据groupId查找组时出错: {}", groupId, e);
            throw e;
        }
    }

    @Timed(value = "repository.group.findByIds", percentiles = {0.5, 0.95, 0.99})
    public Map<String, GroupInfoDTO> findGroupsByIds(List<String> groupIds) {
        if (groupIds == null || groupIds.isEmpty()) {
            return Collections.emptyMap();
        }

        long startTime = System.currentTimeMillis();

        try {
            Map<String, GroupInfoDTO> result = dsl.select(GROUP_ID, STRATEGY_ID, GROUP_NAME, DESCRIPTION, PLATFORM, SOURCE, ACTIVE, FILE_URL, COVERAGE, GENERATE_TIME, MD5SUM)
                    .from(GROUP_INFO_TABLE)
                    .where(GROUP_ID.in(groupIds))
                    .fetch()
                    .stream()
                    .map(r -> GroupInfoDTO.builder()
                            .groupId(r.get(GROUP_ID))
                            .strategyId(r.get(STRATEGY_ID))
                            .groupName(r.get(GROUP_NAME))
                            .description(r.get(DESCRIPTION))
                            .platform(r.get(PLATFORM))
                            .source(r.get(SOURCE))
                            .active(r.get(ACTIVE))
                            .fileUrl(r.get(FILE_URL))
                            .coverage(r.get(COVERAGE))
                            .generateTime(r.get(GENERATE_TIME))
                            .md5sum(r.get(MD5SUM))
                            .build())
                    .collect(Collectors.toMap(
                            GroupInfoDTO::getGroupId,
                            Function.identity()
                    ));

            log.debug("找到了 {} 个群组，共请求了 {} 个，耗时 {}ms",
                    result.size(), groupIds.size(), System.currentTimeMillis() - startTime);

            return result;
        } catch (Exception e) {
            log.error("按ID查找组时出错，数量: {}", groupIds.size(), e);
            metricsRegistry.incrementCounter("repository.error", "method", "findGroupsByIds");
            throw e;
        }
    }

    @Timed(value = "repository.group.save", percentiles = {0.5, 0.95, 0.99})
    public void saveGroupInfo(GroupInfo groupInfo) {
        long startTime = System.currentTimeMillis();

        try {
            int count = dsl.insertInto(GROUP_INFO_TABLE)
                    .set(GROUP_ID, groupInfo.getGroupId())
                    .set(STRATEGY_ID, groupInfo.getStrategyId())
                    .set(GROUP_NAME, groupInfo.getGroupName())
                    .set(DESCRIPTION, groupInfo.getDescription())
                    .set(PLATFORM, groupInfo.getPlatform())
                    .set(SOURCE, groupInfo.getSource())
                    .set(ACTIVE, groupInfo.getActive())
                    .set(FILE_URL, groupInfo.getFileUrl())
                    .set(COVERAGE, groupInfo.getCoverage())
                    .set(GENERATE_TIME, groupInfo.getGenerateTime())
                    .set(MD5SUM, groupInfo.getMd5sum())
                    .onDuplicateKeyUpdate()
                    .set(STRATEGY_ID, groupInfo.getStrategyId())
                    .set(GROUP_NAME, groupInfo.getGroupName())
                    .set(DESCRIPTION, groupInfo.getDescription())
                    .set(PLATFORM, groupInfo.getPlatform())
                    .set(SOURCE, groupInfo.getSource())
                    .set(ACTIVE, groupInfo.getActive())
                    .set(FILE_URL, groupInfo.getFileUrl())
                    .set(COVERAGE, groupInfo.getCoverage())
                    .set(GENERATE_TIME, groupInfo.getGenerateTime())
                    .set(MD5SUM, groupInfo.getMd5sum())
                    .execute();

            log.debug("保存了 groupId: {} 的群组信息，受影响的行数: {}，耗时 {}ms",
                    groupInfo.getGroupId(), count, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("保存groupId的组信息时出错: {}", groupInfo.getGroupId(), e);
            metricsRegistry.incrementCounter("repository.error", "method", "saveGroupInfo");
            throw e;
        }
    }



    /**
     * 根据条件查询分组信息
     *
     * @param request 查询条件
     * @return 分组信息列表
     */
    @Timed(value = "repository.group.queryByConditions", percentiles = {0.5, 0.95, 0.99})
    public List<GroupInfoDTO> queryGroupsByConditions(GroupQueryRequestDTO request) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始根据条件查询分组");

        try {
            // 构建查询条件
            Condition condition = buildQueryCondition(request);

            // 执行查询
            Result<Record> records = dsl.select()
                    .from(GROUP_INFO_TABLE)
                    .where(condition)
                    .orderBy(GROUP_NAME.asc()) // 按分组名称排序
                    .fetch();

            // 转换为DTO列表
            List<GroupInfoDTO> result = new ArrayList<>();
            for (Record record : records) {
                GroupInfoDTO dto = convertRecordToDTO(record);
                result.add(dto);
            }

            long duration = System.currentTimeMillis() - startTime;
            log.debug("流量平台-----> 条件查询分组完成，结果数量: {}, 耗时: {}ms", result.size(), duration);

            return result;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("流量平台-----> 条件查询分组失败，耗时: {}ms", duration, e);
            throw e;
        }
    }

    /**
     * 构建查询条件
     */
    private Condition buildQueryCondition(GroupQueryRequestDTO request) {
        Condition condition = DSL.trueCondition(); // 默认为 true，即无条件

        // 分组名称条件（模糊查询）- 限制长度防止性能问题
        if (request.getTrimmedGroupName() != null && !request.getTrimmedGroupName().isEmpty()) {
            String groupName = request.getTrimmedGroupName();
            // 安全检查：限制查询字符串长度，防止性能攻击
            if (groupName.length() > 100) {
                throw new IllegalArgumentException("分组名称查询条件过长，最大允许100个字符");
            }
            // 使用参数化查询防止注入
            condition = condition.and(GROUP_NAME.like(DSL.concat(DSL.val("%"), DSL.val(groupName), DSL.val("%"))));
            log.debug("流量平台-----> 添加分组名称查询条件: {}", groupName);
        }

        // 策略ID条件（精确查询）
        if (request.getTrimmedStrategyId() != null && !request.getTrimmedStrategyId().isEmpty()) {
            condition = condition.and(STRATEGY_ID.eq(request.getTrimmedStrategyId()));
            log.debug("流量平台-----> 添加策略ID查询条件: {}", request.getTrimmedStrategyId());
        }

        // 平台条件
        if (request.getTrimmedPlatform() != null && !request.getTrimmedPlatform().isEmpty()) {
            condition = condition.and(PLATFORM.eq(request.getTrimmedPlatform()));
            log.debug("流量平台-----> 添加平台查询条件: {}", request.getTrimmedPlatform());
        }

        // 来源条件
        if (request.getTrimmedSource() != null && !request.getTrimmedSource().isEmpty()) {
            condition = condition.and(SOURCE.eq(request.getTrimmedSource()));
            log.debug("流量平台-----> 添加来源查询条件: {}", request.getTrimmedSource());
        }

        // 激活状态条件
        if (request.getActive() != null) {
            condition = condition.and(ACTIVE.eq(request.getActive()));
            log.debug("流量平台-----> 添加激活状态查询条件: {}", request.getActive());
        }

        // 覆盖度范围条件
        if (request.getMinCoverage() != null) {
            condition = condition.and(COVERAGE.greaterOrEqual(request.getMinCoverage()));
            log.debug("流量平台-----> 添加最小覆盖度查询条件: {}", request.getMinCoverage());
        }
        if (request.getMaxCoverage() != null) {
            condition = condition.and(COVERAGE.lessOrEqual(request.getMaxCoverage()));
            log.debug("流量平台-----> 添加最大覆盖度查询条件: {}", request.getMaxCoverage());
        }

        // 生成时间范围条件
        if (request.getGenerateTimeStart() != null) {
            condition = condition.and(GENERATE_TIME.greaterOrEqual(request.getGenerateTimeStart()));
            log.debug("流量平台-----> 添加生成时间开始查询条件: {}", request.getGenerateTimeStart());
        }
        if (request.getGenerateTimeEnd() != null) {
            condition = condition.and(GENERATE_TIME.lessOrEqual(request.getGenerateTimeEnd()));
            log.debug("流量平台-----> 添加生成时间结束查询条件: {}", request.getGenerateTimeEnd());
        }

        return condition;
    }

    /**
     * 将数据库记录转换为DTO
     */
    private GroupInfoDTO convertRecordToDTO(Record record) {
        return GroupInfoDTO.builder()
                .groupId(record.get(GROUP_ID))
                .strategyId(record.get(STRATEGY_ID))
                .groupName(record.get(GROUP_NAME))
                .description(record.get(DESCRIPTION))
                .platform(record.get(PLATFORM))
                .source(record.get(SOURCE))
                .active(record.get(ACTIVE))
                .fileUrl(record.get(FILE_URL))
                .coverage(record.get(COVERAGE))
                .generateTime(record.get(GENERATE_TIME))
                .md5sum(record.get(MD5SUM))
                .build();
    }

    /**
     * 根据条件统计分组数量
     */
    public long countGroupsByConditions(GroupQueryRequestDTO request) {
        try {
            Condition condition = buildQueryCondition(request);

            return dsl.selectCount()
                    .from(GROUP_INFO_TABLE)
                    .where(condition)
                    .fetchOne(0, Long.class);

        } catch (Exception e) {
            log.error("流量平台-----> 统计分组数量失败", e);
            return 0L;
        }
    }

    /**
     * 根据条件分页查询分组信息
     */
    public List<GroupInfoDTO> queryGroupsByConditionsWithPagination(GroupQueryRequestDTO request, int offset, int limit) {
        try {
            Condition condition = buildQueryCondition(request);

            return dsl.select(GROUP_ID, STRATEGY_ID, GROUP_NAME, DESCRIPTION, PLATFORM, SOURCE, ACTIVE, FILE_URL, COVERAGE, GENERATE_TIME, MD5SUM)
                    .from(GROUP_INFO_TABLE)
                    .where(condition)
                    .orderBy(GROUP_NAME.asc())
                    .limit(limit)
                    .offset(offset)
                    .fetch()
                    .map(this::convertRecordToDTO);

        } catch (Exception e) {
            log.error("流量平台-----> 分页查询分组失败", e);
            return Collections.emptyList();
        }
    }

}