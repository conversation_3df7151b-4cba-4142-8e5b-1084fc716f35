package com.iptv.flux.service.util;

import com.iptv.flux.common.dto.UserGroupDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.util
 * @className: UserGroupFileParser
 * @author: Claude 4.0 sonnet
 * @description: userGroup.txt文件解析器 - 解析用户分组关系文件
 * @date: 2025/8/14 12:00
 * @version: 1.0
 */
@Component
@Slf4j
public class UserGroupFileParser {

    private static final int BATCH_SIZE = 1000;

    /**
     * 流式解析userGroup.txt文件
     * 
     * 文件格式示例：
     * UserID|GroupList
     * user123|CG001,CG002,CG003
     * user456|CG001,CG004
     *
     * @param file 要解析的文件
     * @param source 来源标识（运营商：dx/lt/yd）
     * @param batchProcessor 批处理器，处理每批解析的数据
     * @throws IOException 文件读取异常
     */
    public void parseUserGroupFile(File file, String source, Consumer<List<UserGroupDTO>> batchProcessor) throws IOException {
        log.info("流量平台-----> 开始解析userGroup.txt文件: {}, 来源: {}, 大小: {} bytes", 
                file.getName(), source, file.length());
        
        long startTime = System.currentTimeMillis();
        int totalLines = 0;
        int processedLines = 0;
        int errorLines = 0;

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {

            String line;
            List<UserGroupDTO> batch = new ArrayList<>(BATCH_SIZE);
            boolean isFirstLine = true;

            while ((line = reader.readLine()) != null) {
                totalLines++;
                
                // 跳过空行
                if (line.trim().isEmpty()) {
                    continue;
                }

                // 跳过标题行（如果存在）
                if (isFirstLine && isHeaderLine(line)) {
                    isFirstLine = false;
                    log.debug("流量平台-----> 跳过标题行: {}", line);
                    continue;
                }
                isFirstLine = false;

                try {
                    UserGroupDTO userGroup = parseUserGroupLine(line, source);
                    if (userGroup != null) {
                        batch.add(userGroup);
                        processedLines++;

                        // 达到批次大小时处理
                        if (batch.size() >= BATCH_SIZE) {
                            processBatch(batch, batchProcessor, processedLines);
                            batch.clear();
                        }
                    }
                } catch (Exception e) {
                    errorLines++;
                    log.warn("流量平台-----> 解析用户分组行失败，行号: {}, 内容: {}, 错误: {}", 
                            totalLines, line, e.getMessage());
                }
            }

            // 处理最后一批数据
            if (!batch.isEmpty()) {
                processBatch(batch, batchProcessor, processedLines);
            }

            long duration = System.currentTimeMillis() - startTime;
            log.info("流量平台-----> userGroup.txt文件解析完成，总行数: {}, 成功: {}, 失败: {}, 耗时: {}ms",
                    totalLines, processedLines, errorLines, duration);

        } catch (IOException e) {
            log.error("流量平台-----> 读取userGroup.txt文件失败: {}", file.getName(), e);
            throw e;
        }
    }

    /**
     * 批量解析用户分组文件，返回用户ID到分组ID列表的映射
     * 
     * @param file 要解析的文件
     * @param source 来源标识
     * @return 用户ID到分组ID列表的映射
     * @throws IOException 文件读取异常
     */
    public Map<String, List<String>> parseToUserGroupMapping(File file, String source) throws IOException {
        log.info("流量平台-----> 开始批量解析userGroup.txt文件为映射: {}, 来源: {}", 
                file.getName(), source);
        
        Map<String, List<String>> userGroupMapping = new HashMap<>();
        long startTime = System.currentTimeMillis();
        int totalLines = 0;
        int processedLines = 0;

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {

            String line;
            boolean isFirstLine = true;

            while ((line = reader.readLine()) != null) {
                totalLines++;
                
                if (line.trim().isEmpty()) {
                    continue;
                }

                if (isFirstLine && isHeaderLine(line)) {
                    isFirstLine = false;
                    continue;
                }
                isFirstLine = false;

                try {
                    String[] parts = line.split("\\|", 2);
                    if (parts.length >= 2) {
                        String userId = parts[0].trim();
                        String groupList = parts[1].trim();
                        
                        if (!userId.isEmpty() && !groupList.isEmpty()) {
                            List<String> groupIds = Arrays.asList(groupList.split(","));
                            // 清理分组ID，去除空白字符
                            groupIds = groupIds.stream()
                                    .map(String::trim)
                                    .filter(id -> !id.isEmpty())
                                    .toList();
                            
                            if (!groupIds.isEmpty()) {
                                userGroupMapping.put(userId, groupIds);
                                processedLines++;
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("流量平台-----> 解析用户分组映射行失败，行号: {}, 内容: {}", totalLines, line);
                }
            }

            long duration = System.currentTimeMillis() - startTime;
            log.info("流量平台-----> userGroup.txt映射解析完成，总行数: {}, 成功: {}, 用户数: {}, 耗时: {}ms",
                    totalLines, processedLines, userGroupMapping.size(), duration);

            return userGroupMapping;

        } catch (IOException e) {
            log.error("流量平台-----> 批量解析userGroup.txt文件失败: {}", file.getName(), e);
            throw e;
        }
    }

    /**
     * 获取文件总行数（快速统计）
     */
    public int getTotalLines(File file) throws IOException {
        int lines = 0;
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {
            while (reader.readLine() != null) {
                lines++;
            }
        }
        log.debug("流量平台-----> userGroup.txt文件总行数: {}", lines);
        return lines;
    }

    /**
     * 解析单行用户分组信息
     * 
     * 格式：UserID|GroupList（如：user123|CG001,CG002,CG003）
     */
    private UserGroupDTO parseUserGroupLine(String line, String source) {
        if (line == null || line.trim().isEmpty()) {
            return null;
        }

        String[] parts = line.split("\\|", 2);
        if (parts.length < 2) {
            throw new IllegalArgumentException("用户分组信息格式错误，期望格式：UserID|GroupList");
        }

        String userId = parts[0].trim();
        String groupList = parts[1].trim();

        if (userId.isEmpty()) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        if (groupList.isEmpty()) {
            throw new IllegalArgumentException("分组列表不能为空");
        }

        // 解析分组ID列表
        String[] groupArray = groupList.split(",");
        Set<String> groupIds = new LinkedHashSet<>();
        
        for (String groupId : groupArray) {
            String trimmedGroupId = groupId.trim();
            if (!trimmedGroupId.isEmpty()) {
                groupIds.add(trimmedGroupId);
            }
        }

        if (groupIds.isEmpty()) {
            throw new IllegalArgumentException("有效分组ID不能为空");
        }

        return UserGroupDTO.builder()
                .userId(userId)
                .source(source)
                .groupIds(groupIds)
                .lastSeenAt(LocalDateTime.now()) // 设置为当前处理时间
                .build();
    }

    /**
     * 判断是否为标题行
     */
    private boolean isHeaderLine(String line) {
        return line.toLowerCase().contains("userid") && 
               line.toLowerCase().contains("grouplist");
    }

    /**
     * 处理批次数据
     */
    private void processBatch(List<UserGroupDTO> batch, Consumer<List<UserGroupDTO>> processor, int processedCount) {
        if (batch.isEmpty()) {
            return;
        }

        log.debug("流量平台-----> 处理用户分组批次，数量: {}, 累计处理: {}", batch.size(), processedCount);
        
        try {
            processor.accept(new ArrayList<>(batch));
        } catch (Exception e) {
            log.error("流量平台-----> 处理用户分组批次失败", e);
            throw e;
        }
    }

    /**
     * 验证文件格式
     */
    public boolean isValidUserGroupFile(File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            return false;
        }

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {
            
            String firstLine = reader.readLine();
            if (firstLine == null) {
                return false;
            }

            // 检查是否包含必要的分隔符
            return firstLine.contains("|") && firstLine.split("\\|").length >= 2;

        } catch (IOException e) {
            log.warn("流量平台-----> 验证userGroup.txt文件格式时发生异常", e);
            return false;
        }
    }

    /**
     * 解析结果统计
     */
    public static class ParseResult {
        private final int totalLines;
        private final int processedLines;
        private final int errorLines;
        private final int uniqueUsers;
        private final long durationMs;

        public ParseResult(int totalLines, int processedLines, int errorLines, int uniqueUsers, long durationMs) {
            this.totalLines = totalLines;
            this.processedLines = processedLines;
            this.errorLines = errorLines;
            this.uniqueUsers = uniqueUsers;
            this.durationMs = durationMs;
        }

        public int getTotalLines() { return totalLines; }
        public int getProcessedLines() { return processedLines; }
        public int getErrorLines() { return errorLines; }
        public int getUniqueUsers() { return uniqueUsers; }
        public long getDurationMs() { return durationMs; }
        public boolean isSuccess() { return errorLines == 0; }
        public double getSuccessRate() { 
            return totalLines > 0 ? (double) processedLines / totalLines * 100 : 0; 
        }
    }
}
