package com.iptv.flux.service.exception;

import com.iptv.flux.common.dto.ResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.apache.tomcat.util.http.fileupload.impl.FileSizeLimitExceededException;
import org.apache.tomcat.util.http.fileupload.impl.SizeLimitExceededException;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.exception
 * @className: GlobalExceptionHandler
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 13:01
 * @version: 1.0
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResultDTO<String> handleException(Exception ex) {
        log.error("未处理的异常发生", ex);
        return ResultDTO.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Internal server error");
    }

    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResultDTO<String> handleIllegalArgumentException(IllegalArgumentException ex) {
        log.warn("无效参数: {}", ex.getMessage());
        return ResultDTO.fail(HttpStatus.BAD_REQUEST.value(), ex.getMessage());
    }

    @ExceptionHandler(NullPointerException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResultDTO<String> handleNullPointerException(NullPointerException ex) {
        log.error("空指针异常", ex);
        return ResultDTO.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "数据处理异常，请检查输入数据格式");
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResultDTO<Map<String, String>> handleValidationExceptions(MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getFieldErrors().forEach(error -> {
            errors.put(error.getField(), error.getDefaultMessage());
        });

        String errorMessage = errors.entrySet().stream()
                .map(entry -> entry.getKey() + ": " + entry.getValue())
                .collect(Collectors.joining(", "));

        log.warn("验证失败: {}", errorMessage);

        return ResultDTO.fail(HttpStatus.BAD_REQUEST.value(), "Validation failed");
    }

    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResultDTO<Map<String, String>> handleBindException(BindException ex) {
        Map<String, String> errors = new HashMap<>();
        for (FieldError error : ex.getBindingResult().getFieldErrors()) {
            errors.put(error.getField(), error.getDefaultMessage());
        }

        String errorMessage = errors.entrySet().stream()
                .map(entry -> entry.getKey() + ": " + entry.getValue())
                .collect(Collectors.joining(", "));

        log.warn("绑定失败: {}", errorMessage);

        return ResultDTO.fail(HttpStatus.BAD_REQUEST.value(), "Binding failed");
    }

    // 处理客户端中断连接的异常，避免污染日志
    @ExceptionHandler(ClientAbortException.class)
    @ResponseStatus(HttpStatus.OK)
    public void handleClientAbortException(ClientAbortException ex) {
        log.debug("客户端中断连接: {}", ex.getMessage());
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResultDTO<String> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException ex) {
        log.warn("文件上传大小超限: {}", ex.getMessage());

        // 提取具体的大小限制信息
        String message = "文件大小超过限制，请选择小于200MB的文件";
        if (ex.getCause() instanceof FileSizeLimitExceededException) {
            FileSizeLimitExceededException cause = (FileSizeLimitExceededException) ex.getCause();
            long maxSize = cause.getPermittedSize();
            message = String.format("单个文件大小超过限制，最大允许: %dMB", maxSize / (1024 * 1024));
        } else if (ex.getCause() instanceof SizeLimitExceededException) {
            SizeLimitExceededException cause = (SizeLimitExceededException) ex.getCause();
            long maxSize = cause.getPermittedSize();
            message = String.format("请求总大小超过限制，最大允许: %dMB", maxSize / (1024 * 1024));
        }

        return ResultDTO.fail(HttpStatus.BAD_REQUEST.value(), message);
    }

    /**
     * 处理Tomcat文件大小限制异常
     */
    @ExceptionHandler(FileSizeLimitExceededException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResultDTO<String> handleFileSizeLimitExceededException(FileSizeLimitExceededException ex) {
        log.warn("Tomcat文件大小限制异常: {}", ex.getMessage());
        long maxSizeMB = ex.getPermittedSize() / (1024 * 1024);
        String message = String.format("文件大小超过限制，最大允许: %dMB，请选择更小的文件", maxSizeMB);
        return ResultDTO.fail(HttpStatus.BAD_REQUEST.value(), message);
    }

    /**
     * 处理请求总大小限制异常
     */
    @ExceptionHandler(SizeLimitExceededException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResultDTO<String> handleSizeLimitExceededException(SizeLimitExceededException ex) {
        log.warn("请求总大小限制异常: {}", ex.getMessage());
        long maxSizeMB = ex.getPermittedSize() / (1024 * 1024);
        String message = String.format("请求总大小超过限制，最大允许: %dMB", maxSizeMB);
        return ResultDTO.fail(HttpStatus.BAD_REQUEST.value(), message);
    }
}