package com.iptv.flux.service.service.progress;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.service.progress
 * @className: ProgressStatus
 * @author: chiron
 * @description: 进度状态枚举 - 清晰定义所有可能的状态
 * @date: 2025-06-13
 * @version: 2.0
 */
public enum ProgressStatus {
    
    /**
     * 等待处理
     */
    PENDING("等待处理", 0.0),

    /**
     * 正在解析文件
     */
    PARSING("正在解析文件", 0.0),

    /**
     * 正在处理数据
     */
    PROCESSING("正在处理数据", 0.0),

    /**
     * 正在导入数据
     */
    IMPORTING("正在导入数据", 0.0),

    /**
     * 处理完成
     */
    COMPLETED("处理完成", 100.0),

    /**
     * 处理失败
     */
    FAILED("处理失败", 0.0);

    private final String description;
    private final double basePercentage;

    ProgressStatus(String description, double basePercentage) {
        this.description = description;
        this.basePercentage = basePercentage;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 获取英文状态码（用于前端统一处理）
     */
    public String getStatusCode() {
        return this.name();
    }

    public double getBasePercentage() {
        return basePercentage;
    }

    /**
     * 计算实际进度比例 - 返回0-1之间的小数（前端统一格式）
     * @param processed 已处理数量
     * @param total 总数量
     * @return 实际进度比例（0-1之间的小数）
     */
    public double calculateActualPercentage(int processed, int total) {
        // 处理完成，直接返回1.0（表示100%）
        if (this == COMPLETED) {
            return 1.0;
        }

        // 失败或等待状态，返回0.0
        if (this == FAILED || this == PENDING) {
            return 0.0;
        }

        // 无效的总数，返回0.0
        if (total <= 0) {
            return 0.0;
        }

        // 简化计算：直接基于实际处理进度，返回0-1之间的小数
        double progressRatio = Math.min(1.0, (double) processed / total);

        // 确保进度在合理范围内（0-1之间）
        return Math.max(0.0, Math.min(1.0, progressRatio));
    }
}
