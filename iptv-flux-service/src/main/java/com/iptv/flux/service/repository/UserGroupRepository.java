package com.iptv.flux.service.repository;

import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.model.entity.UserGroupRelation;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.table;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.repository
 * @className: UserGroupRepository
 * @author: chiron
 * @description: TODO
 * @date: 2025/3/4 12:59
 * @version: 1.0
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class UserGroupRepository {

    private final DSLContext dsl;
    private final MetricsRegistryUtil metricsRegistry;

    private static final Table<?> USER_GROUP_TABLE = table("user_group_relation");
    private static final Field<String> USER_ID = field("user_id", String.class);
    private static final Field<String> SOURCE = field("source", String.class);
    private static final Field<String> GROUP_IDS = field("group_ids", String.class);
    private static final Field<String> PLATFORM = field("platform", String.class);
    private static final Field<LocalDateTime> EFFECTIVE_FROM = field("effective_from", LocalDateTime.class);
    private static final Field<LocalDateTime> LAST_SEEN_AT = field("last_seen_at", LocalDateTime.class);
    private static final Field<String> DESCRIPTION = field("description", String.class);

    /**
     * 根据用户ID和来源查询用户分组
     * @param userId
     * @param source
     * @return
     */
    @Timed(value = "repository.usergroup.queryGroups", percentiles = {0.5, 0.95, 0.99})
    public Set<String> queryGroupsByUserId(String userId, String source) { // 修改: 参数类型从Long改为String
        long startTime = System.currentTimeMillis();

        try {
            Record record = dsl.select(GROUP_IDS)
                    .from(USER_GROUP_TABLE)
                    .where(USER_ID.eq(userId))
                    .and(SOURCE.eq(source))
                    .fetchOne();

            if (record != null) {
                String groupIdsStr = record.get(GROUP_IDS);
                if (groupIdsStr != null && !groupIdsStr.isEmpty()) {
                    Set<String> groups = new LinkedHashSet<>(Arrays.asList(groupIdsStr.split(",")));
                    log.debug("流量平台-----> 为用户ID: {}, 来源: {} 找到 {} 个分组，耗时 {}ms",
                            userId, source, groups.size(), System.currentTimeMillis() - startTime);
                    return groups;
                }
            }

            log.debug("流量平台-----> 用户ID: {}, 来源: {} 没有找到分组，耗时 {}ms",
                    userId, source, System.currentTimeMillis() - startTime);

            return Collections.emptySet();
        } catch (Exception e) {
            log.error("流量平台-----> 查询用户ID: {}, 来源: {} 的分组失败", userId, source, e);
            metricsRegistry.incrementCounter("repository.error", "method", "queryGroupsByUserId");
            return Collections.emptySet();
        }
    }

    /**
     * 根据用户ID和来源查询用户分组关系
     * @param userId
     * @param source
     * @return
     */
    @Timed(value = "repository.usergroup.findByUserIdAndSource", percentiles = {0.5, 0.95, 0.99})
    public UserGroupRelation findByUserIdAndSource(String userId, String source) {
        long startTime = System.currentTimeMillis();

        try {
            Record record = dsl.select()
                    .from(USER_GROUP_TABLE)
                    .where(USER_ID.eq(userId))
                    .and(SOURCE.eq(source))
                    .fetchOne();

            if (record == null) {
                log.debug("流量平台-----> 没有找到用户ID: {}, 来源: {} 的用户分组关系", userId, source);
                return null;
            }

            log.debug("流量平台-----> 找到用户ID: {}, 来源: {} 的用户分组关系，耗时 {}ms",
                    userId, source, System.currentTimeMillis() - startTime);

            return UserGroupRelation.builder()
                    .id(record.get(field("id", Long.class)))
                    .userId(record.get(USER_ID))
                    .source(record.get(SOURCE))
                    .groupIds(record.get(GROUP_IDS))
                    .build();
        } catch (Exception e) {
            log.error("流量平台-----> 查找用户ID: {}, A来源: {} 的用户分组关系失败", userId, source, e);
            metricsRegistry.incrementCounter("repository.error", "method", "findByUserIdAndSource");
            throw e;
        }
    }

    /**
     * 保存用户分组关系
     * @param relation
     */
    @Timed(value = "repository.usergroup.save", percentiles = {0.5, 0.95, 0.99})
    public void saveUserGroupRelation(UserGroupRelation relation) {
        long startTime = System.currentTimeMillis();

        try {
            int count = dsl.insertInto(USER_GROUP_TABLE)
                    .set(USER_ID, relation.getUserId())
                    .set(SOURCE, relation.getSource())
                    .set(GROUP_IDS, relation.getGroupIds())
                    .set(PLATFORM, relation.getPlatform())
                    .set(EFFECTIVE_FROM, relation.getEffectiveFrom())
                    .set(LAST_SEEN_AT, relation.getLastSeenAt())
                    .set(DESCRIPTION, relation.getDescription())
                    .onDuplicateKeyUpdate()
                    .set(GROUP_IDS, relation.getGroupIds())
                    .set(PLATFORM, relation.getPlatform())
                    .set(EFFECTIVE_FROM, relation.getEffectiveFrom())
                    .set(LAST_SEEN_AT, relation.getLastSeenAt())
                    .set(DESCRIPTION, relation.getDescription())
                    .execute();

            log.debug("流量平台-----> 保存用户ID: {}, 来源: {} 的用户分组关系，影响行数: {}, 耗时 {}ms",
                    relation.getUserId(), relation.getSource(), count, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("流量平台-----> 保存用户ID: {}, 来源: {} 的用户分组关系失败",
                    relation.getUserId(), relation.getSource(), e);
            metricsRegistry.incrementCounter(UserGroupConstants.REPOSITORY_ERROR_METRIC, "method", "saveUserGroupRelation");
            throw e;
        }
    }

    /**
     * 分页查询所有用户分组关系
     */
    @Timed(value = "repository.usergroup.findAllWithPagination", percentiles = {0.5, 0.95, 0.99})
    public List<UserGroupRelation> findAllWithPagination(int offset, int limit) {
        long startTime = System.currentTimeMillis();

        try {
            List<UserGroupRelation> result = dsl.select()
                    .from(USER_GROUP_TABLE)
                    .orderBy(field("id", Long.class).asc())
                    .limit(limit)
                    .offset(offset)
                    .fetch()
                    .map(r -> UserGroupRelation.builder()
                            .id(r.get(field("id", Long.class)))
                            .userId(r.get(USER_ID))
                            .source(r.get(SOURCE))
                            .groupIds(r.get(GROUP_IDS))
                            .platform(r.get(PLATFORM))
                            .effectiveFrom(r.get(EFFECTIVE_FROM))
                            .lastSeenAt(r.get(LAST_SEEN_AT))
                            .description(r.get(DESCRIPTION))
                            .createdAt(r.get(field("created_at", Timestamp.class)) != null ?
                                    r.get(field("created_at", Timestamp.class)).toLocalDateTime() : null)
                            .updatedAt(r.get(field("updated_at", Timestamp.class)) != null ?
                                    r.get(field("updated_at", Timestamp.class)).toLocalDateTime() : null)
                            .build());

            log.debug("流量平台-----> 使用分页(偏移量: {}, 限制: {})找到 {} 个用户分组关系，耗时 {}ms",
                    offset, limit, result.size(), System.currentTimeMillis() - startTime);

            return result;
        } catch (Exception e) {
            log.error("流量平台-----> 使用分页查询用户分组关系失败", e);
            metricsRegistry.incrementCounter(UserGroupConstants.REPOSITORY_ERROR_METRIC, "method", "findAllWithPagination");
            return Collections.emptyList();
        }
    }

    /**
     * 查询最近更新的用户分组关系
     */
    @Timed(value = "repository.usergroup.findRecentUpdates", percentiles = {0.5, 0.95, 0.99})
    public List<UserGroupRelation> findRecentUpdates(LocalDateTime since, int limit) {
        long startTime = System.currentTimeMillis();

        try {
            Timestamp sinceTimestamp = Timestamp.valueOf(since);
            List<UserGroupRelation> result = dsl.select()
                    .from(USER_GROUP_TABLE)
                    .where(field("updated_at", Timestamp.class).greaterOrEqual(sinceTimestamp))
                    .orderBy(field("updated_at", Timestamp.class).desc())
                    .limit(limit)
                    .fetch()
                    .map(r -> UserGroupRelation.builder()
                            .id(r.get(field("id", Long.class)))
                            .userId(r.get(USER_ID))
                            .source(r.get(SOURCE))
                            .groupIds(r.get(GROUP_IDS))
                            .platform(r.get(PLATFORM))
                            .effectiveFrom(r.get(EFFECTIVE_FROM))
                            .lastSeenAt(r.get(LAST_SEEN_AT))
                            .description(r.get(DESCRIPTION))
                            .createdAt(r.get(field("created_at", Timestamp.class)) != null ?
                                    r.get(field("created_at", Timestamp.class)).toLocalDateTime() : null)
                            .updatedAt(r.get(field("updated_at", Timestamp.class)) != null ?
                                    r.get(field("updated_at", Timestamp.class)).toLocalDateTime() : null)
                            .build());

            log.debug("流量平台-----> 找到 {} 个自 {} 以来的最近更新用户分组关系，耗时 {}ms",
                    result.size(), since, System.currentTimeMillis() - startTime);

            return result;
        } catch (Exception e) {
            log.error("流量平台-----> 查找最近更新的用户分组关系失败", e);
            metricsRegistry.incrementCounter(UserGroupConstants.REPOSITORY_ERROR_METRIC, "method", "findRecentUpdates");
            return Collections.emptyList();
        }
    }

    /**
     * 统计用户分组关系数量
     * @return
     */
    @Timed(value = "repository.usergroup.countAll", percentiles = {0.5, 0.95, 0.99})
    public long countAll() {
        long startTime = System.currentTimeMillis();

        try {
            // 使用安全的方式获取计数，避免NPE
            Long count = dsl.selectCount()
                    .from(USER_GROUP_TABLE)
                    .fetchOneInto(Long.class);

            // 确保返回非null值
            long result = count != null ? count : 0L;

            log.debug("流量平台-----> 统计总用户分组关系数量: {}, 耗时 {}ms",
                    result, System.currentTimeMillis() - startTime);

            return result;
        } catch (Exception e) {
            log.error("流量平台-----> 统计用户分组关系数量失败", e);
            metricsRegistry.incrementCounter(UserGroupConstants.REPOSITORY_ERROR_METRIC, "method", "countAll");
            return 0;
        }
    }
    /**
     * 计算符合条件的用户分组关系数量
     *
     * @param source 来源（可选）
     * @param userId 用户ID（可选）
     * @return 符合条件的记录数
     */
    @Timed(value = "repository.usergroup.countUserGroups", percentiles = {0.5, 0.95, 0.99})
    public long countUserGroups(String source, String userId) {
        long startTime = System.currentTimeMillis();

        try {
            org.jooq.Condition condition = DSL.trueCondition();

            if (source != null && !source.isEmpty()) {
                condition = condition.and(SOURCE.eq(source));
            }

            if (userId != null && !userId.isEmpty()) {
                condition = condition.and(USER_ID.eq(userId));
            }

            Long count = dsl.selectCount()
                    .from(USER_GROUP_TABLE)
                    .where(condition)
                    .fetchOneInto(Long.class);

            // 确保返回非null值
            long result = count != null ? count : 0L;

            log.debug("流量平台-----> 统计用户分组关系数量: {}，来源: {}，用户ID: {}，耗时 {}ms",
                    result, source, userId, System.currentTimeMillis() - startTime);

            return result;
        } catch (Exception e) {
            log.error("流量平台-----> 统计用户分组关系数量失败", e);
            metricsRegistry.incrementCounter(UserGroupConstants.REPOSITORY_ERROR_METRIC, "method", "countUserGroups");
            return 0;
        }
    }

    /**
     * 分页查询用户分组关系
     *
     * @param source 来源（可选）
     * @param userId 用户ID（可选）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 用户分组关系列表
     */
    @Timed(value = "repository.usergroup.findUserGroupsWithPagination", percentiles = {0.5, 0.95, 0.99})
    public List<UserGroupRelation> findUserGroupsWithPagination(String source, String userId, int offset, int limit) {
        long startTime = System.currentTimeMillis();

        try {
            org.jooq.Condition condition = DSL.trueCondition();

            if (source != null && !source.isEmpty()) {
                condition = condition.and(SOURCE.eq(source));
            }

            if (userId != null && !userId.isEmpty()) {
                condition = condition.and(USER_ID.eq(userId));
            }

            List<UserGroupRelation> result = dsl.select()
                    .from(USER_GROUP_TABLE)
                    .where(condition)
                    .orderBy(field("id", Long.class).desc())
                    .limit(limit)
                    .offset(offset)
                    .fetch()
                    .map(r -> {
                        // 安全地处理时间戳到LocalDateTime的转换
                        LocalDateTime createdAt = null;
                        LocalDateTime updatedAt = null;

                        // 获取时间戳并转换
                        Timestamp createdTimestamp = r.get(field("created_at", Timestamp.class));
                        if (createdTimestamp != null) {
                            createdAt = createdTimestamp.toLocalDateTime();
                        }

                        Timestamp updatedTimestamp = r.get(field("updated_at", Timestamp.class));
                        if (updatedTimestamp != null) {
                            updatedAt = updatedTimestamp.toLocalDateTime();
                        }

                        return UserGroupRelation.builder()
                                .id(r.get(field("id", Long.class)))
                                .userId(r.get(USER_ID))
                                .source(r.get(SOURCE))
                                .groupIds(r.get(GROUP_IDS))
                                .platform(r.get(PLATFORM))
                                .effectiveFrom(r.get(EFFECTIVE_FROM))
                                .lastSeenAt(r.get(LAST_SEEN_AT))
                                .description(r.get(DESCRIPTION))
                                .createdAt(createdAt)
                                .updatedAt(updatedAt)
                                .build();
                    });

            log.debug("流量平台-----> 使用分页(偏移量: {}, 限制: {})找到 {} 个用户分组关系，耗时 {}ms",
                    offset, limit, result.size(), System.currentTimeMillis() - startTime);

            return result;
        } catch (Exception e) {
            log.error("流量平台-----> 使用分页查询用户分组关系失败", e);
            metricsRegistry.incrementCounter(UserGroupConstants.REPOSITORY_ERROR_METRIC, "method", "findUserGroupsWithPagination");
            return Collections.emptyList();
        }
    }


    /**
     * 根据ID查找用户分组关系
     *
     * @param id 用户分组关系ID
     * @return 用户分组关系，如果不存在则返回null
     */
    @Timed(value = "repository.usergroup.findById", percentiles = {0.5, 0.95, 0.99})
    public UserGroupRelation findById(Long id) {
        long startTime = System.currentTimeMillis();

        try {
            Record record = dsl.select()
                    .from(USER_GROUP_TABLE)
                    .where(field("id", Long.class).eq(id))
                    .fetchOne();

            if (record == null) {
                log.debug("流量平台-----> 未找到ID为 {} 的用户分组关系", id);
                return null;
            }

            // 安全地处理时间戳到LocalDateTime的转换
            LocalDateTime createdAt = null;
            LocalDateTime updatedAt = null;

            // 获取时间戳并转换
            Timestamp createdTimestamp = record.get(field("created_at", Timestamp.class));
            if (createdTimestamp != null) {
                createdAt = createdTimestamp.toLocalDateTime();
            }

            Timestamp updatedTimestamp = record.get(field("updated_at", Timestamp.class));
            if (updatedTimestamp != null) {
                updatedAt = updatedTimestamp.toLocalDateTime();
            }

            UserGroupRelation result = UserGroupRelation.builder()
                    .id(record.get(field("id", Long.class)))
                    .userId(record.get(USER_ID))
                    .source(record.get(SOURCE))
                    .groupIds(record.get(GROUP_IDS))
                    .platform(record.get(PLATFORM))
                    .effectiveFrom(record.get(EFFECTIVE_FROM))
                    .lastSeenAt(record.get(LAST_SEEN_AT))
                    .description(record.get(DESCRIPTION))
                    .createdAt(createdAt)
                    .updatedAt(updatedAt)
                    .build();

            log.debug("流量平台-----> 找到ID为 {} 的用户分组关系，耗时 {}ms",
                    id, System.currentTimeMillis() - startTime);

            return result;
        } catch (Exception e) {
            log.error("流量平台-----> 查找ID为 {} 的用户分组关系失败", id, e);
            metricsRegistry.incrementCounter(UserGroupConstants.REPOSITORY_ERROR_METRIC, "method", "findById");
            return null;
        }
    }


    /**
     * 删除用户分组关系
     *
     * @param id 用户分组关系ID
     * @return 影响的行数
     */
    @Timed(value = "repository.usergroup.deleteUserGroup", percentiles = {0.5, 0.95, 0.99})
    public int deleteUserGroup(Long id) {
        long startTime = System.currentTimeMillis();

        try {
            int count = dsl.deleteFrom(USER_GROUP_TABLE)
                    .where(field("id", Long.class).eq(id))
                    .execute();

            log.debug("流量平台-----> 删除ID为 {} 的用户分组关系，影响行数: {}，耗时 {}ms",
                    id, count, System.currentTimeMillis() - startTime);

            return count;
        } catch (Exception e) {
            log.error("流量平台-----> 删除ID为 {} 的用户分组关系失败", id, e);
            metricsRegistry.incrementCounter(UserGroupConstants.REPOSITORY_ERROR_METRIC, "method", "deleteUserGroup");
            throw e;
        }
    }

    /**
     * 查询指定来源下的所有分组ID
     *
     * @param source 来源/运营商
     * @return 该来源下的所有分组ID集合
     */
    @Timed(value = "repository.usergroup.findGroupIdsBySource", percentiles = {0.5, 0.95, 0.99})
    public Set<String> findGroupIdsBySource(String source) {
        long startTime = System.currentTimeMillis();

        try {
            // 从user_group_relation表获取特定source下的所有groupIds
            Result<Record1<String>> groupIdsRecords = dsl.selectDistinct(GROUP_IDS)
                    .from(USER_GROUP_TABLE)
                    .where(SOURCE.eq(source))
                    .fetch();

            // 提取所有groupId到一个集合中
            Set<String> allGroupIds = new HashSet<>();
            for (Record1<String> record : groupIdsRecords) {
                String groupIdsStr = record.value1();
                if (groupIdsStr != null && !groupIdsStr.isEmpty()) {
                    Collections.addAll(allGroupIds, groupIdsStr.split(","));
                }
            }

            log.debug("流量平台-----> 来源 {} 关联的分组ID数量: {}，耗时 {}ms",
                    source, allGroupIds.size(), System.currentTimeMillis() - startTime);

            return allGroupIds;
        } catch (Exception e) {
            log.error("流量平台-----> 查询来源 {} 的分组ID失败", source, e);
            metricsRegistry.incrementCounter(UserGroupConstants.REPOSITORY_ERROR_METRIC, "method", "findGroupIdsBySource");
            return Collections.emptySet();
        }
    }

}