package com.iptv.flux.service.service;

import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.dto.DataImportProgressDTO;
import com.iptv.flux.common.dto.DataImportRequestDTO;
import com.iptv.flux.common.dto.DataImportResponseDTO;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.model.entity.BatchProcessResult;
import com.iptv.flux.service.repository.DataImportRepository;
import com.iptv.flux.service.repository.DataImportLogRepository;
import com.iptv.flux.service.service.progress.ProgressManager;
import com.iptv.flux.service.util.ModernExcelProcessor;
import io.micrometer.core.annotation.Timed;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.service
 * @className: DataImportService
 * @author: chiron
 * @description: 现代化数据导入服务 - 高效、准确、实时进度跟踪
 * @date: 2025-06-13
 * @version: 2.0
 */
@Service
@Slf4j
public class DataImportService {

    /**
     * -- GETTER --
     *  获取Excel处理器（用于预览功能）
     */
    @Getter
    private final ModernExcelProcessor excelProcessor;
    private final DataImportRepository dataImportRepository;
    private final DataImportLogRepository dataImportLogRepository;
    private final GroupInfoService groupInfoService;
    private final RedissonClient redissonClient;
    private final MetricsRegistryUtil metricsRegistry;
    private final ProgressManager progressManager;
    private final ApplicationContext applicationContext;

    public DataImportService(
            ModernExcelProcessor excelProcessor,
            DataImportRepository dataImportRepository,
            DataImportLogRepository dataImportLogRepository,
            GroupInfoService groupInfoService,
            RedissonClient redissonClient,
            MetricsRegistryUtil metricsRegistry,
            ProgressManager progressManager,
            ApplicationContext applicationContext) {
        this.excelProcessor = excelProcessor;
        this.dataImportRepository = dataImportRepository;
        this.dataImportLogRepository = dataImportLogRepository;
        this.groupInfoService = groupInfoService;
        this.redissonClient = redissonClient;
        this.metricsRegistry = metricsRegistry;
        this.progressManager = progressManager;
        this.applicationContext = applicationContext;
    }

    /**
     * 导入Excel文件数据 - 现代化实现，支持实时进度跟踪
     * 
     * @param file Excel文件
     * @param request 导入请求参数
     * @return 导入响应
     */
    @Timed(value = "service.dataimport.importExcel", percentiles = {0.5, 0.95, 0.99})
    public DataImportResponseDTO importExcelFile(MultipartFile file, DataImportRequestDTO request) {
        long startTime = System.currentTimeMillis();
        String taskId = generateTaskId();
        
        log.info("流量平台-----> 开始导入Excel文件: {}，任务ID: {}", file.getOriginalFilename(), taskId);

        // 验证文件
        if (!excelProcessor.isValidExcelFile(file)) {
            throw new IllegalArgumentException("不支持的文件格式，请上传.xlsx或.xls文件");
        }

        // 检查文件大小
        if (file.getSize() > UserGroupConstants.DATA_IMPORT_MAX_FILE_SIZE_MB * 1024 * 1024) {
            throw new IllegalArgumentException("文件大小超过限制：" + UserGroupConstants.DATA_IMPORT_MAX_FILE_SIZE_MB + "MB");
        }

        try {
            // 快速获取总记录数
            int totalRecords = excelProcessor.getTotalRecords(file);
            
            // 计算批次数
            int batchSize = request.getBatchSize() != null ? request.getBatchSize() : 1000;
            int totalBatches = (totalRecords + batchSize - 1) / batchSize;

            // 创建进度跟踪器
            ProgressManager.ProgressTracker tracker = progressManager.createTracker(taskId, totalRecords, totalBatches);
            
            // 构建响应对象
            DataImportResponseDTO response = DataImportResponseDTO.builder()
                    .taskId(taskId)
                    .fileName(file.getOriginalFilename())
                    .fileSize(file.getSize())
                    .totalRecords(totalRecords)
                    .estimatedUserCount(totalRecords) // 添加预估用户数，与totalRecords相同
                    .status("PENDING")
                    .startTime(LocalDateTime.now())
                    .source(request.getSource())
                    .build();

            if (request.getAsync()) {
                // 异步处理 - 立即返回，所有操作都在后台进行
                log.info("流量平台-----> 启动异步处理，任务ID: {}，总记录数: {}", taskId, totalRecords);

                // 记录导入开始日志
                saveImportStartLog(response, request.getDescription(), true);

                // 通过ApplicationContext获取代理对象，确保@Async生效
                DataImportService proxyService = applicationContext.getBean(DataImportService.class);
                proxyService.processFileAsync(file, request, tracker);
                response.setStatus("PROCESSING");

                log.info("流量平台-----> 异步任务已提交，立即返回响应，任务ID: {}", taskId);
            } else {
                // 同步处理
                saveImportStartLog(response, request.getDescription(), false);
                response = processFileSync(file, request, response, tracker);

                // 记录同步处理完成日志
                saveImportCompletionLog(response);
            }

            return response;

        } catch (Exception e) {
            log.error("流量平台-----> 导入Excel文件失败，任务ID: {}", taskId, e);
            progressManager.failTask(taskId, e.getMessage());
            saveImportFailureLog(taskId, e.getMessage());
            metricsRegistry.incrementCounter(UserGroupConstants.DATA_IMPORT_ERROR_METRIC);

            throw new RuntimeException("文件导入失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取导入进度 - 使用现代化进度管理器
     */
    public DataImportProgressDTO getImportProgress(String taskId) {
        return progressManager.getProgress(taskId);
    }

    /**
     * 同步处理文件 - 现代化实现，支持实时进度更新
     */
    public DataImportResponseDTO processFileSync(MultipartFile file, DataImportRequestDTO request,
                                                 DataImportResponseDTO response, ProgressManager.ProgressTracker tracker) {
        long startTime = System.currentTimeMillis();
        String taskId = response.getTaskId();

        try {
            // 使用快速预览获取基本信息
            ModernExcelProcessor.PreviewResult previewResult = excelProcessor.fastPreview(file);
            String groupId = extractGroupIdFromFileName(file.getOriginalFilename());

            // 自动创建分组信息（如果启用）
            if (request.getAutoCreateGroup()) {
                createGroupIfNeeded(request, groupId);
            }

            // 计算批次数
            int batchSize = request.getBatchSize() != null ? request.getBatchSize() : 1000;
            int totalBatches = (previewResult.getEstimatedTotal() + batchSize - 1) / batchSize;
            
            // 流式处理Excel文件，实时更新进度
            AtomicInteger processedCount = new AtomicInteger(0);
            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger failedCount = new AtomicInteger(0);
            AtomicInteger batchCounter = new AtomicInteger(0);

            log.info("流量平台-----> 开始流式处理，预计批次数: {}，批次大小: {}", totalBatches, batchSize);

            excelProcessor.processExcelStream(file, userIdBatch -> {
                int currentBatch = batchCounter.incrementAndGet();
                log.info("流量平台-----> 处理批次 {}/{}，数据量: {}", currentBatch, totalBatches, userIdBatch.size());

                // 处理每个批次
                processBatchWithProgress(userIdBatch, groupId, request, tracker,
                        processedCount, successCount, failedCount, currentBatch, totalBatches);
            });

            // 更新最终结果
            response.setProcessedRecords(processedCount.get());
            response.setInsertedRecords(successCount.get());
            response.setFailedRecords(failedCount.get());
            response.setStatus("COMPLETED");
            response.setEndTime(LocalDateTime.now());
            response.setDurationMs(System.currentTimeMillis() - startTime);
            response.setProgressPercentage(1.0); // 使用小数格式：1.0表示100%

            // 标记完成
            tracker.complete();

            log.info("流量平台-----> 同步处理完成，任务ID: {}，处理: {}，成功: {}，失败: {}",
                    taskId, processedCount.get(), successCount.get(), failedCount.get());

        } catch (Exception e) {
            log.error("流量平台-----> 同步处理文件失败，任务ID: {}", taskId, e);
            tracker.fail(e.getMessage());

            response.setStatus("FAILED");
            response.setErrorMessage(e.getMessage());
            response.setEndTime(LocalDateTime.now());
            response.setDurationMs(System.currentTimeMillis() - startTime);

            // 记录失败日志
            saveImportFailureLog(taskId, e.getMessage());
        }

        return response;
    }

    /**
     * 异步处理文件导入 - 现代化实现
     */
    @Async("dataImportExecutor")
    public CompletableFuture<Void> processFileAsync(MultipartFile file, DataImportRequestDTO request,
                                                    ProgressManager.ProgressTracker tracker) {
        String taskId = tracker.getTaskId();
        String lockKey = UserGroupConstants.DATA_IMPORT_LOCK_PREFIX + taskId;
        RLock lock = redissonClient.getLock(lockKey);

        log.info("流量平台-----> 异步任务开始执行，任务ID: {}，线程: {}", taskId, Thread.currentThread().getName());

        try {
            // 获取分布式锁
            if (lock.tryLock(30, 1800, TimeUnit.SECONDS)) {
                try {
                    log.info("流量平台-----> 获取分布式锁成功，开始异步处理文件，任务ID: {}", taskId);

                    // 获取总记录数（重要：异步处理也需要这个信息）
                    int totalRecords = excelProcessor.getTotalRecords(file);

                    // 构建响应对象
                    DataImportResponseDTO response = DataImportResponseDTO.builder()
                            .taskId(taskId)
                            .fileName(file.getOriginalFilename())
                            .fileSize(file.getSize())
                            .totalRecords(totalRecords)
                            .estimatedUserCount(totalRecords) // 添加预估用户数
                            .status("PROCESSING")
                            .startTime(LocalDateTime.now())
                            .source(request.getSource())
                            .build();

                    log.info("流量平台-----> 异步处理获取总记录数: {}", totalRecords);

                    // 执行同步处理逻辑
                    DataImportResponseDTO finalResponse = processFileSync(file, request, response, tracker);

                    // 记录异步处理完成日志
                    saveImportCompletionLog(finalResponse);

                } finally {
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                        log.debug("流量平台-----> 释放分布式锁，任务ID: {}", taskId);
                    }
                }
            } else {
                log.warn("流量平台-----> 无法获取导入锁，任务ID: {}", taskId);
                tracker.fail("无法获取处理锁，请稍后重试");
                saveImportFailureLog(taskId, "无法获取处理锁，请稍后重试");
            }
        } catch (Exception e) {
            log.error("流量平台-----> 异步处理文件失败，任务ID: {}", taskId, e);
            tracker.fail("处理异常: " + e.getMessage());
            saveImportFailureLog(taskId, "处理异常: " + e.getMessage());
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId() {
        return "import_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 处理批次数据并更新进度
     */
    private void processBatchWithProgress(List<String> userIds, String groupId,
                                        DataImportRequestDTO request, ProgressManager.ProgressTracker tracker,
                                        AtomicInteger processedCount, AtomicInteger successCount,
                                        AtomicInteger failedCount, int currentBatch, int totalBatches) {
        try {
            log.info("流量平台-----> 开始处理批次 {}/{}，用户数: {}", currentBatch, totalBatches, userIds.size());

            // 构建用户分组映射
            Map<String, List<String>> userGroupMappings = new HashMap<>();
            for (String userId : userIds) {
                userGroupMappings.put(userId, Arrays.asList(groupId));
            }

            // 使用高性能批量处理
            BatchProcessResult result =
                    dataImportRepository.batchProcessUserGroups(userGroupMappings, request.getSource(), request.getOverwrite());

            // 更新计数器
            int processed = result.getProcessedCount();
            int success = result.getInsertedCount() + result.getUpdatedCount();
            int failed = result.getFailedCount();

            processedCount.addAndGet(processed);
            successCount.addAndGet(success);
            failedCount.addAndGet(failed);

            // 更新进度 - 关键修复：确保进度更新
            tracker.updateBatch(processed, success, failed);

            log.info("流量平台-----> 批次 {}/{} 处理完成，处理: {}，成功: {}，失败: {}，累计处理: {}",
                    currentBatch, totalBatches, processed, success, failed, processedCount.get());

        } catch (Exception e) {
            log.error("流量平台-----> 批次 {}/{} 处理失败", currentBatch, totalBatches, e);
            int failed = userIds.size();
            processedCount.addAndGet(failed);
            failedCount.addAndGet(failed);
            tracker.addFailed(failed);
        }
    }

    /**
     * 创建分组信息（如果需要）
     */
    private void createGroupIfNeeded(DataImportRequestDTO request, String groupId) {
        try {
            log.info("流量平台-----> 开始自动创建分组信息，分组ID: {}", groupId);

            groupInfoService.autoCreateOrUpdateGroup(
                    groupId,
                    request.getStrategyId(),
                    request.getGroupName(),
                    request.getGroupDescription(),
                    request.getSource()
            );

            log.info("流量平台-----> 分组信息创建/更新成功，分组ID: {}", groupId);
        } catch (Exception e) {
            log.error("流量平台-----> 分组信息创建/更新失败，分组ID: {}，将继续处理用户数据", groupId, e);
            // 不中断处理流程，继续处理用户数据
        }
    }

    /**
     * 从文件名提取分组ID
     */
    private String extractGroupIdFromFileName(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "unknown";
        }

        // 移除文件扩展名
        String nameWithoutExt = filename.replaceAll("\\.(xlsx?|xls)$", "");

        // 简单的分组ID提取逻辑，可以根据实际需求调整
        return nameWithoutExt.replaceAll("[^a-zA-Z0-9_-]", "_");
    }

    /**
     * 获取性能统计信息
     */
    public String getPerformanceStats() {
        return "性能统计功能已集成到进度管理器中";
    }

    /**
     * 清理过期的进度缓存
     */
    public void cleanupExpiredProgress() {
        progressManager.cleanupExpiredProgress();
    }

    /**
     * 保存导入开始日志
     */
    private void saveImportStartLog(DataImportResponseDTO response, String description, boolean asyncMode) {
        try {
            dataImportLogRepository.saveImportLog(response, description, asyncMode);
            log.debug("流量平台-----> 导入开始日志已保存，任务ID: {}", response.getTaskId());
        } catch (Exception e) {
            log.error("流量平台-----> 保存导入开始日志失败，任务ID: {}", response.getTaskId(), e);
            // 不影响主流程，继续执行
        }
    }

    /**
     * 保存导入完成日志
     */
    private void saveImportCompletionLog(DataImportResponseDTO response) {
        try {
            dataImportLogRepository.updateImportLogCompletion(response);
            log.debug("流量平台-----> 导入完成日志已更新，任务ID: {}，状态: {}",
                    response.getTaskId(), response.getStatus());
        } catch (Exception e) {
            log.error("流量平台-----> 更新导入完成日志失败，任务ID: {}", response.getTaskId(), e);
            // 不影响主流程
        }
    }

    /**
     * 保存导入失败日志
     */
    private void saveImportFailureLog(String taskId, String errorMessage) {
        try {
            dataImportLogRepository.updateImportLogStatus(taskId, "FAILED", errorMessage);
            log.debug("流量平台-----> 导入失败日志已更新，任务ID: {}", taskId);
        } catch (Exception e) {
            log.error("流量平台-----> 更新导入失败日志失败，任务ID: {}", taskId, e);
            // 不影响主流程
        }
    }
}
