package com.iptv.flux.service.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.IOException;
import java.time.Duration;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.config
 * @className: FTPConfig
 * @author: Claude 4.0 sonnet
 * @description: FTP客户端配置类，管理FTP连接池和相关参数
 * @date: 2025/8/14 12:00
 * @version: 1.0
 */
@Configuration
@Slf4j
public class FTPConfig {

    /**
     * FTP连接配置属性
     */
    @Data
    @ConfigurationProperties(prefix = "traffic.sync.ftp")
    @Validated
    public static class FTPProperties {
        
        /**
         * FTP服务器主机地址
         */
        @NotBlank(message = "FTP服务器地址不能为空")
        private String host = "***********";
        
        /**
         * FTP服务器端口
         */
        @Min(value = 1, message = "FTP端口必须大于0")
        @Max(value = 65535, message = "FTP端口不能超过65535")
        private int port = 21;
        
        /**
         * FTP用户名
         */
        @NotBlank(message = "FTP用户名不能为空")
        private String username = "vstorero";
        
        /**
         * FTP密码
         */
        @NotBlank(message = "FTP密码不能为空")
        private String password = "iptv_2024at2other";
        
        /**
         * 连接超时时间（毫秒）
         */
        @Min(value = 1000, message = "连接超时时间不能少于1秒")
        private int connectionTimeout = 30000;
        
        /**
         * 数据传输超时时间（毫秒）
         */
        @Min(value = 1000, message = "数据传输超时时间不能少于1秒")
        private int dataTimeout = 60000;
        
        /**
         * 控制连接超时时间（毫秒）
         */
        @Min(value = 1000, message = "控制连接超时时间不能少于1秒")
        private int controlTimeout = 30000;
        
        /**
         * 重试次数
         */
        @Min(value = 0, message = "重试次数不能为负数")
        @Max(value = 10, message = "重试次数不能超过10次")
        private int retryAttempts = 3;
        
        /**
         * 重试间隔时间（毫秒）
         */
        @Min(value = 100, message = "重试间隔不能少于100毫秒")
        private long retryInterval = 1000;
        
        /**
         * 是否使用被动模式
         */
        private boolean passiveMode = true;
        
        /**
         * 文件传输模式（二进制）
         */
        private boolean binaryMode = true;
        
        /**
         * 连接池配置
         */
        @NotNull
        private PoolConfig pool = new PoolConfig();
        
        @Data
        public static class PoolConfig {
            /**
             * 连接池最大连接数
             */
            @Min(value = 1, message = "连接池最大连接数不能少于1")
            private int maxTotal = 10;
            
            /**
             * 连接池最大空闲连接数
             */
            @Min(value = 0, message = "最大空闲连接数不能为负数")
            private int maxIdle = 5;
            
            /**
             * 连接池最小空闲连接数
             */
            @Min(value = 0, message = "最小空闲连接数不能为负数")
            private int minIdle = 1;
            
            /**
             * 获取连接时的最大等待时间（毫秒）
             */
            @Min(value = -1, message = "最大等待时间不能小于-1")
            private long maxWaitMillis = 30000;
            
            /**
             * 连接空闲时间（毫秒），超过此时间的空闲连接将被回收
             */
            @Min(value = 1000, message = "连接空闲时间不能少于1秒")
            private long minEvictableIdleTimeMillis = 300000; // 5分钟
            
            /**
             * 空闲连接检测间隔（毫秒）
             */
            @Min(value = 1000, message = "空闲连接检测间隔不能少于1秒")
            private long timeBetweenEvictionRunsMillis = 60000; // 1分钟
            
            /**
             * 是否在获取连接时测试连接有效性
             */
            private boolean testOnBorrow = true;
            
            /**
             * 是否在归还连接时测试连接有效性
             */
            private boolean testOnReturn = false;
            
            /**
             * 是否在连接空闲时测试连接有效性
             */
            private boolean testWhileIdle = true;
        }
    }

    /**
     * FTP连接属性Bean
     */
    @Bean
    @ConfigurationProperties(prefix = "traffic.sync.ftp")
    public FTPProperties ftpProperties() {
        return new FTPProperties();
    }

    /**
     * FTP连接池工厂
     */
    public static class FTPClientFactory extends BasePooledObjectFactory<FTPClient> {
        
        private final FTPProperties ftpProperties;
        
        public FTPClientFactory(FTPProperties ftpProperties) {
            this.ftpProperties = ftpProperties;
        }
        
        @Override
        public FTPClient create() throws Exception {
            FTPClient ftpClient = new FTPClient();
            
            try {
                // 设置超时时间
                ftpClient.setConnectTimeout(ftpProperties.getConnectionTimeout());
                ftpClient.setDataTimeout(Duration.ofMillis(ftpProperties.getDataTimeout()));
                ftpClient.setControlKeepAliveTimeout(Duration.ofMillis(ftpProperties.getControlTimeout()));
                
                // 连接FTP服务器
                ftpClient.connect(ftpProperties.getHost(), ftpProperties.getPort());
                
                // 登录
                boolean loginSuccess = ftpClient.login(ftpProperties.getUsername(), ftpProperties.getPassword());
                if (!loginSuccess) {
                    throw new IOException("FTP登录失败，用户名或密码错误");
                }
                
                // 设置传输模式
                if (ftpProperties.isBinaryMode()) {
                    ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
                }
                
                // 设置被动模式
                if (ftpProperties.isPassiveMode()) {
                    ftpClient.enterLocalPassiveMode();
                }
                
                log.debug("创建FTP连接成功，服务器: {}:{}", ftpProperties.getHost(), ftpProperties.getPort());
                return ftpClient;
                
            } catch (Exception e) {
                if (ftpClient.isConnected()) {
                    try {
                        ftpClient.disconnect();
                    } catch (IOException ex) {
                        log.warn("关闭FTP连接时发生异常", ex);
                    }
                }
                throw e;
            }
        }
        
        @Override
        public PooledObject<FTPClient> wrap(FTPClient ftpClient) {
            return new DefaultPooledObject<>(ftpClient);
        }
        
        @Override
        public void destroyObject(PooledObject<FTPClient> pooledObject) throws Exception {
            FTPClient ftpClient = pooledObject.getObject();
            if (ftpClient != null && ftpClient.isConnected()) {
                try {
                    ftpClient.logout();
                    ftpClient.disconnect();
                    log.debug("销毁FTP连接成功");
                } catch (IOException e) {
                    log.warn("销毁FTP连接时发生异常", e);
                }
            }
        }
        
        @Override
        public boolean validateObject(PooledObject<FTPClient> pooledObject) {
            FTPClient ftpClient = pooledObject.getObject();
            try {
                return ftpClient != null && ftpClient.isConnected() && ftpClient.sendNoOp();
            } catch (IOException e) {
                log.debug("FTP连接验证失败", e);
                return false;
            }
        }
    }

    /**
     * FTP连接池Bean
     */
    @Bean
    public GenericObjectPool<FTPClient> ftpClientPool(FTPProperties ftpProperties) {
        FTPClientFactory factory = new FTPClientFactory(ftpProperties);
        
        GenericObjectPoolConfig<FTPClient> poolConfig = new GenericObjectPoolConfig<>();
        FTPProperties.PoolConfig config = ftpProperties.getPool();
        
        poolConfig.setMaxTotal(config.getMaxTotal());
        poolConfig.setMaxIdle(config.getMaxIdle());
        poolConfig.setMinIdle(config.getMinIdle());
        poolConfig.setMaxWaitMillis(config.getMaxWaitMillis());
        poolConfig.setMinEvictableIdleTimeMillis(config.getMinEvictableIdleTimeMillis());
        poolConfig.setTimeBetweenEvictionRunsMillis(config.getTimeBetweenEvictionRunsMillis());
        poolConfig.setTestOnBorrow(config.isTestOnBorrow());
        poolConfig.setTestOnReturn(config.isTestOnReturn());
        poolConfig.setTestWhileIdle(config.isTestWhileIdle());

        // 禁用JMX监控以避免名称冲突
        poolConfig.setJmxEnabled(false);

        GenericObjectPool<FTPClient> pool = new GenericObjectPool<>(factory, poolConfig);
        
        log.info("流量平台-----> FTP连接池初始化完成，最大连接数: {}，最大空闲: {}，最小空闲: {}",
                config.getMaxTotal(), config.getMaxIdle(), config.getMinIdle());
        
        return pool;
    }
}
