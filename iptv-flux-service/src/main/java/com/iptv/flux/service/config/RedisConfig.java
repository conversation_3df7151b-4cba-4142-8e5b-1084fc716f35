package com.iptv.flux.service.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * 集群部署
 */
@Configuration
@Slf4j
public class RedisConfig {

    @Value("${spring.redis.cluster.nodes}")
    private String clusterNodes;

    @Value("${spring.redis.password:}")
    private String password;

    @Value("${spring.redis.timeout:3000}")
    private int timeout;

    @Value("${spring.redis.lettuce.pool.max-active:1000}")
    private int maxActive;

    @Value("${spring.redis.lettuce.pool.max-idle:10}")
    private int maxIdle;

    @Value("${spring.redis.lettuce.pool.min-idle:10}")
    private int minIdle;

    @Value("${spring.redis.lettuce.pool.max-wait:3000ms}")
    private Duration maxWait;

    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        long startTime = System.currentTimeMillis();
        List<String> clusterNodeList = Arrays.asList(this.clusterNodes.split(","));
        log.info("流量平台-----> 开始配置 Redis 连接工厂，集群节点数: {}, 节点列表: {}",
                clusterNodeList.size(), clusterNodeList);

        RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration(clusterNodeList);
        if (password != null && !password.isEmpty()) {
            clusterConfig.setPassword(password);
            log.debug("流量平台-----> Redis 密码已设置");
        }

        GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
        poolConfig.setMaxTotal(maxActive);
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMinIdle(minIdle);
        poolConfig.setMaxWait(maxWait);

        log.debug("流量平台-----> Redis 连接池配置 - maxActive: {}, maxIdle: {}, minIdle: {}, maxWait: {}",
                maxActive, maxIdle, minIdle, maxWait);

        LettuceClientConfiguration clientConfig = LettucePoolingClientConfiguration.builder()
                .commandTimeout(Duration.ofMillis(timeout))
                .poolConfig(poolConfig)
                .build();

        LettuceConnectionFactory factory = new LettuceConnectionFactory(clusterConfig, clientConfig);
        long endTime = System.currentTimeMillis();
        log.info("流量平台-----> Redis 连接工厂配置完成，耗时: {}ms", endTime - startTime);

        return factory;
    }

    @Bean(name = "stringRedisTemplate")
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(redisConnectionFactory);
        return template;
    }


    @Bean
    public RedissonClient redissonClient() {
        long startTime = System.currentTimeMillis();
        log.info("流量平台-----> 开始配置 Redisson 客户端");

        List<String> clusterNodeList = Arrays.asList(this.clusterNodes.split(","));
        Config config = new Config();
        // 使用Jackson编码器支持对象序列化
        config.setCodec(new org.redisson.codec.JsonJacksonCodec());

        StringBuilder nodesBuilder = new StringBuilder();
        for (String node : clusterNodeList) {
            String[] parts = node.split(":");
            nodesBuilder.append("redis://").append(parts[0]).append(":").append(parts[1]).append(",");
        }

        String nodes = nodesBuilder.toString();
        if (nodes.endsWith(",")) {
            nodes = nodes.substring(0, nodes.length() - 1);
        }

        log.debug("流量平台-----> Redisson 节点地址: {}", nodes);

        config.useClusterServers()
                .setScanInterval(2000)
                .setIdleConnectionTimeout(timeout)
                .setConnectTimeout(timeout)
                .setRetryAttempts(3)
                .setRetryInterval(1500)
                .setMasterConnectionMinimumIdleSize(minIdle / 3)
                .setMasterConnectionPoolSize(maxActive / 3)
                .setSlaveConnectionMinimumIdleSize(minIdle / 3)
                .setSlaveConnectionPoolSize(maxActive / 3)
                .setSubscriptionConnectionMinimumIdleSize(minIdle / 10)
                .setSubscriptionConnectionPoolSize(maxActive / 10)
                .addNodeAddress(nodes.split(","));

        if (password != null && !password.isEmpty()) {
            config.useClusterServers().setPassword(password);
            log.debug("流量平台-----> Redisson 密码已设置");
        }

        RedissonClient client = Redisson.create(config);
        long endTime = System.currentTimeMillis();
        log.info("流量平台-----> Redisson 客户端配置完成，耗时: {}ms", endTime - startTime);

        return client;
    }
}