package com.iptv.flux.service.service;

import com.iptv.flux.common.dto.BlacklistUserDTO;
import com.iptv.flux.common.dto.ListQueryRequestDTO;
import com.iptv.flux.common.dto.PagedResponseDTO;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.repository.BlacklistRepository;
import io.micrometer.core.annotation.Timed;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import com.iptv.flux.common.utils.CacheKeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 黑名单服务层
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BlacklistService {

    private final BlacklistRepository blacklistRepository;
    private final MetricsRegistryUtil metricsRegistry;

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 分页查询黑名单列表
     */
    @Timed(value = "service.blacklist.findPage", percentiles = {0.5, 0.95, 0.99})
    public PagedResponseDTO<BlacklistUserDTO> findPage(ListQueryRequestDTO request) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始查询黑名单列表，页码: {}, 每页: {}", request.getPage(), request.getPageSize());

        try {
            // 参数校验
            validatePageRequest(request);

            PagedResponseDTO<BlacklistUserDTO> result = blacklistRepository.findPage(request);

            log.debug("流量平台-----> 查询黑名单列表完成，总数: {}, 当前页: {}, 耗时: {}ms",
                    result.getTotal(), result.getList().size(), System.currentTimeMillis() - startTime);

            metricsRegistry.incrementCounter("service.blacklist.findPage.success");
            return result;

        } catch (Exception e) {
            log.error("流量平台-----> 查询黑名单列表失败", e);
            metricsRegistry.incrementCounter("service.blacklist.findPage.error");
            throw new RuntimeException("查询黑名单列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加黑名单用户
     */
    @Transactional(rollbackFor = Exception.class)
    @Timed(value = "service.blacklist.addUser", percentiles = {0.5, 0.95, 0.99})
    public void addUser(BlacklistUserDTO dto) {
        long startTime = System.currentTimeMillis();
        log.info("流量平台-----> 开始添加黑名单用户: {}, 来源: {}", dto.getUserId(), dto.getSource());

        try {
            // 参数校验
            validateUserDTO(dto);

            // 检查是否已存在
            if (blacklistRepository.existsUser(dto.getUserId(), dto.getSource())) {
                throw new IllegalArgumentException("用户已在黑名单中: " + dto.getUserId() + ", 来源: " + dto.getSource());
            }

            // 设置添加时间
            dto.setAddTime(LocalDateTime.now());

            // 添加到数据库
            blacklistRepository.addUser(dto);

            // 清理相关缓存
            clearUserCache(dto.getUserId(), dto.getSource());

            log.info("流量平台-----> 成功添加黑名单用户并清理缓存: {}, 来源: {}, 耗时: {}ms",
                    dto.getUserId(), dto.getSource(), System.currentTimeMillis() - startTime);

            metricsRegistry.incrementCounter("service.blacklist.addUser.success");

        } catch (DuplicateKeyException e) {
            log.warn("流量平台-----> 用户已在黑名单中: {}, 来源: {}", dto.getUserId(), dto.getSource());
            throw new IllegalArgumentException("用户已在黑名单中: " + dto.getUserId() + ", 来源: " + dto.getSource());
        } catch (Exception e) {
            log.error("流量平台-----> 添加黑名单用户失败: {}, 来源: {}", dto.getUserId(), dto.getSource(), e);
            metricsRegistry.incrementCounter("service.blacklist.addUser.error");
            throw new RuntimeException("添加黑名单用户失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除黑名单用户
     */
    @Transactional(rollbackFor = Exception.class)
    @Timed(value = "service.blacklist.deleteUser", percentiles = {0.5, 0.95, 0.99})
    public void deleteUser(String id) {
        long startTime = System.currentTimeMillis();
        log.info("流量平台-----> 开始删除黑名单用户: {}", id);

        try {
            // 参数校验
            if (id == null || id.trim().isEmpty()) {
                throw new IllegalArgumentException("黑名单记录ID不能为空");
            }

            // 先查询用户信息，用于清理缓存
            BlacklistUserDTO userInfo = blacklistRepository.findById(id);
            if (userInfo == null) {
                throw new IllegalArgumentException("黑名单记录不存在: " + id);
            }

            // 删除用户
            boolean success = blacklistRepository.deleteUser(id);
            if (!success) {
                throw new IllegalArgumentException("删除黑名单记录失败: " + id);
            }

            // 清理相关缓存
            clearUserCache(userInfo.getUserId(), userInfo.getSource());

            log.info("流量平台-----> 成功删除黑名单用户并清理缓存: {}, 耗时: {}ms",
                    id, System.currentTimeMillis() - startTime);

            metricsRegistry.incrementCounter("service.blacklist.deleteUser.success");

        } catch (Exception e) {
            log.error("流量平台-----> 删除黑名单用户失败: {}", id, e);
            metricsRegistry.incrementCounter("service.blacklist.deleteUser.error");
            throw new RuntimeException("删除黑名单用户失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查用户是否在黑名单中
     */
    @Timed(value = "service.blacklist.isBlacklisted", percentiles = {0.5, 0.95, 0.99})
    public boolean isBlacklisted(String userId, String source) {
        try {
            if (userId == null || userId.trim().isEmpty() || source == null || source.trim().isEmpty()) {
                return false;
            }

            return blacklistRepository.existsUser(userId, source);

        } catch (Exception e) {
            log.error("流量平台-----> 检查黑名单状态失败: {}, 来源: {}", userId, source, e);
            // 检查失败时，为了安全起见，返回false
            return false;
        }
    }

    /**
     * 校验分页请求参数
     */
    private void validatePageRequest(ListQueryRequestDTO request) {
        if (request.getPage() == null || request.getPage() < 1) {
            throw new IllegalArgumentException("页码必须大于0");
        }
        if (request.getPageSize() == null || request.getPageSize() < 1 || request.getPageSize() > 100) {
            throw new IllegalArgumentException("每页条数必须在1-100之间");
        }
    }

    /**
     * 校验用户DTO
     */
    private void validateUserDTO(BlacklistUserDTO dto) {
        if (dto == null) {
            throw new IllegalArgumentException("黑名单用户信息不能为空");
        }
        if (dto.getUserId() == null || dto.getUserId().trim().isEmpty()) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (dto.getSource() == null || dto.getSource().trim().isEmpty()) {
            throw new IllegalArgumentException("来源不能为空");
        }
        
        // 校验来源值
        try {
            BlacklistUserDTO.Source.fromCode(dto.getSource());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("无效的来源值: " + dto.getSource());
        }
    }

    /**
     * 清理用户相关缓存
     */
    private void clearUserCache(String userId, String source) {
        try {
            String compositeKey = CacheKeyBuilder.buildUserGroupCompositeKey(source, userId);
            String blackWhiteListCacheKey = "bwl:" + compositeKey;

            // 清理黑白名单缓存
            redisTemplate.delete(blackWhiteListCacheKey);

            log.debug("流量平台-----> 已清理用户黑白名单缓存: {}, 来源: {}", userId, source);
        } catch (Exception e) {
            log.warn("流量平台-----> 清理用户黑白名单缓存失败: {}, 来源: {}", userId, source, e);
        }
    }
}
