package com.iptv.flux.service.config;

import java.util.concurrent.TimeUnit;
import com.iptv.flux.common.constants.UserGroupConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.core5.util.Timeout;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.config
 * @className: HttpClientConfig
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 12:55
 * @version: 1.0
 */
@Configuration
@Slf4j
public class HttpClientConfig {

    @Bean
    public PoolingHttpClientConnectionManager poolingConnectionManager() {
        PoolingHttpClientConnectionManager poolingConnectionManager = new PoolingHttpClientConnectionManager();
        poolingConnectionManager.setMaxTotal(UserGroupConstants.MAX_HTTP_CONNECTIONS);
        poolingConnectionManager.setDefaultMaxPerRoute(UserGroupConstants.MAX_HTTP_CONNECTIONS / 4);

        log.info("配置HTTP连接池，最大总连接数：{}，每路由最大连接数：{}",
                UserGroupConstants.MAX_HTTP_CONNECTIONS,
                UserGroupConstants.MAX_HTTP_CONNECTIONS / 4);

        return poolingConnectionManager;
    }

    @Bean
    public RequestConfig requestConfig() {
        return RequestConfig.custom()
                .setConnectionRequestTimeout(Timeout.of(UserGroupConstants.CONNECTION_TIMEOUT, TimeUnit.MILLISECONDS))
                .setResponseTimeout(Timeout.of(UserGroupConstants.READ_TIMEOUT, TimeUnit.MILLISECONDS))
                .build();
    }

    @Bean
    public CloseableHttpClient httpClient(PoolingHttpClientConnectionManager poolingConnectionManager,
                                          RequestConfig requestConfig) {
        return HttpClientBuilder
                .create()
                .setConnectionManager(poolingConnectionManager)
                .setDefaultRequestConfig(requestConfig)
                .build();
    }

    @Bean
    public RestTemplate restTemplate(CloseableHttpClient httpClient) {
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setHttpClient(httpClient);
        return new RestTemplate(requestFactory);
    }
}
