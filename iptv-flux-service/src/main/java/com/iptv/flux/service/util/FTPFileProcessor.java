package com.iptv.flux.service.util;

import com.iptv.flux.service.config.FTPConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeUnit;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.util
 * @className: FTPFileProcessor
 * @author: Claude 4.0 sonnet
 * @description: FTP文件处理器 - 负责FTP连接、文件下载、MD5校验等核心功能
 * @date: 2025/8/14 12:00
 * @version: 1.0
 */
@Component
@Slf4j
public class FTPFileProcessor {

    @Autowired
    private GenericObjectPool<FTPClient> ftpClientPool;

    @Autowired
    private FTPConfig.FTPProperties ftpProperties;

    /**
     * 下载FTP文件到本地临时文件
     *
     * @param ftpUrl FTP文件URL，格式：ftp://username:password@host:port/path/file.txt
     * @return 下载的本地临时文件
     * @throws Exception 下载过程中的异常
     */
    public File downloadFile(String ftpUrl) throws Exception {
        log.info("流量平台-----> 开始下载FTP文件: {}", maskPassword(ftpUrl));
        long startTime = System.currentTimeMillis();

        FTPFileInfo fileInfo = parseFTPUrl(ftpUrl);
        FTPClient ftpClient = null;
        File tempFile = null;

        try {
            // 从连接池获取FTP客户端
            ftpClient = borrowFTPClient();

            // 创建临时文件
            tempFile = createTempFile(fileInfo.getFileName());

            // 下载文件
            boolean success = downloadFileWithRetry(ftpClient, fileInfo.getRemotePath(), tempFile);

            if (!success) {
                throw new IOException("FTP文件下载失败: " + fileInfo.getRemotePath());
            }

            long duration = System.currentTimeMillis() - startTime;
            long fileSize = tempFile.length();
            log.info("流量平台-----> FTP文件下载成功，文件: {}，大小: {} bytes，耗时: {}ms",
                    fileInfo.getFileName(), fileSize, duration);

            return tempFile;

        } catch (Exception e) {
            // 下载失败时清理临时文件
            if (tempFile != null && tempFile.exists()) {
                try {
                    Files.delete(tempFile.toPath());
                } catch (IOException ex) {
                    log.warn("清理临时文件失败: {}", tempFile.getAbsolutePath(), ex);
                }
            }
            log.error("流量平台-----> FTP文件下载失败: {}", maskPassword(ftpUrl), e);
            throw e;
        } finally {
            // 归还FTP客户端到连接池
            if (ftpClient != null) {
                returnFTPClient(ftpClient);
            }
        }
    }

    /**
     * 计算文件MD5校验值
     *
     * @param file 要计算MD5的文件
     * @return MD5校验值（32位小写字符串）
     * @throws Exception 计算过程中的异常
     */
    public String calculateMD5(File file) throws Exception {
        log.debug("流量平台-----> 开始计算文件MD5: {}", file.getName());
        long startTime = System.currentTimeMillis();

        try (FileInputStream fis = new FileInputStream(file);
             BufferedInputStream bis = new BufferedInputStream(fis)) {

            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[8192];
            int bytesRead;

            while ((bytesRead = bis.read(buffer)) != -1) {
                md.update(buffer, 0, bytesRead);
            }

            byte[] digest = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }

            String md5 = sb.toString();
            long duration = System.currentTimeMillis() - startTime;
            log.debug("流量平台-----> 文件MD5计算完成: {}，耗时: {}ms", md5, duration);

            return md5;

        } catch (NoSuchAlgorithmException | IOException e) {
            log.error("流量平台-----> 计算文件MD5失败: {}", file.getName(), e);
            throw e;
        }
    }

    /**
     * 验证文件MD5校验值
     *
     * @param file        要验证的文件
     * @param expectedMD5 期望的MD5值
     * @return 校验是否通过
     */
    public boolean verifyMD5(File file, String expectedMD5) {
        if (expectedMD5 == null || expectedMD5.trim().isEmpty()) {
            log.warn("流量平台-----> 未提供MD5校验值，跳过校验");
            return true;
        }

        try {
            String actualMD5 = calculateMD5(file);
            boolean isValid = expectedMD5.equalsIgnoreCase(actualMD5);

            if (isValid) {
                log.info("流量平台-----> 文件MD5校验通过: {}", file.getName());
            } else {
                log.error("流量平台-----> 文件MD5校验失败，期望: {}，实际: {}", expectedMD5, actualMD5);
            }

            return isValid;

        } catch (Exception e) {
            log.error("流量平台-----> MD5校验过程中发生异常", e);
            return false;
        }
    }

    /**
     * 清理临时文件
     *
     * @param file 要清理的文件
     */
    public void cleanupTempFile(File file) {
        if (file != null && file.exists()) {
            try {
                Files.delete(file.toPath());
                log.debug("流量平台-----> 临时文件清理成功: {}", file.getName());
            } catch (IOException e) {
                log.warn("流量平台-----> 临时文件清理失败: {}", file.getAbsolutePath(), e);
            }
        }
    }

    /**
     * 从连接池借用FTP客户端
     */
    private FTPClient borrowFTPClient() throws Exception {
        try {
            FTPClient client = ftpClientPool.borrowObject(ftpProperties.getPool().getMaxWaitMillis());
            log.debug("流量平台-----> 从连接池获取FTP客户端成功");
            return client;
        } catch (Exception e) {
            log.error("流量平台-----> 从连接池获取FTP客户端失败", e);
            throw e;
        }
    }

    /**
     * 归还FTP客户端到连接池
     */
    private void returnFTPClient(FTPClient ftpClient) {
        try {
            ftpClientPool.returnObject(ftpClient);
            log.debug("流量平台-----> FTP客户端归还连接池成功");
        } catch (Exception e) {
            log.warn("流量平台-----> FTP客户端归还连接池失败", e);
        }
    }

    /**
     * 带重试的文件下载
     */
    private boolean downloadFileWithRetry(FTPClient ftpClient, String remotePath, File localFile) throws Exception {
        int attempts = 0;
        int maxAttempts = ftpProperties.getRetryAttempts() + 1;

        while (attempts < maxAttempts) {
            attempts++;
            try {
                log.debug("流量平台-----> 尝试下载文件，第 {} 次，远程路径: {}", attempts, remotePath);

                try (FileOutputStream fos = new FileOutputStream(localFile);
                     BufferedOutputStream bos = new BufferedOutputStream(fos)) {

                    boolean success = ftpClient.retrieveFile(remotePath, bos);
                    bos.flush();

                    if (success) {
                        log.debug("流量平台-----> 文件下载成功，第 {} 次尝试", attempts);
                        return true;
                    } else {
                        log.warn("流量平台-----> 文件下载失败，第 {} 次尝试，FTP响应: {}",
                                attempts, ftpClient.getReplyString());
                    }
                }

            } catch (IOException e) {
                log.warn("流量平台-----> 文件下载异常，第 {} 次尝试", attempts, e);

                if (attempts >= maxAttempts) {
                    throw e;
                }

                // 重试前等待
                try {
                    TimeUnit.MILLISECONDS.sleep(ftpProperties.getRetryInterval());
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new IOException("下载过程被中断", ie);
                }
            }
        }

        return false;
    }

    /**
     * 创建临时文件
     */
    private File createTempFile(String fileName) throws IOException {
        String prefix = "ftp_download_";
        String suffix = getFileExtension(fileName);

        Path tempFile = Files.createTempFile(prefix, suffix);
        File file = tempFile.toFile();
        file.deleteOnExit(); // JVM退出时自动删除

        log.debug("流量平台-----> 创建临时文件: {}", file.getAbsolutePath());
        return file;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return ".tmp";
        }

        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex);
        }

        return ".tmp";
    }

    /**
     * 解析FTP URL
     */
    private FTPFileInfo parseFTPUrl(String ftpUrl) throws Exception {
        try {
            URI uri = new URI(ftpUrl);
            String path = uri.getPath();
            String fileName = path.substring(path.lastIndexOf('/') + 1);

            return new FTPFileInfo(path, fileName);

        } catch (Exception e) {
            log.error("流量平台-----> 解析FTP URL失败: {}", maskPassword(ftpUrl), e);
            throw new IllegalArgumentException("无效的FTP URL格式", e);
        }
    }

    /**
     * 屏蔽URL中的密码信息
     */
    private String maskPassword(String ftpUrl) {
        if (ftpUrl == null) {
            return null;
        }
        return ftpUrl.replaceAll("://[^:]+:[^@]+@", "://***:***@");
    }

    /**
     * FTP文件信息
     */
    private static class FTPFileInfo {
        private final String remotePath;
        private final String fileName;

        public FTPFileInfo(String remotePath, String fileName) {
            this.remotePath = remotePath;
            this.fileName = fileName;
        }

        public String getRemotePath() {
            return remotePath;
        }

        public String getFileName() {
            return fileName;
        }
    }
}
