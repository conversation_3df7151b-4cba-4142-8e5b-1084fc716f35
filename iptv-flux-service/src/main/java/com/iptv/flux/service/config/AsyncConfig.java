package com.iptv.flux.service.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.config
 * @className: AsyncConfig
 * @author: chiron
 * @description: 异步处理配置
 * @date: 2025/1/21 16:30
 * @version: 1.0
 */
@Configuration
@EnableAsync
@Slf4j
public class AsyncConfig {

    @Value("${user-group.data-import.async.core-pool-size:10}")
    private int dataImportCorePoolSize;

    @Value("${user-group.data-import.async.max-pool-size:20}")
    private int dataImportMaxPoolSize;

    @Value("${user-group.data-import.async.queue-capacity:500}")
    private int dataImportQueueCapacity;

    @Value("${user-group.data-import.async.keep-alive:300}")
    private int dataImportKeepAliveSeconds;

    /**
     * 数据导入异步执行器
     */
    @Bean("dataImportExecutor")
    public Executor dataImportExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数 - 增加以支持更多并发导入
        executor.setCorePoolSize(dataImportCorePoolSize);

        // 最大线程数 - 增加以处理大量数据
        executor.setMaxPoolSize(dataImportMaxPoolSize);

        // 队列容量 - 增加以缓冲更多任务
        executor.setQueueCapacity(dataImportQueueCapacity);

        // 线程保持活跃时间 - 增加以减少线程创建销毁开销
        executor.setKeepAliveSeconds(dataImportKeepAliveSeconds);

        // 线程名前缀
        executor.setThreadNamePrefix("DataImport-");

        // 拒绝策略：由调用线程处理
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 等待时间 - 增加以确保大数据量处理完成
        executor.setAwaitTerminationSeconds(600);

        executor.initialize();

        log.info("流量平台-----> 数据导入异步执行器初始化完成，核心线程数: {}，最大线程数: {}，队列容量: {}",
                dataImportCorePoolSize, dataImportMaxPoolSize, dataImportQueueCapacity);
        return executor;
    }
}
