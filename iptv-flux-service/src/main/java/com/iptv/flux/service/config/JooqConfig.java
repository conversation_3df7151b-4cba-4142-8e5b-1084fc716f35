package com.iptv.flux.service.config;

import lombok.extern.slf4j.Slf4j;
import org.jooq.ConnectionProvider;
import org.jooq.DSLContext;
import org.jooq.SQLDialect;
import org.jooq.conf.Settings;
import org.jooq.impl.DSL;
import org.jooq.impl.DataSourceConnectionProvider;
import org.jooq.impl.DefaultConfiguration;
import org.jooq.impl.DefaultExecuteListenerProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.TransactionAwareDataSourceProxy;

import javax.sql.DataSource;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.config
 * @className: JooqConfig
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 12:55
 * @version: 1.0
 */
@Configuration
@Slf4j
public class JooqConfig {

    @Bean
    public ConnectionProvider connectionProvider(DataSource dataSource) {
        log.info("配置jOOQ连接");
        return new DataSourceConnectionProvider(new TransactionAwareDataSourceProxy(dataSource));
    }

    @Bean
    public DSLContext dslContext(ConnectionProvider connectionProvider) {
        log.info("使用MySQL配置jOOQ DSLContext");

        DefaultConfiguration configuration = new DefaultConfiguration();
        configuration.set(connectionProvider);
        configuration.set(SQLDialect.MYSQL);

        // 性能优化设置
        Settings settings = new Settings()
                .withRenderFormatted(false)
                .withExecuteLogging(false)
                .withFetchSize(1000);

        configuration.set(settings);

        // 添加执行监听器以记录和度量
        configuration.set(new DefaultExecuteListenerProvider(new JooqExecuteListener()));

        return DSL.using(configuration);
    }
}
