package com.iptv.flux.service.controller;

import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.dto.*;
import com.iptv.flux.common.swagger.SwaggerAnnotations;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.service.BlacklistService;
import com.iptv.flux.service.service.GroupInfoService;
import com.iptv.flux.service.service.UserGroupService;
import com.iptv.flux.service.service.WhitelistService;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.ratelimiter.annotation.RateLimiter;
import io.github.resilience4j.timelimiter.annotation.TimeLimiter;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.controller
 * @className: UserGroupController
 * @author: chiron
 * @description: 用户分组控制器
 * @date: 2025/2/28 12:58
 * @version: 1.0
 */
@RestController
@RequestMapping(UserGroupConstants.API_BASE_PATH)
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "用户分组管理", description = "用户分组查询与管理接口")
public class UserGroupController {

    private final UserGroupService userGroupService;
    private final GroupInfoService groupInfoService;
    private final BlacklistService blacklistService;
    private final WhitelistService whitelistService;
    private final MetricsRegistryUtil metricsRegistry;

    /**
     * 获取用户分组信息
     *
     * @param source 请求来源
     * @param userId 用户ID
     * @return 包含分组ID集合的结果
     */
    @GetMapping(value = "/{source}/{userId}", produces = MediaType.APPLICATION_JSON_VALUE)
    @CircuitBreaker(name = UserGroupConstants.CIRCUIT_BREAKER_NAME, fallbackMethod = "getUserGroupFallback")
    @TimeLimiter(name = UserGroupConstants.CIRCUIT_BREAKER_NAME)
    @RateLimiter(name = UserGroupConstants.RATE_LIMITER_NAME)
    @Timed(value = "api.usergroup.get", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取用户分组信息",
            description = "根据来源和用户ID获取该用户所属的所有分组ID",
            responseType = Set.class
    )
    public CompletableFuture<ResultDTO<Set<String>>> getUserGroup(
            @PathVariable @NotBlank String source,
            @PathVariable @NotBlank String userId,
            HttpServletRequest request) {

        String clientIp = getClientIp(request);
        long startTime = System.currentTimeMillis();
        log.info("流量平台-----> 收到获取用户分组请求。来源: {}, 用户ID: {}, 客户端IP: {}",
                source, userId, clientIp);

        metricsRegistry.incrementCounter(UserGroupConstants.QUERY_TOTAL_METRIC);

        return CompletableFuture.supplyAsync(() -> {
            try {
                Set<String> groups = userGroupService.loadUserGroupInfo(source, userId, clientIp);

                long executionTime = System.currentTimeMillis() - startTime;
                log.info("流量平台-----> 找到来源: {}, 用户ID: {}, 客户端IP: {} 的 {} 个分组，耗时 {}ms",
                        source, userId, clientIp, groups.size(), executionTime);

                return ResultDTO.success(groups);
            } catch (Exception e) {
                log.error("流量平台-----> 加载来源: {}, 用户ID: {}, 客户端IP: {} 的用户分组出错",
                        source, userId, clientIp, e);
                metricsRegistry.incrementCounter(UserGroupConstants.QUERY_ERROR_METRIC);
                throw e;
            }
        });
    }

    /**
     * 获取详细的用户分组信息，包含分组详情
     *
     * @param source 请求来源
     * @param userId 用户ID
     * @return 包含分组ID到分组信息映射的结果
     */
    @GetMapping(value = "/{source}/{userId}/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    @CircuitBreaker(name = UserGroupConstants.CIRCUIT_BREAKER_NAME, fallbackMethod = "getUserGroupDetailFallback")
    @TimeLimiter(name = UserGroupConstants.CIRCUIT_BREAKER_NAME)
    @RateLimiter(name = UserGroupConstants.RATE_LIMITER_NAME)
    @Timed(value = "api.usergroup.detail.get", percentiles = {0.5, 0.95, 0.99})
    public CompletableFuture<ResultDTO<Map<String, GroupInfoDTO>>> getUserGroupDetail(
            @PathVariable @NotBlank String source,
            @PathVariable @NotBlank String userId,
            HttpServletRequest request) {

        String clientIp = getClientIp(request);
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 收到获取详细用户分组请求，来源: {}, 用户ID: {}, IP: {}",
                source, userId, clientIp);

        metricsRegistry.incrementCounter(UserGroupConstants.QUERY_TOTAL_METRIC, "detail", "true");

        return CompletableFuture.supplyAsync(() -> {
            try {
                Set<String> groupIds = userGroupService.loadUserGroupInfo(source, userId, clientIp);

                if (groupIds.isEmpty()) {
                    log.debug("流量平台-----> 来源: {}, 用户ID: {} 未找到分组", source, userId);
                    return ResultDTO.success(Collections.emptyMap());
                }

                Map<String, GroupInfoDTO> groupDetails = groupInfoService.getGroupInfoBatch(List.copyOf(groupIds));

                log.debug("流量平台-----> 来源: {}, 用户ID: {} 找到 {} 个分组详情，耗时 {}ms",
                        source, userId, groupDetails.size(), System.currentTimeMillis() - startTime);

                return ResultDTO.success(groupDetails);
            } catch (Exception e) {
                log.error("流量平台-----> 加载来源: {}, 用户ID: {} 的详细用户分组出错", source, userId, e);
                metricsRegistry.incrementCounter(UserGroupConstants.QUERY_ERROR_METRIC, "detail", "true");
                throw e;
            }
        });
    }

    /**
     * 保存用户分组信息
     *
     * @param userGroupDTO 用户分组数据
     * @return 成功响应
     */
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.save", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.SaveOperation(
            summary = "保存用户分组信息",
            description = "保存用户与分组的关联关系"
    )
    public ResultDTO<String> saveUserGroup(@RequestBody @Valid UserGroupDTO userGroupDTO) {
        log.info("流量平台-----> 正在保存用户ID: {}, 来源: {}, 分组数: {} 的用户分组",
                userGroupDTO.getUserId(), userGroupDTO.getSource(), userGroupDTO.getGroupIds().size());

        userGroupService.saveUserGroupInfo(userGroupDTO);
        return ResultDTO.success("操作成功");
    }

    /**
     * 保存分组信息
     *
     * @param groupInfoDTO 分组信息数据
     * @return 成功响应
     */
    @PostMapping(value = "/group", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.group.save", percentiles = {0.5, 0.95, 0.99})
    public ResultDTO<String> saveGroupInfo(@RequestBody @Valid GroupInfoDTO groupInfoDTO) {
        log.info("流量平台-----> 正在保存分组ID: {}, 策略ID: {} 的分组信息",
                groupInfoDTO.getGroupId(), groupInfoDTO.getStrategyId());

        groupInfoService.saveGroupInfo(groupInfoDTO);
        return ResultDTO.success("操作成功");
    }



    /**
     * getUserGroup的回退方法
     */
    public CompletableFuture<ResultDTO<Set<String>>> getUserGroupFallback(
            String source, String userId, Throwable ex) {
        log.error("流量平台-----> getUserGroup回退，来源: {}, 用户ID: {}", source, userId, ex);
        metricsRegistry.incrementCounter("fallback.usergroup");
        return CompletableFuture.completedFuture(ResultDTO.success(Collections.emptySet()));
    }

    /**
     * getUserGroupDetail的回退方法
     */
    public CompletableFuture<ResultDTO<Map<String, GroupInfoDTO>>> getUserGroupDetailFallback(
            String source, String userId, Throwable ex) {
        log.error("流量平台-----> getUserGroupDetail回退，来源: {}, 用户ID: {}", source, userId, ex);
        metricsRegistry.incrementCounter("fallback.usergroup.detail");
        return CompletableFuture.completedFuture(ResultDTO.success(Collections.emptyMap()));
    }

    // 添加获取客户端IP的辅助方法
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 如果是多级代理，取第一个IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        return ip;
    }

    /** 20250526 add controller **/

    /**
     * 获取用户分组列表（分页）
     */
    @GetMapping(value = "/list", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.list", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取用户分组列表",
            description = "分页获取用户分组列表，可选择按来源和用户ID筛选",
            responseType = Map.class
    )
    public ResultDTO<Map<String, Object>> getUserGroupList(
            @RequestParam(required = false) String source,
            @RequestParam(required = false) String userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize) {

        log.info("流量平台-----> 收到获取用户分组列表请求。来源: {}, 用户ID: {}, 页码: {}, 每页大小: {}",
                source, userId, page, pageSize);

        // 参数校验
        if (page < 1) {
            page = 1;
        }
        if (pageSize < 1 || pageSize > 100) {
            pageSize = 10;
        }

        try {
            Map<String, Object> result = userGroupService.getUserGroupList(source, userId, page, pageSize);
            return ResultDTO.success(result);
        } catch (Exception e) {
            log.error("流量平台-----> 获取用户分组列表出错", e);
            metricsRegistry.incrementCounter(UserGroupConstants.QUERY_ERROR_METRIC);
            return ResultDTO.fail("获取用户分组列表失败：" + e.getMessage());
        }
    }


    /**
     * 删除用户分组
     */
    @DeleteMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.delete", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "删除用户分组",
            description = "根据ID删除用户分组关系",
            responseType = String.class
    )
    public ResultDTO<String> deleteUserGroup(@PathVariable Long id) {
        log.info("流量平台-----> 收到删除用户分组请求。ID: {}", id);

        try {
            userGroupService.deleteUserGroup(id);
            return ResultDTO.success("删除成功");
        } catch (Exception e) {
            log.error("流量平台-----> 删除用户分组出错，ID: {}", id, e);
            return ResultDTO.fail("删除用户分组失败：" + e.getMessage());
        }
    }




    /**
     * 根据条件查询分组信息
     *
     * @param request 查询条件
     * @return 分组列表
     */
    @PostMapping(value = "/groups/query", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.groups.queryByConditions", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "根据条件查询分组信息",
            description = "根据分组名称、策略ID、平台、来源、激活状态、覆盖度、生成时间等条件查询分组信息，所有参数为空时返回全部分组",
            responseType = List.class
    )
    public ResultDTO<Map<String, Object>> queryGroups(@RequestBody GroupQueryRequestDTO request) {
        log.info("流量平台-----> 收到条件查询分组请求，分组名称: {}, 策略ID: {}, 平台: {}, 来源: {}, 激活状态: {}",
                request.getGroupName(), request.getStrategyId(), request.getPlatform(), request.getSource(), request.getActive());

        try {
            Map<String, Object> result = groupInfoService.queryGroups(request);

            log.info("流量平台-----> 条件查询分组成功，结果数量: {}", result.get("total"));
            return ResultDTO.success(result);

        } catch (Exception e) {
            log.error("流量平台-----> 条件查询分组失败", e);
            return ResultDTO.fail("查询分组信息失败：" + e.getMessage());
        }
    }

    // ==================== 黑名单管理接口 ====================

    /**
     * 获取黑名单列表
     */
    @PostMapping(value = "/blacklist", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.QueryOperation(
            summary = "获取黑名单列表",
            description = "分页查询黑名单用户列表",
            responseType = PagedResponseDTO.class
    )
    public ResultDTO<PagedResponseDTO<BlacklistUserDTO>> getBlacklist(
            @Valid @RequestBody ListQueryRequestDTO request) {
        try {
            log.info("流量平台-----> 查询黑名单列表，页码: {}, 每页: {}", request.getPage(), request.getPageSize());

            PagedResponseDTO<BlacklistUserDTO> result = blacklistService.findPage(request);

            log.info("流量平台-----> 查询黑名单列表成功，总数: {}", result.getTotal());
            return ResultDTO.success(result);

        } catch (Exception e) {
            log.error("流量平台-----> 查询黑名单列表失败", e);
            return ResultDTO.fail("查询黑名单列表失败: " + e.getMessage());
        }
    }

    /**
     * 添加黑名单用户
     */
    @PostMapping(value = "/blacklist/add", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.SaveOperation(
            summary = "添加黑名单用户",
            description = "将用户添加到黑名单"
    )
    public ResultDTO<String> addBlacklist(@Valid @RequestBody BlacklistUserDTO dto) {
        try {
            log.info("流量平台-----> 添加黑名单用户: {}, 来源: {}", dto.getUserId(), dto.getSource());

            blacklistService.addUser(dto);

            log.info("流量平台-----> 添加黑名单用户成功: {}", dto.getUserId());
            return ResultDTO.success("添加黑名单用户成功");

        } catch (Exception e) {
            log.error("流量平台-----> 添加黑名单用户失败: {}", dto.getUserId(), e);
            return ResultDTO.fail("添加黑名单用户失败: " + e.getMessage());
        }
    }

    /**
     * 删除黑名单用户
     */
    @DeleteMapping(value = "/blacklist/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.SaveOperation(
            summary = "删除黑名单用户",
            description = "从黑名单中删除用户"
    )
    public ResultDTO<String> deleteBlacklist(@PathVariable("id") String id) {
        try {
            log.info("流量平台-----> 删除黑名单用户: {}", id);

            blacklistService.deleteUser(id);

            log.info("流量平台-----> 删除黑名单用户成功: {}", id);
            return ResultDTO.success("删除黑名单用户成功");

        } catch (Exception e) {
            log.error("流量平台-----> 删除黑名单用户失败: {}", id, e);
            return ResultDTO.fail("删除黑名单用户失败: " + e.getMessage());
        }
    }

    // ==================== 白名单管理接口 ====================

    /**
     * 获取白名单列表
     */
    @PostMapping(value = "/whitelist", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.QueryOperation(
            summary = "获取白名单列表",
            description = "分页查询白名单用户列表",
            responseType = PagedResponseDTO.class
    )
    public ResultDTO<PagedResponseDTO<WhitelistUserDTO>> getWhitelist(
            @Valid @RequestBody ListQueryRequestDTO request) {
        try {
            log.info("流量平台-----> 查询白名单列表，页码: {}, 每页: {}", request.getPage(), request.getPageSize());

            PagedResponseDTO<WhitelistUserDTO> result = whitelistService.findPage(request);

            log.info("流量平台-----> 查询白名单列表成功，总数: {}", result.getTotal());
            return ResultDTO.success(result);

        } catch (Exception e) {
            log.error("流量平台-----> 查询白名单列表失败", e);
            return ResultDTO.fail("查询白名单列表失败: " + e.getMessage());
        }
    }

    /**
     * 添加白名单用户
     */
    @PostMapping(value = "/whitelist/add", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.SaveOperation(
            summary = "添加白名单用户",
            description = "将用户添加到白名单"
    )
    public ResultDTO<String> addWhitelist(@Valid @RequestBody WhitelistUserDTO dto) {
        try {
            log.info("流量平台-----> 添加白名单用户: {}, 来源: {}", dto.getUserId(), dto.getSource());

            whitelistService.addUser(dto);

            log.info("流量平台-----> 添加白名单用户成功: {}", dto.getUserId());
            return ResultDTO.success("添加白名单用户成功");

        } catch (Exception e) {
            log.error("流量平台-----> 添加白名单用户失败: {}", dto.getUserId(), e);
            return ResultDTO.fail("添加白名单用户失败: " + e.getMessage());
        }
    }

    /**
     * 删除白名单用户
     */
    @DeleteMapping(value = "/whitelist/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.SaveOperation(
            summary = "删除白名单用户",
            description = "从白名单中删除用户"
    )
    public ResultDTO<String> deleteWhitelist(@PathVariable("id") String id) {
        try {
            log.info("流量平台-----> 删除白名单用户: {}", id);

            whitelistService.deleteUser(id);

            log.info("流量平台-----> 删除白名单用户成功: {}", id);
            return ResultDTO.success("删除白名单用户成功");

        } catch (Exception e) {
            log.error("流量平台-----> 删除白名单用户失败: {}", id, e);
            return ResultDTO.fail("删除白名单用户失败: " + e.getMessage());
        }
    }

    /**
     * 转换前端请求格式为内部请求格式
     */
    private ListQueryRequestDTO convertToListQueryRequest(Map<String, Object> frontendRequest) {
        return ListQueryRequestDTO.builder()
                .userId((String) frontendRequest.get("userId"))
                .source((String) frontendRequest.get("source"))
                .page(frontendRequest.get("page") != null ?
                      Integer.valueOf(frontendRequest.get("page").toString()) : 1)
                .pageSize(frontendRequest.get("pageSize") != null ?
                         Integer.valueOf(frontendRequest.get("pageSize").toString()) : 10)
                .build();
    }

}