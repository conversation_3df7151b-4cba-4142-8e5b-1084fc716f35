package com.iptv.flux.service.config;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.iptv.flux.common.constants.UserGroupConstants;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBloomFilter;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.config
 * @className: CacheConfig
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 12:55
 * @version: 1.0
 */
@Configuration
@Slf4j
public class CacheConfig {

    @Value("${cache.caffeine.initial-capacity:50000}")
    private int initialCapacity;

    @Value("${cache.caffeine.maximum-size:500000}")
    private int maximumSize;

    @Value("${cache.caffeine.expire-after-write:600}")
    private int expireAfterWrite;

    @Value("${cache.bloom-filter.expected-insertions:10000000}")
    private long expectedInsertions;

    @Value("${cache.bloom-filter.false-probability:0.001}")
    private double falseProbability;

    @Value("${user-group.security.rate-limit.limit-per-second:100}")
    private int limitPerSecond;

    @Value("${user-group.security.rate-limit.timeout-ms:100}")
    private int timeoutMs;

    @Value("${user-group.security.missing-keys-cache.size:10000}")
    private int missingKeysCacheSize;

    @Value("${user-group.security.missing-keys-cache.expire-minutes:5}")
    private int missingKeysCacheExpireMinutes;

    @Bean
    public Cache<String, Set<String>> localCache() {
        long startTime = System.currentTimeMillis();
        log.info("流量平台-----> 开始初始化Caffeine缓存，容量: {}, 最大大小: {}, 写入后过期时间: {}秒",
                initialCapacity, maximumSize, expireAfterWrite);

        Cache<String, Set<String>> cache = Caffeine.newBuilder()
                .initialCapacity(initialCapacity)
                .maximumSize(maximumSize)
                .expireAfterWrite(expireAfterWrite, TimeUnit.SECONDS)
                .recordStats()
                .build();

        long endTime = System.currentTimeMillis();
        log.info("流量平台-----> Caffeine缓存初始化完成，耗时: {}ms", endTime - startTime);
        return cache;
    }

    @Bean
    public RBloomFilter<String> bloomFilter(RedissonClient redissonClient) {
        long startTime = System.currentTimeMillis();
        log.info("流量平台-----> 开始初始化布隆过滤器，预期插入量: {}, 误判率: {}",
                expectedInsertions, falseProbability);

        RBloomFilter<String> bloomFilter = redissonClient.getBloomFilter(
                UserGroupConstants.BLOOM_FILTER_KEY);

        boolean initialized = bloomFilter.tryInit(expectedInsertions, falseProbability);
        long endTime = System.currentTimeMillis();
        log.info("流量平台-----> 布隆过滤器初始化: {}，耗时: {}ms",
                initialized ? "成功" : "过滤器已存在", endTime - startTime);

        return bloomFilter;
    }

    /**
     * 创建缓存穿透请求限流器
     */
    @Bean
    public RateLimiter cachePenetrationRateLimiter(RateLimiterRegistry rateLimiterRegistry) {
        RateLimiterConfig config = RateLimiterConfig.custom()
                .limitRefreshPeriod(Duration.ofSeconds(1))
                .limitForPeriod(limitPerSecond)
                .timeoutDuration(Duration.ofMillis(timeoutMs))
                .build();

        log.info("配置缓存穿透限流器: 每秒{}个请求, 超时: {}ms",
                limitPerSecond, timeoutMs);

        return rateLimiterRegistry.rateLimiter("cachePenetration", config);
    }

    /**
     * 创建已知不存在键的缓存
     */
    @Bean
    public LoadingCache<String, Boolean> knownMissingKeysCache() {
        log.info("配置已知不存在键缓存，大小: {}, 过期时间: {}分钟",
                missingKeysCacheSize, missingKeysCacheExpireMinutes);

        return Caffeine.newBuilder()
                .maximumSize(missingKeysCacheSize)
                .expireAfterWrite(missingKeysCacheExpireMinutes, TimeUnit.MINUTES)
                .build(key -> false);
    }
}
