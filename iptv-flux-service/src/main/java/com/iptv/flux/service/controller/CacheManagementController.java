package com.iptv.flux.service.controller;

import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.dto.ResultDTO;
import com.iptv.flux.service.schedule.CacheWarmupJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(UserGroupConstants.API_CACHE_PATH)
@RequiredArgsConstructor
@Slf4j
public class CacheManagementController {

    private final Scheduler scheduler;
    private final JobDetail cacheWarmupJobDetail;

    @PostMapping("/warmup")
    public ResultDTO<String> triggerWarmup(@RequestBody Map<String, Object> requestBody) {
        // 从JSON请求体中获取fullLoad参数
        boolean fullLoad = false;
        if (requestBody != null && requestBody.containsKey("fullLoad")) {
            Object fullLoadValue = requestBody.get("fullLoad");
            if (fullLoadValue instanceof Boolean) {
                fullLoad = (Boolean) fullLoadValue;
            } else if (fullLoadValue instanceof String) {
                fullLoad = Boolean.parseBoolean((String) fullLoadValue);
            }
        }
        try {
            JobDataMap dataMap = new JobDataMap();
            dataMap.put("fullLoad", fullLoad);
            dataMap.put("manualTrigger", true);

            String jobId = "manualWarmupJob" + System.currentTimeMillis();
            String triggerId = "manualWarmupTrigger" + System.currentTimeMillis();

            JobDetail jobDetail = JobBuilder.newJob(CacheWarmupJob.class)
                    .withIdentity(jobId)
                    .usingJobData(dataMap)
                    .storeDurably(false)  // 不持久化存储
                    .build();

            // 创建一次性触发器，执行完自动删除
            Trigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(triggerId)
                    .forJob(jobDetail)
                    .startNow()
                    .withSchedule(SimpleScheduleBuilder.simpleSchedule()
                            .withRepeatCount(0))  // 只执行一次
                    .build();

            scheduler.scheduleJob(jobDetail, trigger);

            log.info("流量平台-----> 手动缓存预热已触发，fullLoad: {}, 任务ID: {}, 触发器ID: {}",
                    fullLoad, jobId, triggerId);

            String message = String.format("Cache warmup triggered successfully. Job ID: %s, FullLoad: %s",
                    jobId, fullLoad);
            return ResultDTO.success(message);
        } catch (Exception e) {
            log.error("流量平台-----> 触发缓存预热失败", e);
            return ResultDTO.fail("Failed to trigger cache warmup: " + e.getMessage());
        }
    }

    @GetMapping("/status")
    public ResultDTO<Map<String, Object>> getStatus() {
        try {
            Map<String, Object> status = new HashMap<>();

            // 获取Quartz调度器状态
            status.put("schedulerRunning", !scheduler.isInStandbyMode());
            status.put("schedulerName", scheduler.getSchedulerName());

            // 获取任务信息
            status.put("warmupJobExists", scheduler.checkExists(cacheWarmupJobDetail.getKey()));

            // 获取触发器信息
            Trigger trigger = scheduler.getTrigger(TriggerKey.triggerKey("cacheWarmupTrigger"));
            if (trigger != null) {
                status.put("nextFireTime", trigger.getNextFireTime());
                status.put("previousFireTime", trigger.getPreviousFireTime());

                if (trigger instanceof CronTrigger) {
                    status.put("cronExpression", ((CronTrigger) trigger).getCronExpression());
                }
            }

            return ResultDTO.success(status);
        } catch (Exception e) {
            log.error("获取缓存状态失败", e);
            return ResultDTO.fail("Failed to get cache status: " + e.getMessage());
        }
    }

    @PostMapping("/pause")
    public ResultDTO<String> pauseScheduler() {
        try {
            scheduler.standby();
            log.info("缓存预热调度器暂停");
            return ResultDTO.success("Cache warmup scheduler paused successfully");
        } catch (Exception e) {
            log.error("暂停缓存预热调度失败", e);
            return ResultDTO.fail("Failed to pause scheduler: " + e.getMessage());
        }
    }

    @PostMapping("/resume")
    public ResultDTO<String> resumeScheduler() {
        try {
            scheduler.start();
            log.info("缓存预热调度器恢复");
            return ResultDTO.success("Cache warmup scheduler resumed successfully");
        } catch (Exception e) {
            log.error("恢复缓存预热调度器失败", e);
            return ResultDTO.fail("Failed to resume scheduler: " + e.getMessage());
        }
    }

    /**
     * 清理过期的手动任务和触发器
     */
    @PostMapping("/cleanup")
    public ResultDTO<String> cleanupExpiredJobs() {
        try {
            int cleanedJobs = 0;
            int cleanedTriggers = 0;

            // 获取所有任务
            for (String groupName : scheduler.getJobGroupNames()) {
                for (JobKey jobKey : scheduler.getJobKeys(GroupMatcher.jobGroupEquals(groupName))) {
                    String jobName = jobKey.getName();

                    // 清理手动创建的过期任务
                    if (jobName.startsWith("manualWarmupJob")) {
                        try {
                            // 检查任务是否还在运行
                            if (!scheduler.getCurrentlyExecutingJobs().stream()
                                    .anyMatch(context -> context.getJobDetail().getKey().equals(jobKey))) {

                                scheduler.deleteJob(jobKey);
                                cleanedJobs++;
                                log.debug("流量平台-----> 清理过期手动任务: {}", jobName);
                            }
                        } catch (Exception e) {
                            log.warn("流量平台-----> 清理任务失败: {}", jobName, e);
                        }
                    }
                }
            }

            // 获取所有触发器
            for (String groupName : scheduler.getTriggerGroupNames()) {
                for (TriggerKey triggerKey : scheduler.getTriggerKeys(GroupMatcher.triggerGroupEquals(groupName))) {
                    String triggerName = triggerKey.getName();

                    // 清理手动创建的过期触发器
                    if (triggerName.startsWith("manualWarmupTrigger")) {
                        try {
                            Trigger trigger = scheduler.getTrigger(triggerKey);
                            if (trigger != null && trigger.mayFireAgain() == false) {
                                scheduler.unscheduleJob(triggerKey);
                                cleanedTriggers++;
                                log.debug("流量平台-----> 清理过期手动触发器: {}", triggerName);
                            }
                        } catch (Exception e) {
                            log.warn("流量平台-----> 清理触发器失败: {}", triggerName, e);
                        }
                    }
                }
            }

            String message = String.format("清理完成，删除任务: %d 个，删除触发器: %d 个", cleanedJobs, cleanedTriggers);
            log.info("流量平台-----> {}", message);
            return ResultDTO.success(message);

        } catch (Exception e) {
            log.error("流量平台-----> 清理过期任务失败", e);
            return ResultDTO.fail("Failed to cleanup expired jobs: " + e.getMessage());
        }
    }
}