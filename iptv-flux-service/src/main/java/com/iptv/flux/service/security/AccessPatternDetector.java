package com.iptv.flux.service.security;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;

/**
 * 访问模式检测器，用于识别可能的缓存穿透攻击
 */
@Component
@Slf4j
public class AccessPatternDetector {
    private final LoadingCache<String, AtomicInteger> ipAccessCounter;
    private final LoadingCache<String, AtomicInteger> keyAccessCounter;
    private final Set<Pattern> knownPatterns = new HashSet<>();

    @Value("${user-group.security.ip-threshold:100}")
    private int ipThreshold;

    @Value("${user-group.security.patterns:source\\\\d+:user\\\\d+,app\\\\d+:user\\\\d+}")
    private String patternConfig;

    public AccessPatternDetector() {
        // 初始化计数器缓存，记录IP和键的访问频率
        this.ipAccessCounter = Caffeine.newBuilder()
                .expireAfterWrite(5, TimeUnit.MINUTES)
                .build(key -> new AtomicInteger(0));

        this.keyAccessCounter = Caffeine.newBuilder()
                .expireAfterWrite(1, TimeUnit.MINUTES)
                .build(key -> new AtomicInteger(0));
    }

    @PostConstruct
    public void init() {
        // 从配置加载已知的合法访问模式
        String[] patterns = patternConfig.split(",");
        for (String pattern : patterns) {
            knownPatterns.add(Pattern.compile(pattern));
            log.info("Loaded known access pattern: {}", pattern);
        }
    }

    /**
     * 判断请求是否可疑
     * @param key 请求的键
     * @param clientIp 客户端IP
     * @return 是否为可疑请求
     */
    public boolean isSuspicious(String key, String clientIp) {
        // 1. 检查已知合法模式
        if (matchesKnownPattern(key)) {
            return false;
        }

        // 2. 检查访问频率
        int ipCount = ipAccessCounter.get(clientIp).incrementAndGet();
        int keyCount = keyAccessCounter.get(key).incrementAndGet();

        // 3. 综合判断
        if (ipCount > ipThreshold && keyCount <= 3) {
            // 同一IP短时间内查询大量不同的键
            log.warn("流量平台-----> 检测到IP: {} (计数: {})对键: {} (计数: {})的可疑访问",
                    clientIp, ipCount, key, keyCount);
            return true;
        }

        return false;
    }

    /**
     * 判断键是否符合已知业务模式
     * @param key
     * @return
     */
    private boolean matchesKnownPattern(String key) {
        // 判断键是否符合已知业务模式
        return knownPatterns.stream().anyMatch(pattern -> pattern.matcher(key).matches());
    }
}