package com.iptv.flux.service.service;

import com.github.benmanes.caffeine.cache.Cache;
import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import io.micrometer.core.instrument.Meter;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBloomFilter;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.service
 * @className: MonitoringService
 * @author: chiron
 * @description: 监控指标服务
 * @date: 2025/1/21 16:00
 * @version: 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MonitoringService {

    private final MetricsRegistryUtil metricsRegistry;
    private final MeterRegistry meterRegistry;
    private final RBloomFilter<String> bloomFilter;
    private final StringRedisTemplate redisTemplate;
    private final Cache<String, Set<String>> localCache;

    /**
     * 获取系统健康状态
     */
    public Map<String, Object> getHealthStatus() {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始获取系统健康状态");

        try {
            Map<String, Object> healthStatus = new HashMap<>();

            // 1. 服务可用性指标
            double errorRate = calculateErrorRate();
            String status = errorRate < 0.01 ? "健康" : (errorRate < 0.05 ? "警告" : "异常");

            healthStatus.put("status", status);
            healthStatus.put("errorRate", Math.round(errorRate * 10000.0) / 100.0); // 转换为百分比

            // 2. 缓存健康状态
            double cacheHitRate = calculateCacheHitRate();
            String cacheStatus = cacheHitRate > 0.95 ? "健康" : (cacheHitRate > 0.85 ? "需优化" : "异常");

            healthStatus.put("cacheStatus", cacheStatus);
            healthStatus.put("cacheHitRate", Math.round(cacheHitRate * 100.0) / 100.0);

            // 3. 响应时间健康状态
            double avgResponseTime = metricsRegistry.getTimerMean(UserGroupConstants.API_USERGROUP_GET_TIMER);
            double p99ResponseTime = metricsRegistry.getTimerPercentile(UserGroupConstants.API_USERGROUP_GET_TIMER, 0.99);

            String responseTimeStatus = avgResponseTime < 50 && p99ResponseTime < 200 ?
                    "健康" : (avgResponseTime < 100 && p99ResponseTime < 500 ? "需优化" : "异常");

            healthStatus.put("responseTimeStatus", responseTimeStatus);
            healthStatus.put("avgResponseTime", Math.round(avgResponseTime * 100.0) / 100.0);
            healthStatus.put("p99ResponseTime", Math.round(p99ResponseTime * 100.0) / 100.0);

            // 4. 系统负载指标
            healthStatus.put("totalRequestsLast5Min",
                    metricsRegistry.getCounterValueForLast(UserGroupConstants.QUERY_TOTAL_METRIC, TimeUnit.MINUTES, 5));

            log.debug("流量平台-----> 获取系统健康状态完成，状态: {}，耗时 {}ms", 
                    status, System.currentTimeMillis() - startTime);

            return healthStatus;
        } catch (Exception e) {
            log.error("流量平台-----> 获取系统健康状态失败", e);
            throw e;
        }
    }

    /**
     * 获取系统关键指标
     */
    public Map<String, Object> getSystemMetrics() {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始获取系统监控指标");

        try {
            Map<String, Object> metrics = new HashMap<>();

            // 1. 请求统计指标
            double totalRequests = getCounterValue(UserGroupConstants.QUERY_TOTAL_METRIC);
            double errorRequests = getCounterValue(UserGroupConstants.QUERY_ERROR_METRIC);
            
            metrics.put("totalRequests", totalRequests);
            metrics.put("errorRequests", errorRequests);
            metrics.put("errorRate", calculateRate(errorRequests, totalRequests));

            // 2. 缓存命中指标
            metrics.put("localCacheHits", getCounterValue(UserGroupConstants.CACHE_HIT_METRIC));
            metrics.put("redisCacheHits", getCounterValue(UserGroupConstants.REDIS_HIT_METRIC));
            metrics.put("cacheMisses", getCounterValue(UserGroupConstants.CACHE_MISS_METRIC));
            metrics.put("cacheHitRate", calculateCacheHitRate());

            // 3. 布隆过滤器指标
            metrics.put("bloomFilterSize", bloomFilter.count());
            metrics.put("bloomFilterHits", getCounterValue(UserGroupConstants.BLOOM_FILTER_HIT_METRIC));

            // 4. 缓存预热指标
            String coverageStr = redisTemplate.opsForValue().get(UserGroupConstants.CACHE_WARMUP_COVERAGE_KEY);
            double coverage = coverageStr != null ? Double.parseDouble(coverageStr) : 0.0;
            metrics.put("cacheWarmupCoverage", coverage);

            // 5. 响应时间指标
            metrics.put("averageResponseTime", getTimerMean(UserGroupConstants.API_USERGROUP_GET_TIMER));
            metrics.put("p95ResponseTime", getTimerPercentile(UserGroupConstants.API_USERGROUP_GET_TIMER, 0.95));
            metrics.put("p99ResponseTime", getTimerPercentile(UserGroupConstants.API_USERGROUP_GET_TIMER, 0.99));

            log.debug("流量平台-----> 获取系统监控指标完成，包含 {} 个指标，耗时 {}ms", 
                    metrics.size(), System.currentTimeMillis() - startTime);

            return metrics;
        } catch (Exception e) {
            log.error("流量平台-----> 获取系统监控指标失败", e);
            throw e;
        }
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始获取缓存统计信息");

        try {
            Map<String, Object> cacheStats = new HashMap<>();

            // 1. 本地缓存统计
            cacheStats.put("localCacheSize", localCache.estimatedSize());
            cacheStats.put("localCacheHitRate", Math.round(localCache.stats().hitRate() * 10000.0) / 100.0);
            cacheStats.put("localCacheMissRate", Math.round(localCache.stats().missRate() * 10000.0) / 100.0);
            cacheStats.put("localCacheEvictions", localCache.stats().evictionCount());

            // 2. Redis缓存统计
            cacheStats.put("redisHits", getCounterValue(UserGroupConstants.REDIS_HIT_METRIC));
            cacheStats.put("redisMisses", getCounterValue(UserGroupConstants.CACHE_MISS_METRIC)
                    - getCounterValue(UserGroupConstants.CACHE_HIT_METRIC));

            // 3. 布隆过滤器统计
            cacheStats.put("bloomFilterSize", bloomFilter.count());
            cacheStats.put("bloomFilterHits", getCounterValue(UserGroupConstants.BLOOM_FILTER_HIT_METRIC));
            cacheStats.put("bloomFilterCapacity", bloomFilter.getSize());
            cacheStats.put("bloomFilterErrorRate", Math.round(bloomFilter.getFalseProbability() * 10000.0) / 100.0);

            log.debug("流量平台-----> 获取缓存统计信息完成，本地缓存大小: {}，耗时 {}ms", 
                    localCache.estimatedSize(), System.currentTimeMillis() - startTime);

            return cacheStats;
        } catch (Exception e) {
            log.error("流量平台-----> 获取缓存统计信息失败", e);
            throw e;
        }
    }

    /**
     * 获取性能指标信息
     */
    public Map<String, Object> getPerformanceMetrics() {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始获取性能指标信息");

        try {
            Map<String, Object> performanceMetrics = new HashMap<>();

            // 1. API性能指标
            Map<String, Object> apiMetrics = new HashMap<>();
            apiMetrics.put("getUserGroup", collectTimerStats(UserGroupConstants.API_USERGROUP_GET_TIMER));
            apiMetrics.put("getUserGroupDetail", collectTimerStats(UserGroupConstants.API_USERGROUP_DETAIL_GET_TIMER));
            apiMetrics.put("saveUserGroup", collectTimerStats(UserGroupConstants.API_USERGROUP_SAVE_TIMER));
            apiMetrics.put("saveGroupInfo", collectTimerStats(UserGroupConstants.API_GROUP_SAVE_TIMER));
            performanceMetrics.put("api", apiMetrics);

            // 2. 服务层性能指标
            Map<String, Object> serviceMetrics = new HashMap<>();
            serviceMetrics.put("loadUserGroup", collectTimerStats(UserGroupConstants.SERVICE_USERGROUP_LOAD_TIMER));
            serviceMetrics.put("saveUserGroup", collectTimerStats(UserGroupConstants.SERVICE_USERGROUP_SAVE_TIMER));
            serviceMetrics.put("getGroupInfo", collectTimerStats(UserGroupConstants.SERVICE_GROUPINFO_GETINFO_TIMER));
            serviceMetrics.put("getGroupInfoBatch", collectTimerStats(UserGroupConstants.SERVICE_GROUPINFO_BATCH_TIMER));
            performanceMetrics.put("service", serviceMetrics);

            // 3. 数据库性能指标
            Map<String, Object> dbMetrics = new HashMap<>();
            dbMetrics.put("queryUserGroup", collectTimerStats(UserGroupConstants.REPOSITORY_USERGROUP_QUERY_GROUPS_TIMER));
            dbMetrics.put("findGroupsByIds", collectTimerStats(UserGroupConstants.REPOSITORY_GROUP_FIND_BY_IDS_TIMER));
            dbMetrics.put("saveUserGroup", collectTimerStats(UserGroupConstants.REPOSITORY_USERGROUP_SAVE_TIMER));
            performanceMetrics.put("database", dbMetrics);

            // 4. 缓存预热性能
            Map<String, Object> warmupMetrics = new HashMap<>();
            warmupMetrics.put("duration", getTimerMean(UserGroupConstants.CACHE_WARMUP_DURATION_TIMER));
            warmupMetrics.put("executionTime", getTimerMean(UserGroupConstants.CACHE_WARMUP_EXECUTION_TIME_TIMER));
            warmupMetrics.put("processedCount", getCounterValue(UserGroupConstants.CACHE_WARMUP_PROCESSED_COUNT_METRIC));
            warmupMetrics.put("successCount", getCounterValue(UserGroupConstants.CACHE_WARMUP_SUCCESS_COUNT_METRIC));
            warmupMetrics.put("errorCount", getCounterValue(UserGroupConstants.CACHE_WARMUP_ERROR_COUNT_METRIC));
            performanceMetrics.put("cacheWarmup", warmupMetrics);

            log.debug("流量平台-----> 获取性能指标信息完成，包含 {} 个分类，耗时 {}ms", 
                    performanceMetrics.size(), System.currentTimeMillis() - startTime);

            return performanceMetrics;
        } catch (Exception e) {
            log.error("流量平台-----> 获取性能指标信息失败", e);
            throw e;
        }
    }

    /**
     * 获取所有监控指标的完整列表
     */
    public List<Map<String, Object>> getAllMetrics() {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始获取所有监控指标");

        try {
            List<Map<String, Object>> allMetrics = new ArrayList<>();

            // 遍历所有指标
            meterRegistry.getMeters().forEach(meter -> {
                try {
                    Map<String, Object> metricInfo = new HashMap<>();
                    metricInfo.put("name", meter.getId().getName());
                    metricInfo.put("type", meter.getId().getType().toString());

                    // 收集标签信息
                    List<Map<String, String>> tags = new ArrayList<>();
                    meter.getId().getTags().forEach(tag -> {
                        Map<String, String> tagMap = new HashMap<>();
                        tagMap.put(tag.getKey(), tag.getValue());
                        tags.add(tagMap);
                    });

                    metricInfo.put("tags", tags);

                    // 安全地获取指标值
                    try {
                        metricInfo.put("value", getSafeValue(meter));
                    } catch (Exception e) {
                        metricInfo.put("value", "N/A");
                        metricInfo.put("valueError", e.getMessage());
                    }

                    allMetrics.add(metricInfo);
                } catch (Exception e) {
                    log.warn("流量平台-----> 处理指标 {} 时出错: {}", meter.getId().getName(), e.getMessage());
                }
            });

            log.debug("流量平台-----> 获取所有监控指标完成，共 {} 个指标，耗时 {}ms", 
                    allMetrics.size(), System.currentTimeMillis() - startTime);

            return allMetrics;
        } catch (Exception e) {
            log.error("流量平台-----> 获取所有监控指标失败", e);
            throw e;
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 计算错误率
     */
    private double calculateErrorRate() {
        double totalRequests = metricsRegistry.getCounterValue(UserGroupConstants.QUERY_TOTAL_METRIC);
        double errorRequests = metricsRegistry.getCounterValue(UserGroupConstants.QUERY_ERROR_METRIC);

        return totalRequests > 0 ? errorRequests / totalRequests : 0;
    }

    /**
     * 计算缓存命中率
     */
    private double calculateCacheHitRate() {
        double localHits = getCounterValue(UserGroupConstants.CACHE_HIT_METRIC);
        double redisHits = getCounterValue(UserGroupConstants.REDIS_HIT_METRIC);
        double misses = getCounterValue(UserGroupConstants.CACHE_MISS_METRIC);
        double total = localHits + redisHits + misses;

        return total == 0 ? 0 : (localHits + redisHits) / total;
    }

    /**
     * 收集Timer统计信息
     */
    private Map<String, Object> collectTimerStats(String timerName) {
        Map<String, Object> stats = new HashMap<>();
        stats.put("count", getTimerCount(timerName));
        stats.put("mean", Math.round(getTimerMean(timerName) * 100.0) / 100.0);
        stats.put("max", Math.round(getTimerMax(timerName) * 100.0) / 100.0);
        stats.put("p95", Math.round(getTimerPercentile(timerName, 0.95) * 100.0) / 100.0);
        stats.put("p99", Math.round(getTimerPercentile(timerName, 0.99) * 100.0) / 100.0);
        return stats;
    }

    /**
     * 获取计数器值
     */
    private double getCounterValue(String name) {
        return meterRegistry.find(name).counter() != null
                ? meterRegistry.find(name).counter().count() : 0;
    }

    /**
     * 计算比率
     */
    private double calculateRate(double numerator, double denominator) {
        return denominator == 0 ? 0 : Math.round((numerator / denominator) * 10000.0) / 100.0;
    }

    /**
     * 获取计时器调用次数
     */
    private long getTimerCount(String name) {
        return meterRegistry.find(name).timer() != null
                ? meterRegistry.find(name).timer().count() : 0;
    }

    /**
     * 获取计时器平均时间（毫秒）
     */
    private double getTimerMean(String name) {
        return meterRegistry.find(name).timer() != null
                ? meterRegistry.find(name).timer().mean(TimeUnit.MILLISECONDS) : 0;
    }

    /**
     * 获取计时器最大时间（毫秒）
     */
    private double getTimerMax(String name) {
        return meterRegistry.find(name).timer() != null
                ? meterRegistry.find(name).timer().max(TimeUnit.MILLISECONDS) : 0;
    }

    /**
     * 获取计时器百分位数（毫秒）
     */
    private double getTimerPercentile(String name, double percentile) {
        return meterRegistry.find(name).timer() != null
                ? meterRegistry.find(name).timer().percentile(percentile, TimeUnit.MILLISECONDS) : 0;
    }

    /**
     * 安全地获取任何类型指标的值
     */
    private Object getSafeValue(Meter meter) {
        String meterName = meter.getId().getName();
        Meter.Type meterType = meter.getId().getType();

        try {
            switch (meterType) {
                case COUNTER:
                    return tryGetCounterValue(meterName);
                case GAUGE:
                    return tryGetGaugeValue(meterName);
                case TIMER:
                    return tryGetTimerValue(meterName);
                case DISTRIBUTION_SUMMARY:
                    return tryGetSummaryValue(meterName);
                case LONG_TASK_TIMER:
                    return tryGetLongTaskTimerValue(meterName);
                default:
                    return tryGetGenericValue(meter);
            }
        } catch (Exception e) {
            log.debug("流量平台-----> 获取指标 {} 值时出错: {}", meterName, e.getMessage());
            return "获取值出错";
        }
    }

    /**
     * 尝试获取Counter值
     */
    private double tryGetCounterValue(String name) {
        try {
            return meterRegistry.get(name).counter().count();
        } catch (Exception e) {
            try {
                Object functionCounter = meterRegistry.find(name).meter();
                Method countMethod = functionCounter.getClass().getMethod("count");
                return (double) countMethod.invoke(functionCounter);
            } catch (Exception ex) {
                return 0;
            }
        }
    }

    /**
     * 尝试获取Gauge值
     */
    private double tryGetGaugeValue(String name) {
        try {
            return meterRegistry.get(name).gauge().value();
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 尝试获取Timer值（平均值，毫秒）
     */
    private double tryGetTimerValue(String name) {
        try {
            return meterRegistry.get(name).timer().mean(TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            try {
                Object functionTimer = meterRegistry.find(name).meter();
                Method meanMethod = functionTimer.getClass().getMethod("mean", TimeUnit.class);
                return (double) meanMethod.invoke(functionTimer, TimeUnit.MILLISECONDS);
            } catch (Exception ex) {
                return 0;
            }
        }
    }

    /**
     * 尝试获取Summary值
     */
    private double tryGetSummaryValue(String name) {
        try {
            return meterRegistry.get(name).summary().mean();
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 尝试获取LongTaskTimer值
     */
    private double tryGetLongTaskTimerValue(String name) {
        try {
            return meterRegistry.get(name).longTaskTimer().mean(TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 通用的指标值获取方法
     */
    private Object tryGetGenericValue(Meter meter) {
        try {
            for (Method method : meter.getClass().getMethods()) {
                if (method.getName().equals("count") || method.getName().equals("value")) {
                    if (method.getParameterCount() == 0) {
                        return method.invoke(meter);
                    }
                }
            }
            return "类型: " + meter.getId().getType().toString();
        } catch (Exception e) {
            return "未支持的指标类型";
        }
    }
}
