package com.iptv.flux.service.controller;

import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.dto.DataImportProgressDTO;
import com.iptv.flux.common.dto.DataImportRequestDTO;
import com.iptv.flux.common.dto.DataImportResponseDTO;
import com.iptv.flux.common.dto.ResultDTO;
import com.iptv.flux.common.swagger.SwaggerAnnotations;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.service.DataImportService;
import com.iptv.flux.service.util.ModernExcelProcessor;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.controller
 * @className: DataImportController
 * @author: chiron
 * @description: 现代化数据导入控制器 - 提供Excel文件导入、预览、进度查询等功能
 * @date: 2025-06-13
 * @version: 2.0
 */
@RestController
@RequestMapping(UserGroupConstants.API_DATA_IMPORT_PATH)
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "数据导入管理", description = "Excel文件数据导入相关接口 - 支持实时进度跟踪、快速预览、性能优化")
public class DataImportController {

    private final DataImportService dataImportService;
    private final MetricsRegistryUtil metricsRegistry;

    /**
     * 上传并导入Excel文件
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.dataimport.upload", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "上传并导入Excel文件",
            description = "上传Excel文件并导入用户分组数据，支持同步和异步处理，可自动创建分组信息。异步模式下接口会立即返回，通过进度查询接口跟踪处理状态。",
            responseType = DataImportResponseDTO.class
    )
    public ResultDTO<DataImportResponseDTO> uploadAndImport(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "source", defaultValue = "dx") String source,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "overwrite", defaultValue = "false") Boolean overwrite,
            @RequestParam(value = "batchSize", defaultValue = "1000") Integer batchSize,
            @RequestParam(value = "async", defaultValue = "true") Boolean async,
            @RequestParam(value = "strategyId", required = false) String strategyId,
            @RequestParam(value = "groupName", required = false) String groupName,
            @RequestParam(value = "groupDescription", required = false) String groupDescription,
            @RequestParam(value = "autoCreateGroup", defaultValue = "true") Boolean autoCreateGroup) {
        
        log.info("流量平台-----> 收到文件上传请求，文件名: {}，大小: {} bytes，来源: {}，自动创建分组: {}",
                file.getOriginalFilename(), file.getSize(), source, autoCreateGroup);

        // 预检查文件大小，避免浪费资源
        if (file.getSize() > UserGroupConstants.DATA_IMPORT_MAX_FILE_SIZE_MB * 1024 * 1024) {
            log.warn("流量平台-----> 文件大小超过限制: {}MB，实际大小: {}MB",
                    UserGroupConstants.DATA_IMPORT_MAX_FILE_SIZE_MB, file.getSize() / 1024 / 1024);
            metricsRegistry.incrementCounter("dataimport.upload.error");
            return ResultDTO.fail("文件大小超过限制：" + UserGroupConstants.DATA_IMPORT_MAX_FILE_SIZE_MB + "MB");
        }

        // 预检查文件是否为空
        if (file.isEmpty()) {
            log.warn("流量平台-----> 上传的文件为空");
            metricsRegistry.incrementCounter("dataimport.upload.error");
            return ResultDTO.fail("上传的文件为空");
        }

        try {
            // 构建请求对象
            DataImportRequestDTO request = DataImportRequestDTO.builder()
                    .source(source)
                    .description(description)
                    .overwrite(overwrite)
                    .batchSize(batchSize)
                    .async(async)
                    .strategyId(strategyId)
                    .groupName(groupName)
                    .groupDescription(groupDescription)
                    .autoCreateGroup(autoCreateGroup)
                    .build();

            // 执行导入
            DataImportResponseDTO response = dataImportService.importExcelFile(file, request);

            log.info("流量平台-----> 文件上传处理完成，任务ID: {}，状态: {}，异步模式: {}",
                    response.getTaskId(), response.getStatus(), async);

            // 为异步任务提供额外的提示信息
            if (async && "PROCESSING".equals(response.getStatus())) {
                response.setMessage("文件已成功接收，正在后台处理中。请使用任务ID查询处理进度。");
                log.info("流量平台-----> 异步任务已启动，任务ID: {}，可通过进度查询接口跟踪处理状态", response.getTaskId());
            }

            metricsRegistry.incrementCounter("dataimport.upload.success");
            return ResultDTO.success(response);

        } catch (Exception e) {
            log.error("流量平台-----> 文件上传处理失败，文件名: {}", file.getOriginalFilename(), e);
            metricsRegistry.incrementCounter("dataimport.upload.error");

            // 提供更详细的错误信息
            String errorMessage = "文件导入失败：" + e.getMessage();
            if (e.getMessage().contains("timeout") || e.getMessage().contains("超时")) {
                errorMessage += "。建议：1) 减小文件大小 2) 使用异步模式 3) 检查网络连接";
            }

            return ResultDTO.fail(errorMessage);
        }
    }

    /**
     * 查询导入进度
     */
    @GetMapping(value = "/progress/{taskId}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.dataimport.progress", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "查询导入进度",
            description = "根据任务ID查询数据导入的进度信息",
            responseType = DataImportProgressDTO.class
    )
    public ResultDTO<DataImportProgressDTO> getImportProgress(@PathVariable String taskId) {
        log.debug("流量平台-----> 收到查询导入进度请求，任务ID: {}", taskId);

        try {
            DataImportProgressDTO progress = dataImportService.getImportProgress(taskId);
            return ResultDTO.success(progress);

        } catch (Exception e) {
            log.error("流量平台-----> 查询导入进度失败，任务ID: {}", taskId, e);
            metricsRegistry.incrementCounter("dataimport.progress.error");
            return ResultDTO.fail("查询进度失败：" + e.getMessage());
        }
    }

    /**
     * 获取支持的文件格式信息
     */
    @GetMapping(value = "/formats", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.QueryOperation(
            summary = "获取支持的文件格式",
            description = "获取系统支持的Excel文件格式和限制信息",
            responseType = Object.class
    )
    public ResultDTO<Object> getSupportedFormats() {
        log.debug("流量平台-----> 收到获取支持文件格式请求");

        try {
            return ResultDTO.success(Map.of(
                    "supportedExtensions", UserGroupConstants.ALLOWED_FILE_EXTENSIONS,
                    "maxFileSizeMB", UserGroupConstants.DATA_IMPORT_MAX_FILE_SIZE_MB,
                    "defaultBatchSize", UserGroupConstants.DATA_IMPORT_BATCH_SIZE,
                    "defaultSource", UserGroupConstants.DEFAULT_SOURCE
            ));

        } catch (Exception e) {
            log.error("流量平台-----> 获取支持文件格式失败", e);
            return ResultDTO.fail("获取格式信息失败：" + e.getMessage());
        }
    }

    /**
     * 预览Excel文件中的分组信息 - 使用现代化Excel处理器
     */
    @PostMapping(value = "/preview", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.QueryOperation(
            summary = "预览Excel文件分组信息",
            description = "预览Excel文件中可提取的分组信息，不执行实际导入",
            responseType = Object.class
    )
    public ResultDTO<Object> previewExcelGroupInfo(@RequestParam("file") MultipartFile file) {
        log.info("流量平台-----> 收到Excel文件预览请求，文件名: {}", file.getOriginalFilename());

        try {
            // 验证文件
            if (!dataImportService.getExcelProcessor().isValidExcelFile(file)) {
                return ResultDTO.fail("不支持的文件格式，请上传.xlsx或.xls文件");
            }

            // 使用现代化Excel处理器进行快速预览
            ModernExcelProcessor.PreviewResult previewResult =
                    dataImportService.getExcelProcessor().fastPreview(file);

            // 从文件名提取分组ID
            String groupId = extractGroupIdFromFileName(file.getOriginalFilename());

            Map<String, Object> result = new HashMap<>();
            result.put("fileName", file.getOriginalFilename());
            result.put("fileSize", file.getSize());
            result.put("fileSizeMB", String.format("%.2f", file.getSize() / 1024.0 / 1024.0));
            result.put("totalRows", previewResult.getTotalRows());
            result.put("estimatedUserCount", previewResult.getEstimatedTotal());
            result.put("sampleUserCount", previewResult.getSampleData().size());
            result.put("sampleUsers", previewResult.getSampleData().subList(0,
                    Math.min(10, previewResult.getSampleData().size())));

            // 构建分组信息
            Map<String, Object> groupInfo = new HashMap<>();
            groupInfo.put("groupId", groupId);
            groupInfo.put("groupName", groupId); // 默认使用groupId作为名称
            groupInfo.put("strategyId", null);
            groupInfo.put("description", "从文件 " + file.getOriginalFilename() + " 导入");

            result.put("groupInfo", groupInfo);

            log.info("流量平台-----> Excel文件预览完成，文件: {}，估算记录数: {}",
                    file.getOriginalFilename(), previewResult.getEstimatedTotal());

            return ResultDTO.success(result);

        } catch (Exception e) {
            log.error("流量平台-----> Excel文件预览失败", e);
            return ResultDTO.fail("文件预览失败：" + e.getMessage());
        }
    }

    /**
     * 从文件名提取分组ID
     */
    private String extractGroupIdFromFileName(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "unknown";
        }

        // 移除文件扩展名
        String nameWithoutExt = filename.replaceAll("\\.(xlsx?|xls)$", "");

        // 简单的分组ID提取逻辑
        return nameWithoutExt.replaceAll("[^a-zA-Z0-9_-]", "_");
    }

    /**
     * 快速预览Excel文件基本信息
     */
    @PostMapping(value = "/info", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.QueryOperation(
            summary = "获取Excel文件基本信息",
            description = "快速获取Excel文件的基本信息，如行数、大小等",
            responseType = Object.class
    )
    public ResultDTO<Object> getExcelInfo(@RequestParam("file") MultipartFile file) {
        log.info("流量平台-----> 收到Excel文件信息查询请求，文件名: {}", file.getOriginalFilename());

        try {
            // 验证文件
            if (!dataImportService.getExcelProcessor().isValidExcelFile(file)) {
                return ResultDTO.fail("不支持的文件格式，请上传.xlsx或.xls文件");
            }

            // 获取总记录数
            int totalRecords = dataImportService.getExcelProcessor().getTotalRecords(file);

            // 构建响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("fileName", file.getOriginalFilename());
            responseData.put("fileSize", file.getSize());
            responseData.put("fileSizeMB", String.format("%.2f", file.getSize() / 1024.0 / 1024.0));
            responseData.put("totalRecords", totalRecords);
            responseData.put("estimatedProcessingTime", estimateProcessingTime(totalRecords));
            responseData.put("recommendedBatchSize", calculateRecommendedBatchSize(totalRecords));

            return ResultDTO.success(responseData);

        } catch (Exception e) {
            log.error("流量平台-----> 获取Excel文件信息失败，文件: {}", file.getOriginalFilename(), e);
            return ResultDTO.fail("获取文件信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统性能统计
     */
    @GetMapping(value = "/stats", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.QueryOperation(
            summary = "获取系统性能统计",
            description = "获取数据导入系统的性能统计信息",
            responseType = String.class
    )
    public ResultDTO<String> getPerformanceStats() {
        try {
            String stats = dataImportService.getPerformanceStats();
            return ResultDTO.success(stats);
        } catch (Exception e) {
            log.error("流量平台-----> 获取性能统计失败", e);
            return ResultDTO.fail("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 清理过期进度缓存
     */
    @PostMapping(value = "/cleanup", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.QueryOperation(
            summary = "清理过期进度缓存",
            description = "清理过期的进度缓存数据",
            responseType = String.class
    )
    public ResultDTO<String> cleanupExpiredProgress() {
        try {
            dataImportService.cleanupExpiredProgress();
            return ResultDTO.success("清理完成");
        } catch (Exception e) {
            log.error("流量平台-----> 清理过期进度缓存失败", e);
            return ResultDTO.fail("清理失败: " + e.getMessage());
        }
    }



    /**
     * 估算处理时间
     */
    private String estimateProcessingTime(int totalRecords) {
        // 基于经验值：每秒处理约1000条记录
        int estimatedSeconds = Math.max(1, totalRecords / 1000);

        if (estimatedSeconds < 60) {
            return estimatedSeconds + "秒";
        } else if (estimatedSeconds < 3600) {
            return (estimatedSeconds / 60) + "分钟";
        } else {
            return (estimatedSeconds / 3600) + "小时";
        }
    }

    /**
     * 计算推荐的批次大小
     */
    private int calculateRecommendedBatchSize(int totalRecords) {
        if (totalRecords < 1000) {
            return 100;
        } else if (totalRecords < 10000) {
            return 500;
        } else if (totalRecords < 100000) {
            return 1000;
        } else {
            return 2000;
        }
    }

    /**
     * 健康检查端点 - 兼容性保留
     */
    @GetMapping(value = "/health", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.QueryOperation(
            summary = "数据导入服务健康检查",
            description = "检查数据导入相关服务的健康状态，建议使用 /api/monitor/health 获取完整系统状态",
            responseType = String.class
    )
    public ResultDTO<String> healthCheck() {
        log.debug("流量平台-----> 收到数据导入健康检查请求");
        return ResultDTO.success("数据导入服务运行正常");
    }
}
