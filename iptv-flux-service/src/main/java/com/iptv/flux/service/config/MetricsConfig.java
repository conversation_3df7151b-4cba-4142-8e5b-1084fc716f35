package com.iptv.flux.service.config;

import com.github.benmanes.caffeine.cache.Cache;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.jvm.JvmGcMetrics;
import io.micrometer.core.instrument.binder.jvm.JvmMemoryMetrics;
import io.micrometer.core.instrument.binder.jvm.JvmThreadMetrics;
import io.micrometer.core.instrument.binder.system.ProcessorMetrics;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBloomFilter;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.util.Set;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.config
 * @className: MetricsConfig
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 12:56
 * @version: 1.0
 */
@Configuration
@Slf4j
public class MetricsConfig {

    @Bean
    public MetricsRegistryUtil metricsRegistryUtil(MeterRegistry meterRegistry) {
        log.info("配置指标注册表工具类");
        return new MetricsRegistryUtil(meterRegistry);
    }

    @Bean
    public JvmMemoryMetrics jvmMemoryMetrics() {
        return new JvmMemoryMetrics();
    }

    @Bean
    public JvmGcMetrics jvmGcMetrics() {
        return new JvmGcMetrics();
    }

    @Bean
    public JvmThreadMetrics jvmThreadMetrics() {
        return new JvmThreadMetrics();
    }

    @Bean
    public ProcessorMetrics processorMetrics() {
        return new ProcessorMetrics();
    }

    private final Cache<String, Set<String>> cache;
    private final RBloomFilter<String> bloomFilter;
    private final RedissonClient redissonClient;
    private final Environment environment;

    public MetricsConfig(
            @Qualifier("localCache") Cache<String, Set<String>> cache,
            RBloomFilter<String> bloomFilter,
            RedissonClient redissonClient,
            Environment environment) {
        this.cache = cache;
        this.bloomFilter = bloomFilter;
        this.redissonClient = redissonClient;
        this.environment = environment;
    }

    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        String applicationName = environment.getProperty("spring.application.name", "iptv-flux-service");
        String profile = environment.getProperty("spring.profiles.active", "unknown");

        log.info("设置通用指标标签: application={}, profile={}", applicationName, profile);

        return registry -> registry.config()
                .commonTags("application", applicationName)
                .commonTags("profile", profile);
    }

    @Bean
    public MetricsInitializer metricsInitializer(MeterRegistry registry) {
        return new MetricsInitializer(registry, cache, bloomFilter, redissonClient);
    }

    private static class MetricsInitializer {
        private final MeterRegistry registry;
        private final Cache<String, Set<String>> cache;
        private final RBloomFilter<String> bloomFilter;
        private final RedissonClient redissonClient;

        public MetricsInitializer(MeterRegistry registry,
                                  Cache<String, Set<String>> cache,
                                  RBloomFilter<String> bloomFilter,
                                  RedissonClient redissonClient) {
            this.registry = registry;
            this.cache = cache;
            this.bloomFilter = bloomFilter;
            this.redissonClient = redissonClient;
        }

        @PostConstruct
        public void registerCustomMetrics() {
            registry.gauge("cache.size", cache, c -> c.estimatedSize());
            registry.gauge("cache.stats.hit.ratio", cache, c -> c.stats().hitRate());
            registry.gauge("cache.stats.miss.ratio", cache, c -> c.stats().missRate());
            registry.gauge("cache.stats.eviction.count", cache, c -> c.stats().evictionCount());
            registry.gauge("bloom.filter.size", bloomFilter, RBloomFilter::count);

            // 直接获取当前 Redis 配置
            registry.gauge("redis.connections.client.count",
                    redissonClient, client -> 1);

            log.info("自定义指标注册成功");
        }
    }
}
