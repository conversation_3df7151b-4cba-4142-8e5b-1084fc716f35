//package com.iptv.flux.service.config;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
//import org.redisson.Redisson;
//import org.redisson.api.RedissonClient;
//import org.redisson.client.codec.StringCodec;
//import org.redisson.config.Config;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.data.redis.connection.RedisConnectionFactory;
//import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
//import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
//import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
//import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
//import org.springframework.data.redis.core.StringRedisTemplate;
//
//import java.time.Duration;
//
///**
// * @projectName: flux-manager
// * @package: com.iptv.flux.service.config
// * @className: RedisStandaloneConfig
// * @author: chiron
// * @description: 主从模式
// * @date: 2025/3/10 09:00
// * @version: 1.0
// */
//@Configuration
//@Deprecated
//@Slf4j
//public class RedisStandaloneConfig {
//    @Value("${spring.redis.host:**************}")
//    private String host;
//
//    @Value("${spring.redis.port:6379}")
//    private int port;
//
//    @Value("${spring.redis.password:5iZhX0z8FymFMHddcGQt}")
//    private String password;
//
//    @Value("${spring.redis.timeout:3000}")
//    private int timeout;
//
//    @Value("${spring.redis.lettuce.pool.max-active:1000}")
//    private int maxActive;
//
//    @Value("${spring.redis.lettuce.pool.max-idle:10}")
//    private int maxIdle;
//
//    @Value("${spring.redis.lettuce.pool.min-idle:10}")
//    private int minIdle;
//
//    @Value("${spring.redis.lettuce.pool.max-wait:3000ms}")
//    private Duration maxWait;
//
//    @Bean
//    public RedisConnectionFactory redisConnectionFactory() {
//        log.info("配置Redis单节点连接工厂: {}:{}", host, port);
//
//        RedisStandaloneConfiguration standaloneConfig = new RedisStandaloneConfiguration(host, port);
//        if (password != null && !password.isEmpty()) {
//            standaloneConfig.setPassword(password);
//        }
//
//        GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
//        poolConfig.setMaxTotal(maxActive);
//        poolConfig.setMaxIdle(maxIdle);
//        poolConfig.setMinIdle(minIdle);
//        poolConfig.setMaxWait(maxWait);
//
//        LettuceClientConfiguration clientConfig = LettucePoolingClientConfiguration.builder()
//                .commandTimeout(Duration.ofMillis(timeout))
//                .poolConfig(poolConfig)
//                .build();
//
//        return new LettuceConnectionFactory(standaloneConfig, clientConfig);
//    }
//
//    @Bean(name = "stringRedisTemplate")
//    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
//        StringRedisTemplate template = new StringRedisTemplate();
//        template.setConnectionFactory(redisConnectionFactory);
//        return template;
//    }
//
//    @Bean
//    public RedissonClient redissonClient() {
//        log.info("配置Redisson单节点客户端: {}:{}", host, port);
//        Config config = new Config();
//        config.setCodec(new StringCodec());
//
//        // 单节点模式配置
//        config.useSingleServer()
//                .setAddress("redis://" + host + ":" + port)
//                .setConnectionMinimumIdleSize(minIdle / 2)
//                .setConnectionPoolSize(maxActive / 2)
//                .setSubscriptionConnectionMinimumIdleSize(minIdle / 10)
//                .setSubscriptionConnectionPoolSize(maxActive / 10)
//                .setIdleConnectionTimeout(timeout)
//                .setConnectTimeout(timeout)
//                .setRetryAttempts(3)
//                .setRetryInterval(1500);
//
//        if (password != null && !password.isEmpty()) {
//            config.useSingleServer().setPassword(password);
//        }
//
//        return Redisson.create(config);
//    }
//}
