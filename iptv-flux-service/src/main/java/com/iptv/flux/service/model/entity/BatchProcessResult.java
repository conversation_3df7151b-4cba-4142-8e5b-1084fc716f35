package com.iptv.flux.service.model.entity;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.repository
 * @className: BatchProcessResult
 * @author: chiron
 * @description: 批处理结果统计类
 * @date: 2025-06-13
 * @version: 1.0
 */
@Data
public class BatchProcessResult {
    
    /**
     * 已处理记录数
     */
    public int processedCount = 0;
    
    /**
     * 插入记录数
     */
    public int insertedCount = 0;
    
    /**
     * 更新记录数
     */
    public int updatedCount = 0;
    
    /**
     * 失败记录数
     */
    public int failedCount = 0;
    
    /**
     * 错误信息映射
     */
    private Map<String, String> errors = new HashMap<>();

    /**
     * 增加已处理计数
     */
    public void incrementProcessed() { 
        processedCount++; 
    }
    
    /**
     * 增加插入计数
     */
    public void incrementInserted() { 
        insertedCount++; 
    }
    
    /**
     * 增加更新计数
     */
    public void incrementUpdated() { 
        updatedCount++; 
    }
    
    /**
     * 增加失败计数
     */
    public void incrementFailed() { 
        failedCount++; 
    }
    
    /**
     * 添加错误信息
     */
    public void addError(String userId, String errorMessage) {
        errors.put(userId, errorMessage);
    }

    /**
     * 获取成功记录数
     */
    public int getSuccessCount() {
        return insertedCount + updatedCount;
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (processedCount == 0) {
            return 0.0;
        }
        return (double) getSuccessCount() / processedCount * 100.0;
    }

    @Override
    public String toString() {
        return String.format("BatchProcessResult{processed=%d, inserted=%d, updated=%d, failed=%d, successRate=%.2f%%}",
                processedCount, insertedCount, updatedCount, failedCount, getSuccessRate());
    }
}
