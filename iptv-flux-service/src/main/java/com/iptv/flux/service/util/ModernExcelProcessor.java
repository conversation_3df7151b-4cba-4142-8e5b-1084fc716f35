package com.iptv.flux.service.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Consumer;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.util
 * @className: ModernExcelProcessor
 * @author: chiron
 * @description: 现代化Excel处理器 - 流式处理，高性能，内存优化
 * @date: 2025-06-13
 * @version: 2.0
 */
@Component
@Slf4j
public class ModernExcelProcessor {

    private static final int PREVIEW_BATCH_SIZE = 100;
    private static final int PROCESSING_BATCH_SIZE = 1000;

    /**
     * 快速预览Excel文件 - 只读取必要数据，极速响应
     */
    public PreviewResult fastPreview(MultipartFile file) throws IOException {
        log.info("流量平台-----> 开始快速预览Excel文件: {}, 大小: {} bytes", 
                file.getOriginalFilename(), file.getSize());
        
        long startTime = System.currentTimeMillis();
        
        try (InputStream inputStream = file.getInputStream()) {
            Workbook workbook = createWorkbook(file.getOriginalFilename(), inputStream);
            Sheet sheet = workbook.getSheetAt(0);
            
            int totalRows = sheet.getPhysicalNumberOfRows();
            List<String> sampleData = new ArrayList<>();
            
            // 智能采样：只读取前100行进行预览
            int sampleRows = Math.min(PREVIEW_BATCH_SIZE, totalRows);
            
            for (int i = 0; i < sampleRows; i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    extractUserIdsFromRow(row, sampleData);
                }
            }
            
            // 去重并估算总数
            Set<String> uniqueIds = new LinkedHashSet<>(sampleData);
            uniqueIds.removeIf(id -> id == null || id.trim().isEmpty());
            
            int estimatedTotal = totalRows > 0 ? 
                    (int) ((double) uniqueIds.size() / sampleRows * totalRows) : 0;
            
            workbook.close();
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("流量平台-----> 快速预览完成，耗时: {}ms，预览行数: {}，唯一ID数: {}，估算总数: {}", 
                    duration, sampleRows, uniqueIds.size(), estimatedTotal);
            
            return new PreviewResult(new ArrayList<>(uniqueIds), estimatedTotal, totalRows);
        }
    }

    /**
     * 流式处理Excel文件 - 分批处理，实时进度回调
     */
    public void processExcelStream(MultipartFile file, Consumer<List<String>> batchProcessor) throws IOException {
        log.info("流量平台-----> 开始流式处理Excel文件: {}", file.getOriginalFilename());

        try (InputStream inputStream = file.getInputStream()) {
            Workbook workbook = createWorkbook(file.getOriginalFilename(), inputStream);
            Sheet sheet = workbook.getSheetAt(0);

            List<String> batch = new ArrayList<>(PROCESSING_BATCH_SIZE);
            int processedRows = 0;
            int batchCount = 0;

            // 逐行处理，确保进度更新
            for (Row row : sheet) {
                if (row != null) {
                    processedRows++;
                    List<String> rowData = new ArrayList<>();
                    extractUserIdsFromRow(row, rowData);
                    batch.addAll(rowData);

                    // 达到批次大小时处理
                    if (batch.size() >= PROCESSING_BATCH_SIZE) {
                        batchCount++;
                        List<String> currentBatch = new ArrayList<>(batch);
                        batch.clear();

                        // 同步处理批次，确保进度更新
                        processBatch(currentBatch, batchProcessor, batchCount);

                        log.debug("流量平台-----> 已处理批次 {}，当前行数: {}", batchCount, processedRows);
                    }
                }
            }

            // 处理最后一批数据
            if (!batch.isEmpty()) {
                batchCount++;
                processBatch(batch, batchProcessor, batchCount);
                log.debug("流量平台-----> 处理最后批次 {}，总行数: {}", batchCount, processedRows);
            }

            workbook.close();
            log.info("流量平台-----> 流式处理完成，总处理行数: {}，总批次数: {}", processedRows, batchCount);
        }
    }

    /**
     * 获取Excel文件总记录数 - 快速统计，不加载全部数据
     */
    public int getTotalRecords(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            Workbook workbook = createWorkbook(file.getOriginalFilename(), inputStream);
            Sheet sheet = workbook.getSheetAt(0);
            
            int totalRows = sheet.getPhysicalNumberOfRows();
            workbook.close();
            
            log.debug("流量平台-----> Excel文件总行数: {}", totalRows);
            return totalRows;
        }
    }

    /**
     * 验证Excel文件格式
     */
    public boolean isValidExcelFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }
        
        String filename = file.getOriginalFilename();
        if (filename == null) {
            return false;
        }
        
        return filename.toLowerCase().endsWith(".xlsx") || 
               filename.toLowerCase().endsWith(".xls");
    }

    /**
     * 处理批次数据
     */
    private void processBatch(List<String> batch, Consumer<List<String>> processor, int batchNumber) {
        // 去重处理
        Set<String> uniqueIds = new LinkedHashSet<>(batch);
        uniqueIds.removeIf(id -> id == null || id.trim().isEmpty());

        if (!uniqueIds.isEmpty()) {
            log.debug("流量平台-----> 处理批次 {}，去重后数据量: {}", batchNumber, uniqueIds.size());
            processor.accept(new ArrayList<>(uniqueIds));
        } else {
            log.debug("流量平台-----> 批次 {} 无有效数据", batchNumber);
        }
    }

    /**
     * 从行中提取用户ID
     */
    private void extractUserIdsFromRow(Row row, List<String> userIds) {
        for (Cell cell : row) {
            if (cell != null) {
                String cellValue = getCellValueAsString(cell);
                if (cellValue != null && !cellValue.trim().isEmpty()) {
                    userIds.add(cellValue.trim());
                }
            }
        }
    }

    /**
     * 获取单元格值为字符串
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue();
                } catch (Exception e) {
                    return String.valueOf(cell.getNumericCellValue());
                }
            default:
                return null;
        }
    }

    /**
     * 创建工作簿
     */
    private Workbook createWorkbook(String filename, InputStream inputStream) throws IOException {
        if (filename.toLowerCase().endsWith(".xlsx")) {
            return new XSSFWorkbook(inputStream);
        } else {
            return new HSSFWorkbook(inputStream);
        }
    }

    /**
     * 预览结果类
     */
    public static class PreviewResult {
        private final List<String> sampleData;
        private final int estimatedTotal;
        private final int totalRows;

        public PreviewResult(List<String> sampleData, int estimatedTotal, int totalRows) {
            this.sampleData = sampleData;
            this.estimatedTotal = estimatedTotal;
            this.totalRows = totalRows;
        }

        public List<String> getSampleData() { return sampleData; }
        public int getEstimatedTotal() { return estimatedTotal; }
        public int getTotalRows() { return totalRows; }
    }
}
