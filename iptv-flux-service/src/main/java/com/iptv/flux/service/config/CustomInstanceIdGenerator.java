package com.iptv.flux.service.config;

import org.quartz.SchedulerException;
import org.quartz.spi.InstanceIdGenerator;

import java.util.UUID;

/**
 * @projectName: flux-manager
 * @package: com.iptv.flux.service.config
 * @className: CustomInstanceIdGenerator
 * @author: chiron
 * @description: 实例ID随机生成类
 * @date: 2025/3/24 10:45
 * @version: 1.0
 */
public class CustomInstanceIdGenerator implements InstanceIdGenerator {
    @Override
    public String generateInstanceId() throws SchedulerException {
        return "FLUX-QUARTZ-" + UUID.randomUUID().toString();
    }
}
