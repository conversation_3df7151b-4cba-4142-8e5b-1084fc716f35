package com.iptv.flux.service.task;

import com.iptv.flux.service.service.progress.ProgressManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.task
 * @className: ProgressCleanupTask
 * @author: chiron
 * @description: 进度清理定时任务 - 定期清理过期的进度缓存
 * @date: 2025-06-13
 * @version: 2.0
 */
@Component
@Slf4j
public class ProgressCleanupTask {

    private final ProgressManager progressManager;

    public ProgressCleanupTask(ProgressManager progressManager) {
        this.progressManager = progressManager;
    }

    /**
     * 每小时清理一次过期的进度缓存
     */
    @Scheduled(fixedRate = 3600000) // 1小时 = 3600000毫秒
    public void cleanupExpiredProgress() {
        try {
            log.info("流量平台-----> 开始清理过期进度缓存");
            progressManager.cleanupExpiredProgress();
            log.info("流量平台-----> 过期进度缓存清理完成");
        } catch (Exception e) {
            log.error("流量平台-----> 清理过期进度缓存失败", e);
        }
    }

    /**
     * 每天凌晨2点输出性能统计
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void logPerformanceStats() {
        try {
            log.info("流量平台-----> 进度管理器运行正常，定期清理任务已启用");
        } catch (Exception e) {
            log.error("流量平台-----> 性能统计任务失败", e);
        }
    }
}
