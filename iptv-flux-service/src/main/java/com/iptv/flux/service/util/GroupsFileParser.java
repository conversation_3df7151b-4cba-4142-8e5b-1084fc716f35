package com.iptv.flux.service.util;

import com.iptv.flux.common.dto.GroupInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Consumer;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.util
 * @className: GroupsFileParser
 * @author: Claude 4.0 sonnet
 * @description: groups.txt文件解析器 - 解析分组信息文件
 * @date: 2025/8/14 12:00
 * @version: 1.0
 */
@Component
@Slf4j
public class GroupsFileParser {

    private static final int BATCH_SIZE = 1000;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    /**
     * 流式解析groups.txt文件
     * 
     * 文件格式示例：
     * SysID|GroupID|GroupName|Platform|Description|FileURL|UserCovering|GenerateTime|sumkey|Strategys
     * dx|CG001|VIP用户组|platform1|高价值用户群体|http://example.com/file1.txt|5000|20250814120000|abc123|ST001,ST002
     *
     * @param file 要解析的文件
     * @param batchProcessor 批处理器，处理每批解析的数据
     * @throws IOException 文件读取异常
     */
    public void parseGroupsFile(File file, Consumer<List<GroupInfoDTO>> batchProcessor) throws IOException {
        log.info("流量平台-----> 开始解析groups.txt文件: {}, 大小: {} bytes", 
                file.getName(), file.length());
        
        long startTime = System.currentTimeMillis();
        int totalLines = 0;
        int processedLines = 0;
        int errorLines = 0;

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {

            String line;
            List<GroupInfoDTO> batch = new ArrayList<>(BATCH_SIZE);
            boolean isFirstLine = true;

            while ((line = reader.readLine()) != null) {
                totalLines++;
                
                // 跳过空行
                if (line.trim().isEmpty()) {
                    continue;
                }

                // 跳过标题行（如果存在）
                if (isFirstLine && isHeaderLine(line)) {
                    isFirstLine = false;
                    log.debug("流量平台-----> 跳过标题行: {}", line);
                    continue;
                }
                isFirstLine = false;

                try {
                    GroupInfoDTO groupInfo = parseGroupLine(line);
                    if (groupInfo != null) {
                        batch.add(groupInfo);
                        processedLines++;

                        // 达到批次大小时处理
                        if (batch.size() >= BATCH_SIZE) {
                            processBatch(batch, batchProcessor, processedLines);
                            batch.clear();
                        }
                    }
                } catch (Exception e) {
                    errorLines++;
                    log.warn("流量平台-----> 解析分组信息行失败，行号: {}, 内容: {}, 错误: {}", 
                            totalLines, line, e.getMessage());
                }
            }

            // 处理最后一批数据
            if (!batch.isEmpty()) {
                processBatch(batch, batchProcessor, processedLines);
            }

            long duration = System.currentTimeMillis() - startTime;
            log.info("流量平台-----> groups.txt文件解析完成，总行数: {}, 成功: {}, 失败: {}, 耗时: {}ms",
                    totalLines, processedLines, errorLines, duration);

        } catch (IOException e) {
            log.error("流量平台-----> 读取groups.txt文件失败: {}", file.getName(), e);
            throw e;
        }
    }

    /**
     * 获取文件总行数（快速统计）
     */
    public int getTotalLines(File file) throws IOException {
        int lines = 0;
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {
            while (reader.readLine() != null) {
                lines++;
            }
        }
        log.debug("流量平台-----> groups.txt文件总行数: {}", lines);
        return lines;
    }

    /**
     * 解析单行分组信息
     * 
     * 格式：SysID|GroupID|GroupName|Platform|Description|FileURL|UserCovering|GenerateTime|sumkey|Strategys
     */
    private GroupInfoDTO parseGroupLine(String line) {
        if (line == null || line.trim().isEmpty()) {
            return null;
        }

        String[] fields = line.split("\\|", -1); // -1 保留空字段
        if (fields.length < 9) {
            throw new IllegalArgumentException("分组信息字段数量不足，期望至少9个字段，实际: " + fields.length);
        }

        try {
            GroupInfoDTO.GroupInfoDTOBuilder builder = GroupInfoDTO.builder();

            // SysID -> source（运营商：dx/lt/yd）
            String sysID = fields[0].trim();
            builder.source(sysID);

            // GroupID -> group_id
            String groupId = fields[1].trim();
            if (groupId.isEmpty()) {
                throw new IllegalArgumentException("GroupID不能为空");
            }
            builder.groupId(groupId);

            // GroupName -> group_name
            builder.groupName(fields[2].trim());

            // Platform -> platform
            builder.platform(fields[3].trim());

            // Description -> description
            builder.description(fields[4].trim());

            // FileURL -> file_url
            builder.fileUrl(fields[5].trim());

            // UserCovering -> coverage
            String userCovering = fields[6].trim();
            if (!userCovering.isEmpty()) {
                try {
                    builder.coverage(Long.parseLong(userCovering));
                } catch (NumberFormatException e) {
                    log.warn("流量平台-----> 用户覆盖度解析失败: {}", userCovering);
                }
            }

            // GenerateTime -> generate_time
            String generateTime = fields[7].trim();
            if (!generateTime.isEmpty()) {
                try {
                    LocalDateTime dateTime = LocalDateTime.parse(generateTime, DATE_FORMATTER);
                    builder.generateTime(dateTime);
                } catch (Exception e) {
                    log.warn("流量平台-----> 生成时间解析失败: {}", generateTime);
                }
            }

            // sumkey -> md5sum
            builder.md5sum(fields[8].trim());

            // Strategys -> strategy_id（处理多个策略的情况，取第一个）
            if (fields.length > 9) {
                String strategys = fields[9].trim();
                if (!strategys.isEmpty()) {
                    // 如果有多个策略，用逗号分隔，取第一个
                    String[] strategyArray = strategys.split(",");
                    builder.strategyId(strategyArray[0].trim());
                }
            }

            // 默认激活状态
            builder.active(true);

            return builder.build();

        } catch (Exception e) {
            throw new IllegalArgumentException("解析分组信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 判断是否为标题行
     */
    private boolean isHeaderLine(String line) {
        return line.toLowerCase().contains("sysid") && 
               line.toLowerCase().contains("groupid") && 
               line.toLowerCase().contains("groupname");
    }

    /**
     * 处理批次数据
     */
    private void processBatch(List<GroupInfoDTO> batch, Consumer<List<GroupInfoDTO>> processor, int processedCount) {
        if (batch.isEmpty()) {
            return;
        }

        log.debug("流量平台-----> 处理分组信息批次，数量: {}, 累计处理: {}", batch.size(), processedCount);
        
        try {
            processor.accept(new ArrayList<>(batch));
        } catch (Exception e) {
            log.error("流量平台-----> 处理分组信息批次失败", e);
            throw e;
        }
    }

    /**
     * 验证文件格式
     */
    public boolean isValidGroupsFile(File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            return false;
        }

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {
            
            String firstLine = reader.readLine();
            if (firstLine == null) {
                return false;
            }

            // 检查是否包含必要的分隔符
            return firstLine.contains("|") && firstLine.split("\\|").length >= 9;

        } catch (IOException e) {
            log.warn("流量平台-----> 验证groups.txt文件格式时发生异常", e);
            return false;
        }
    }

    /**
     * 解析结果统计
     */
    public static class ParseResult {
        private final int totalLines;
        private final int processedLines;
        private final int errorLines;
        private final long durationMs;

        public ParseResult(int totalLines, int processedLines, int errorLines, long durationMs) {
            this.totalLines = totalLines;
            this.processedLines = processedLines;
            this.errorLines = errorLines;
            this.durationMs = durationMs;
        }

        public int getTotalLines() { return totalLines; }
        public int getProcessedLines() { return processedLines; }
        public int getErrorLines() { return errorLines; }
        public long getDurationMs() { return durationMs; }
        public boolean isSuccess() { return errorLines == 0; }
        public double getSuccessRate() { 
            return totalLines > 0 ? (double) processedLines / totalLines * 100 : 0; 
        }
    }
}
