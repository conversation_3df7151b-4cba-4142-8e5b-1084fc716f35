package com.iptv.flux.service.service.progress;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iptv.flux.common.dto.DataImportProgressDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.service.progress
 * @className: ProgressManager
 * @author: chiron
 * @description: 统一的进度管理器 - 合并所有进度管理功能
 * @date: 2025-06-13
 * @version: 3.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProgressManager {

    @Qualifier("stringRedisTemplate")
    private final StringRedisTemplate redisTemplate;
    private final RedissonClient redissonClient;
    private final ObjectMapper objectMapper;

    // 内存缓存，提升性能
    private final ConcurrentHashMap<String, ProgressContext> progressCache = new ConcurrentHashMap<>();

    private static final String PROGRESS_REDIS_KEY = "data:import:progress:hash";
    private static final int PROGRESS_EXPIRE_HOURS = 24;

    /**
     * 创建进度跟踪器
     */
    public ProgressTracker createTracker(String taskId, int totalRecords, int totalBatches) {
        ProgressContext context = new ProgressContext(taskId, totalRecords, totalBatches);
        progressCache.put(taskId, context);
        
        log.info("流量平台-----> 创建进度跟踪器，任务ID: {}，总记录: {}，总批次: {}", 
                taskId, totalRecords, totalBatches);
        
        return new ProgressTracker(context, this);
    }

    /**
     * 获取进度信息
     */
    public DataImportProgressDTO getProgress(String taskId) {
        // 先从内存缓存获取
        ProgressContext context = progressCache.get(taskId);
        if (context != null) {
            return context.toDTO();
        }

        // 从Redis获取
        try {
            HashOperations<String, String, String> hashOps = redisTemplate.opsForHash();
            String progressJson = hashOps.get(PROGRESS_REDIS_KEY, taskId);
            if (progressJson != null) {
                return objectMapper.readValue(progressJson, DataImportProgressDTO.class);
            }
        } catch (Exception e) {
            log.error("流量平台-----> 从Redis获取进度失败，任务ID: {}", taskId, e);
        }

        return null;
    }

    /**
     * 更新进度到Redis - 遵循 RedisConfig 配置
     */
    public void updateProgress(ProgressContext context) {
        try {
            DataImportProgressDTO dto = context.toDTO();
            String progressJson = objectMapper.writeValueAsString(dto);

            HashOperations<String, String, String> hashOps = redisTemplate.opsForHash();
            hashOps.put(PROGRESS_REDIS_KEY, context.getTaskId(), progressJson);
            redisTemplate.expire(PROGRESS_REDIS_KEY, Duration.ofHours(PROGRESS_EXPIRE_HOURS));

            log.debug("流量平台-----> 进度已更新到Redis，任务ID: {}，进度: {:.2f}%",
                    context.getTaskId(), dto.getProgressPercentage());

        } catch (JsonProcessingException e) {
            log.error("流量平台-----> 序列化进度失败，任务ID: {}", context.getTaskId(), e);
        } catch (Exception e) {
            log.error("流量平台-----> 保存进度到Redis失败，任务ID: {}", context.getTaskId(), e);
        }
    }

    /**
     * 标记任务失败
     */
    public void failTask(String taskId, String errorMessage) {
        ProgressContext context = progressCache.get(taskId);
        if (context != null) {
            context.fail(errorMessage);
            updateProgress(context);
        }
    }

    /**
     * 清理过期进度
     */
    public void cleanupExpiredProgress() {
        LocalDateTime cutoff = LocalDateTime.now().minusHours(PROGRESS_EXPIRE_HOURS);
        
        progressCache.entrySet().removeIf(entry -> {
            ProgressContext context = entry.getValue();
            return context.getStartTime().isBefore(cutoff);
        });
        
        log.info("流量平台-----> 清理过期进度缓存完成");
    }

    /**
     * 进度上下文 - 内部类
     */
    public static class ProgressContext {
        private final String taskId;
        private final int totalRecords;
        private final int totalBatches;
        private final LocalDateTime startTime;
        
        private final AtomicInteger processedRecords = new AtomicInteger(0);
        private final AtomicInteger successRecords = new AtomicInteger(0);
        private final AtomicInteger failedRecords = new AtomicInteger(0);
        private final AtomicInteger currentBatch = new AtomicInteger(0);
        
        private volatile ProgressStatus status = ProgressStatus.PENDING;
        private volatile String errorMessage;
        private volatile LocalDateTime endTime;
        private volatile Double processingSpeed;
        private volatile Long estimatedRemainingSeconds;

        public ProgressContext(String taskId, int totalRecords, int totalBatches) {
            this.taskId = taskId;
            this.totalRecords = totalRecords;
            this.totalBatches = totalBatches;
            this.startTime = LocalDateTime.now();
        }

        public void updateBatch(int processed, int success, int failed) {
            processedRecords.addAndGet(processed);
            successRecords.addAndGet(success);
            failedRecords.addAndGet(failed);
            currentBatch.incrementAndGet();
            
            if (status == ProgressStatus.PENDING) {
                status = ProgressStatus.IMPORTING;
            }
            
            calculatePerformanceMetrics();
        }

        public void complete() {
            status = ProgressStatus.COMPLETED;
            endTime = LocalDateTime.now();
            estimatedRemainingSeconds = 0L;
        }

        public void fail(String errorMessage) {
            status = ProgressStatus.FAILED;
            this.errorMessage = errorMessage;
            endTime = LocalDateTime.now();
            estimatedRemainingSeconds = 0L;
        }

        private void calculatePerformanceMetrics() {
            LocalDateTime now = LocalDateTime.now();
            long elapsedSeconds = java.time.Duration.between(startTime, now).getSeconds();
            
            if (elapsedSeconds > 0) {
                processingSpeed = (double) processedRecords.get() / elapsedSeconds;
                
                int remaining = totalRecords - processedRecords.get();
                if (remaining > 0 && processingSpeed > 0) {
                    long basicEstimate = (long) (remaining / processingSpeed);
                    
                    if (elapsedSeconds < 60) {
                        estimatedRemainingSeconds = null; // 显示"计算中"
                    } else if (elapsedSeconds < 300) {
                        estimatedRemainingSeconds = (long) (basicEstimate * 0.8);
                    } else {
                        estimatedRemainingSeconds = basicEstimate;
                    }
                    
                    if (estimatedRemainingSeconds != null && estimatedRemainingSeconds > 86400) {
                        estimatedRemainingSeconds = 86400L; // 24小时上限
                    }
                } else {
                    estimatedRemainingSeconds = 0L;
                }
            }
        }

        public DataImportProgressDTO toDTO() {
            // 获取0-1之间的进度比例（前端统一格式）
            double progressPercentage = status.calculateActualPercentage(processedRecords.get(), totalRecords);
            // 保留4位小数，确保精度
            progressPercentage = Math.round(progressPercentage * 10000.0) / 10000.0;

            return DataImportProgressDTO.builder()
                    .taskId(taskId)
                    .status(status.getStatusCode()) // 使用英文状态码
                    .currentBatch(currentBatch.get())
                    .totalBatches(totalBatches)
                    .processedRecords(processedRecords.get())
                    .totalRecords(totalRecords)
                    .successRecords(successRecords.get())
                    .failedRecords(failedRecords.get())
                    .progressPercentage(progressPercentage) // 0-1之间的小数
                    .estimatedRemainingSeconds(estimatedRemainingSeconds)
                    .processingSpeed(processingSpeed)
                    .lastUpdateTime(LocalDateTime.now())
                    .errorMessage(errorMessage)
                    .build();
        }

        // Getters
        public String getTaskId() { return taskId; }
        public LocalDateTime getStartTime() { return startTime; }
    }

    /**
     * 进度跟踪器 - 内部类
     */
    public static class ProgressTracker {
        private final ProgressContext context;
        private final ProgressManager manager;

        public ProgressTracker(ProgressContext context, ProgressManager manager) {
            this.context = context;
            this.manager = manager;
        }

        public void updateBatch(int processed, int success, int failed) {
            context.updateBatch(processed, success, failed);
            manager.updateProgress(context);
        }

        public void addFailed(int failed) {
            context.failedRecords.addAndGet(failed);
            context.processedRecords.addAndGet(failed);
            manager.updateProgress(context);
        }

        public void complete() {
            context.complete();
            manager.updateProgress(context);
        }

        public void fail(String errorMessage) {
            context.fail(errorMessage);
            manager.updateProgress(context);
        }

        /**
         * 获取任务ID
         */
        public String getTaskId() {
            return context.getTaskId();
        }
    }
}
