package com.iptv.flux.service.config;

import lombok.extern.slf4j.Slf4j;
import org.jooq.ExecuteContext;
import org.jooq.impl.DefaultExecuteListener;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.config
 * @className: JooqExecuteListener
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 12:56
 * @version: 1.0
 */
@Slf4j
public class JooqExecuteListener extends DefaultExecuteListener {
    private static final long SLOW_QUERY_THRESHOLD_MS = 200;
    private long start;

    @Override
    public void executeStart(ExecuteContext ctx) {
        start = System.currentTimeMillis();
    }

    @Override
    public void executeEnd(ExecuteContext ctx) {
        long duration = System.currentTimeMillis() - start;
        if (duration > SLOW_QUERY_THRESHOLD_MS) {
            log.warn("慢查询检测到 ({}ms): {}", duration, ctx.sql());
        } else if (log.isDebugEnabled()) {
            log.debug("查询执行用时{}ms: {}", duration, ctx.sql());
        }
    }

    @Override
    public void exception(ExecuteContext ctx) {
        log.error("Error executing query: {}", ctx.sql(), ctx.exception());
    }
}
