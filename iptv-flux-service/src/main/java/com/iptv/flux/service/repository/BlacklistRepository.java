package com.iptv.flux.service.repository;

import com.iptv.flux.common.dto.BlacklistUserDTO;
import com.iptv.flux.common.dto.ListQueryRequestDTO;
import com.iptv.flux.common.dto.PagedResponseDTO;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.Result;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.jooq.impl.DSL.*;

/**
 * 黑名单数据访问层
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class BlacklistRepository {

    private final DSLContext dsl;
    private final MetricsRegistryUtil metricsRegistry;

    // 表名和字段定义
    private static final String BLACKLIST_TABLE = "user_blacklist";
    private static final org.jooq.Field<Long> ID = field("id", Long.class);
    private static final org.jooq.Field<String> USER_ID = field("user_id", String.class);
    private static final org.jooq.Field<String> SOURCE = field("source", String.class);
    private static final org.jooq.Field<String> REMARK = field("remark", String.class);
    private static final org.jooq.Field<Timestamp> ADD_TIME = field("add_time", Timestamp.class);
    private static final org.jooq.Field<Timestamp> CREATED_AT = field("created_at", Timestamp.class);
    private static final org.jooq.Field<Timestamp> UPDATED_AT = field("updated_at", Timestamp.class);

    /**
     * 分页查询黑名单列表
     */
    @Timed(value = "repository.blacklist.findPage", percentiles = {0.5, 0.95, 0.99})
    public PagedResponseDTO<BlacklistUserDTO> findPage(ListQueryRequestDTO request) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始分页查询黑名单，页码: {}, 每页: {}", request.getPage(), request.getPageSize());

        try {
            // 构建查询条件
            Condition condition = trueCondition();
            
            if (request.getUserId() != null && !request.getUserId().trim().isEmpty()) {
                condition = condition.and(USER_ID.like("%" + request.getUserId().trim() + "%"));
            }
            
            if (request.getSource() != null && !request.getSource().trim().isEmpty()) {
                condition = condition.and(SOURCE.eq(request.getSource().trim()));
            }

            // 查询总数
            Long total = dsl.selectCount()
                    .from(table(BLACKLIST_TABLE))
                    .where(condition)
                    .fetchOne(0, Long.class);

            // 查询数据
            int offset = (request.getPage() - 1) * request.getPageSize();
            Result<Record> records = dsl.select()
                    .from(table(BLACKLIST_TABLE))
                    .where(condition)
                    .orderBy(ADD_TIME.desc())
                    .limit(request.getPageSize())
                    .offset(offset)
                    .fetch();

            // 转换为DTO
            List<BlacklistUserDTO> list = new ArrayList<>();
            for (Record record : records) {
                BlacklistUserDTO dto = convertToDTO(record);
                list.add(dto);
            }

            log.debug("流量平台-----> 分页查询黑名单完成，总数: {}, 当前页数据: {}, 耗时: {}ms",
                    total, list.size(), System.currentTimeMillis() - startTime);

            return PagedResponseDTO.of(list, total, request.getPage(), request.getPageSize());

        } catch (Exception e) {
            log.error("流量平台-----> 分页查询黑名单失败", e);
            metricsRegistry.incrementCounter("repository.blacklist.findPage.error");
            throw e;
        }
    }

    /**
     * 添加黑名单用户
     */
    @Timed(value = "repository.blacklist.add", percentiles = {0.5, 0.95, 0.99})
    public void addUser(BlacklistUserDTO dto) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始添加黑名单用户: {}, 来源: {}", dto.getUserId(), dto.getSource());

        try {
            Timestamp now = Timestamp.valueOf(LocalDateTime.now());
            
            int result = dsl.insertInto(table(BLACKLIST_TABLE))
                    .set(USER_ID, dto.getUserId())
                    .set(SOURCE, dto.getSource())
                    .set(REMARK, dto.getRemark())
                    .set(ADD_TIME, now)
                    .set(CREATED_AT, now)
                    .set(UPDATED_AT, now)
                    .execute();

            if (result > 0) {
                log.info("流量平台-----> 成功添加黑名单用户: {}, 来源: {}, 耗时: {}ms",
                        dto.getUserId(), dto.getSource(), System.currentTimeMillis() - startTime);
                metricsRegistry.incrementCounter("repository.blacklist.add.success");
            } else {
                log.warn("流量平台-----> 添加黑名单用户失败，未影响任何行: {}", dto.getUserId());
            }

        } catch (Exception e) {
            log.error("流量平台-----> 添加黑名单用户失败: {}", dto.getUserId(), e);
            metricsRegistry.incrementCounter("repository.blacklist.add.error");
            throw e;
        }
    }

    /**
     * 删除黑名单用户
     */
    @Timed(value = "repository.blacklist.delete", percentiles = {0.5, 0.95, 0.99})
    public boolean deleteUser(String id) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始删除黑名单用户: {}", id);

        try {
            int result = dsl.deleteFrom(table(BLACKLIST_TABLE))
                    .where(ID.eq(Long.valueOf(id)))
                    .execute();

            boolean success = result > 0;
            if (success) {
                log.info("流量平台-----> 成功删除黑名单用户: {}, 耗时: {}ms",
                        id, System.currentTimeMillis() - startTime);
                metricsRegistry.incrementCounter("repository.blacklist.delete.success");
            } else {
                log.warn("流量平台-----> 删除黑名单用户失败，记录不存在: {}", id);
            }

            return success;

        } catch (Exception e) {
            log.error("流量平台-----> 删除黑名单用户失败: {}", id, e);
            metricsRegistry.incrementCounter("repository.blacklist.delete.error");
            throw e;
        }
    }

    /**
     * 根据ID查询黑名单用户
     */
    @Timed(value = "repository.blacklist.findById", percentiles = {0.5, 0.95, 0.99})
    public BlacklistUserDTO findById(String id) {
        try {
            Record record = dsl.select()
                    .from(table(BLACKLIST_TABLE))
                    .where(ID.eq(Long.valueOf(id)))
                    .fetchOne();

            if (record != null) {
                return convertToDTO(record);
            }
            return null;

        } catch (Exception e) {
            log.error("流量平台-----> 根据ID查询黑名单用户失败: {}", id, e);
            throw e;
        }
    }

    /**
     * 检查用户是否在黑名单中
     */
    @Timed(value = "repository.blacklist.exists", percentiles = {0.5, 0.95, 0.99})
    public boolean existsUser(String userId, String source) {
        try {
            Integer count = dsl.selectCount()
                    .from(table(BLACKLIST_TABLE))
                    .where(USER_ID.eq(userId))
                    .and(SOURCE.eq(source))
                    .fetchOne(0, Integer.class);

            return count != null && count > 0;

        } catch (Exception e) {
            log.error("流量平台-----> 检查黑名单用户失败: {}, 来源: {}", userId, source, e);
            throw e;
        }
    }

    /**
     * 转换Record为DTO
     */
    private BlacklistUserDTO convertToDTO(Record record) {
        Timestamp addTime = record.get(ADD_TIME);
        
        return BlacklistUserDTO.builder()
                .id(String.valueOf(record.get(ID)))
                .userId(record.get(USER_ID))
                .source(record.get(SOURCE))
                .remark(record.get(REMARK))
                .addTime(addTime != null ? addTime.toLocalDateTime() : null)
                .build();
    }
}
