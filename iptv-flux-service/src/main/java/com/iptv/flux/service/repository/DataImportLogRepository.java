package com.iptv.flux.service.repository;

import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.dto.DataImportResponseDTO;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

import static org.jooq.impl.DSL.*;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.repository
 * @className: DataImportLogRepository
 * @author: chiron
 * @description: 数据导入日志仓储类
 * @date: 2025/1/21 19:00
 * @version: 1.0
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class DataImportLogRepository {

    private final DSLContext dsl;
    private final MetricsRegistryUtil metricsRegistry;

    private static final org.jooq.Table<org.jooq.Record> DATA_IMPORT_LOG_TABLE = table("data_import_log");

    /**
     * 保存导入日志
     */
    @Timed(value = "repository.dataImportLog.save", percentiles = {0.5, 0.95, 0.99})
    public void saveImportLog(DataImportResponseDTO response, String description, boolean asyncMode) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始保存导入日志，任务ID: {}", response.getTaskId());

        try {
            LocalDateTime completedAt = "COMPLETED".equals(response.getStatus()) || "FAILED".equals(response.getStatus()) 
                    ? response.getEndTime() : null;

            dsl.insertInto(DATA_IMPORT_LOG_TABLE)
                    .set(field("task_id"), response.getTaskId())
                    .set(field("file_name"), response.getFileName())
                    .set(field("file_size"), response.getFileSize())
                    .set(field("source"), response.getSource())
                    .set(field("status"), response.getStatus())
                    .set(field("total_records"), response.getTotalRecords() != null ? response.getTotalRecords() : 0)
                    .set(field("processed_records"), response.getProcessedRecords() != null ? response.getProcessedRecords() : 0)
                    .set(field("inserted_records"), response.getInsertedRecords() != null ? response.getInsertedRecords() : 0)
                    .set(field("updated_records"), response.getUpdatedRecords() != null ? response.getUpdatedRecords() : 0)
                    .set(field("failed_records"), response.getFailedRecords() != null ? response.getFailedRecords() : 0)
                    .set(field("processing_time_ms"), response.getDurationMs() != null ? response.getDurationMs() : 0L)
                    .set(field("error_message"), response.getErrorMessage())
                    .set(field("description"), description)
                    .set(field("async_mode"), asyncMode ? 1 : 0)
                    .set(field("created_at"), response.getStartTime())
                    .set(field("completed_at"), completedAt)
                    .execute();

            log.debug("流量平台-----> 保存导入日志完成，任务ID: {}，状态: {}，耗时 {}ms",
                    response.getTaskId(), response.getStatus(), System.currentTimeMillis() - startTime);

            metricsRegistry.incrementCounter(UserGroupConstants.DATA_IMPORT_LOG_SAVE_SUCCESS_METRIC);

        } catch (Exception e) {
            log.error("流量平台-----> 保存导入日志失败，任务ID: {}", response.getTaskId(), e);
            metricsRegistry.incrementCounter(UserGroupConstants.DATA_IMPORT_LOG_SAVE_ERROR_METRIC);
            throw e;
        }
    }

    /**
     * 更新导入日志状态
     */
    @Timed(value = "repository.dataImportLog.updateStatus", percentiles = {0.5, 0.95, 0.99})
    public void updateImportLogStatus(String taskId, String status, String errorMessage) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始更新导入日志状态，任务ID: {}，新状态: {}", taskId, status);

        try {
            LocalDateTime completedAt = "COMPLETED".equals(status) || "FAILED".equals(status) 
                    ? LocalDateTime.now() : null;

            int updatedRows = dsl.update(DATA_IMPORT_LOG_TABLE)
                    .set(field("status"), status)
                    .set(field("error_message"), errorMessage)
                    .set(field("completed_at"), completedAt)
                    .set(field("updated_at"), LocalDateTime.now())
                    .where(field("task_id").eq(taskId))
                    .execute();

            if (updatedRows > 0) {
                log.debug("流量平台-----> 更新导入日志状态完成，任务ID: {}，新状态: {}，耗时 {}ms",
                        taskId, status, System.currentTimeMillis() - startTime);
            } else {
                log.warn("流量平台-----> 未找到要更新的导入日志记录，任务ID: {}", taskId);
            }

        } catch (Exception e) {
            log.error("流量平台-----> 更新导入日志状态失败，任务ID: {}", taskId, e);
            metricsRegistry.incrementCounter(UserGroupConstants.DATA_IMPORT_LOG_UPDATE_ERROR_METRIC);
            throw e;
        }
    }

    /**
     * 更新导入日志完成信息
     */
    @Timed(value = "repository.dataImportLog.updateCompletion", percentiles = {0.5, 0.95, 0.99})
    public void updateImportLogCompletion(DataImportResponseDTO response) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始更新导入日志完成信息，任务ID: {}", response.getTaskId());

        try {
            int updatedRows = dsl.update(DATA_IMPORT_LOG_TABLE)
                    .set(field("status"), response.getStatus())
                    .set(field("total_records"), response.getTotalRecords() != null ? response.getTotalRecords() : 0)
                    .set(field("processed_records"), response.getProcessedRecords() != null ? response.getProcessedRecords() : 0)
                    .set(field("inserted_records"), response.getInsertedRecords() != null ? response.getInsertedRecords() : 0)
                    .set(field("updated_records"), response.getUpdatedRecords() != null ? response.getUpdatedRecords() : 0)
                    .set(field("failed_records"), response.getFailedRecords() != null ? response.getFailedRecords() : 0)
                    .set(field("processing_time_ms"), response.getDurationMs() != null ? response.getDurationMs() : 0L)
                    .set(field("error_message"), response.getErrorMessage())
                    .set(field("completed_at"), response.getEndTime())
                    .set(field("updated_at"), LocalDateTime.now())
                    .where(field("task_id").eq(response.getTaskId()))
                    .execute();

            if (updatedRows > 0) {
                log.debug("流量平台-----> 更新导入日志完成信息成功，任务ID: {}，状态: {}，耗时 {}ms",
                        response.getTaskId(), response.getStatus(), System.currentTimeMillis() - startTime);
            } else {
                log.warn("流量平台-----> 未找到要更新的导入日志记录，任务ID: {}", response.getTaskId());
            }

        } catch (Exception e) {
            log.error("流量平台-----> 更新导入日志完成信息失败，任务ID: {}", response.getTaskId(), e);
            metricsRegistry.incrementCounter(UserGroupConstants.DATA_IMPORT_LOG_UPDATE_ERROR_METRIC);
            throw e;
        }
    }

    /**
     * 检查导入日志是否存在
     */
    @Timed(value = "repository.dataImportLog.exists", percentiles = {0.5, 0.95, 0.99})
    public boolean existsByTaskId(String taskId) {
        try {
            Integer count = dsl.selectCount()
                    .from(DATA_IMPORT_LOG_TABLE)
                    .where(field("task_id").eq(taskId))
                    .fetchOne(0, Integer.class);

            return count != null && count > 0;
        } catch (Exception e) {
            log.error("流量平台-----> 检查导入日志是否存在失败，任务ID: {}", taskId, e);
            return false;
        }
    }
}
