package com.iptv.flux.service.repository;

import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.dto.DataImportResponseDTO;
import com.iptv.flux.common.dto.TrafficSyncResponseDTO;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

import static org.jooq.impl.DSL.*;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.repository
 * @className: DataImportLogRepository
 * @author: chiron
 * @description: 数据导入日志仓储类
 * @date: 2025/1/21 19:00
 * @version: 1.0
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class DataImportLogRepository {

    private final DSLContext dsl;
    private final MetricsRegistryUtil metricsRegistry;

    private static final org.jooq.Table<org.jooq.Record> DATA_IMPORT_LOG_TABLE = table("data_import_log");

    /**
     * 保存导入日志
     */
    @Timed(value = "repository.dataImportLog.save", percentiles = {0.5, 0.95, 0.99})
    public void saveImportLog(DataImportResponseDTO response, String description, boolean asyncMode) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始保存导入日志，任务ID: {}", response.getTaskId());

        try {
            LocalDateTime completedAt = "COMPLETED".equals(response.getStatus()) || "FAILED".equals(response.getStatus()) 
                    ? response.getEndTime() : null;

            dsl.insertInto(DATA_IMPORT_LOG_TABLE)
                    .set(field("task_id"), response.getTaskId())
                    .set(field("file_name"), response.getFileName())
                    .set(field("file_size"), response.getFileSize())
                    .set(field("source"), response.getSource())
                    .set(field("status"), response.getStatus())
                    .set(field("total_records"), response.getTotalRecords() != null ? response.getTotalRecords() : 0)
                    .set(field("processed_records"), response.getProcessedRecords() != null ? response.getProcessedRecords() : 0)
                    .set(field("inserted_records"), response.getInsertedRecords() != null ? response.getInsertedRecords() : 0)
                    .set(field("updated_records"), response.getUpdatedRecords() != null ? response.getUpdatedRecords() : 0)
                    .set(field("failed_records"), response.getFailedRecords() != null ? response.getFailedRecords() : 0)
                    .set(field("processing_time_ms"), response.getDurationMs() != null ? response.getDurationMs() : 0L)
                    .set(field("error_message"), response.getErrorMessage())
                    .set(field("description"), description)
                    .set(field("async_mode"), asyncMode ? 1 : 0)
                    .set(field("created_at"), response.getStartTime())
                    .set(field("completed_at"), completedAt)
                    .execute();

            log.debug("流量平台-----> 保存导入日志完成，任务ID: {}，状态: {}，耗时 {}ms",
                    response.getTaskId(), response.getStatus(), System.currentTimeMillis() - startTime);

            metricsRegistry.incrementCounter(UserGroupConstants.DATA_IMPORT_LOG_SAVE_SUCCESS_METRIC);

        } catch (Exception e) {
            log.error("流量平台-----> 保存导入日志失败，任务ID: {}", response.getTaskId(), e);
            metricsRegistry.incrementCounter(UserGroupConstants.DATA_IMPORT_LOG_SAVE_ERROR_METRIC);
            throw e;
        }
    }

    /**
     * 更新导入日志状态
     */
    @Timed(value = "repository.dataImportLog.updateStatus", percentiles = {0.5, 0.95, 0.99})
    public void updateImportLogStatus(String taskId, String status, String errorMessage) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始更新导入日志状态，任务ID: {}，新状态: {}", taskId, status);

        try {
            LocalDateTime completedAt = "COMPLETED".equals(status) || "FAILED".equals(status) 
                    ? LocalDateTime.now() : null;

            int updatedRows = dsl.update(DATA_IMPORT_LOG_TABLE)
                    .set(field("status"), status)
                    .set(field("error_message"), errorMessage)
                    .set(field("completed_at"), completedAt)
                    .set(field("updated_at"), LocalDateTime.now())
                    .where(field("task_id").eq(taskId))
                    .execute();

            if (updatedRows > 0) {
                log.debug("流量平台-----> 更新导入日志状态完成，任务ID: {}，新状态: {}，耗时 {}ms",
                        taskId, status, System.currentTimeMillis() - startTime);
            } else {
                log.warn("流量平台-----> 未找到要更新的导入日志记录，任务ID: {}", taskId);
            }

        } catch (Exception e) {
            log.error("流量平台-----> 更新导入日志状态失败，任务ID: {}", taskId, e);
            metricsRegistry.incrementCounter(UserGroupConstants.DATA_IMPORT_LOG_UPDATE_ERROR_METRIC);
            throw e;
        }
    }

    /**
     * 更新导入日志完成信息
     */
    @Timed(value = "repository.dataImportLog.updateCompletion", percentiles = {0.5, 0.95, 0.99})
    public void updateImportLogCompletion(DataImportResponseDTO response) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始更新导入日志完成信息，任务ID: {}", response.getTaskId());

        try {
            int updatedRows = dsl.update(DATA_IMPORT_LOG_TABLE)
                    .set(field("status"), response.getStatus())
                    .set(field("total_records"), response.getTotalRecords() != null ? response.getTotalRecords() : 0)
                    .set(field("processed_records"), response.getProcessedRecords() != null ? response.getProcessedRecords() : 0)
                    .set(field("inserted_records"), response.getInsertedRecords() != null ? response.getInsertedRecords() : 0)
                    .set(field("updated_records"), response.getUpdatedRecords() != null ? response.getUpdatedRecords() : 0)
                    .set(field("failed_records"), response.getFailedRecords() != null ? response.getFailedRecords() : 0)
                    .set(field("processing_time_ms"), response.getDurationMs() != null ? response.getDurationMs() : 0L)
                    .set(field("error_message"), response.getErrorMessage())
                    .set(field("completed_at"), response.getEndTime())
                    .set(field("updated_at"), LocalDateTime.now())
                    .where(field("task_id").eq(response.getTaskId()))
                    .execute();

            if (updatedRows > 0) {
                log.debug("流量平台-----> 更新导入日志完成信息成功，任务ID: {}，状态: {}，耗时 {}ms",
                        response.getTaskId(), response.getStatus(), System.currentTimeMillis() - startTime);
            } else {
                log.warn("流量平台-----> 未找到要更新的导入日志记录，任务ID: {}", response.getTaskId());
            }

        } catch (Exception e) {
            log.error("流量平台-----> 更新导入日志完成信息失败，任务ID: {}", response.getTaskId(), e);
            metricsRegistry.incrementCounter(UserGroupConstants.DATA_IMPORT_LOG_UPDATE_ERROR_METRIC);
            throw e;
        }
    }

    /**
     * 检查导入日志是否存在
     */
    @Timed(value = "repository.dataImportLog.exists", percentiles = {0.5, 0.95, 0.99})
    public boolean existsByTaskId(String taskId) {
        try {
            Integer count = dsl.selectCount()
                    .from(DATA_IMPORT_LOG_TABLE)
                    .where(field("task_id").eq(taskId))
                    .fetchOne(0, Integer.class);

            return count != null && count > 0;
        } catch (Exception e) {
            log.error("流量平台-----> 检查导入日志是否存在失败，任务ID: {}", taskId, e);
            return false;
        }
    }

    /**
     * 保存流量同步日志
     */
    @Timed(value = "repository.trafficSyncLog.save", percentiles = {0.5, 0.95, 0.99})
    public void saveTrafficSyncLog(TrafficSyncResponseDTO response, String description, boolean asyncMode) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始保存流量同步日志，任务ID: {}", response.getTaskId());

        try {
            LocalDateTime completedAt = "COMPLETED".equals(response.getStatus()) ||
                                      "FAILED".equals(response.getStatus()) ||
                                      "PARTIAL_SUCCESS".equals(response.getStatus())
                    ? response.getCompletedTime() : null;

            // 计算总记录数和处理记录数
            int totalRecords = calculateTotalRecords(response);
            int processedRecords = calculateProcessedRecords(response);
            int insertedRecords = calculateInsertedRecords(response);
            int updatedRecords = calculateUpdatedRecords(response);
            int failedRecords = calculateFailedRecords(response);

            dsl.insertInto(DATA_IMPORT_LOG_TABLE)
                    .set(field("task_id"), response.getTaskId())
                    .set(field("file_name"), "traffic_sync_" + response.getTaskId())
                    .set(field("file_size"), 0L) // 流量同步涉及多个文件，这里设为0
                    .set(field("source"), "traffic_sync") // 特殊来源标识
                    .set(field("status"), response.getStatus())
                    .set(field("total_records"), totalRecords)
                    .set(field("processed_records"), processedRecords)
                    .set(field("inserted_records"), insertedRecords)
                    .set(field("updated_records"), updatedRecords)
                    .set(field("failed_records"), failedRecords)
                    .set(field("processing_time_ms"), response.getDurationMs() != null ? response.getDurationMs() : 0L)
                    .set(field("error_message"), response.getErrorMessage())
                    .set(field("description"), description != null ? description : "流量平台数据同步")
                    .set(field("async_mode"), asyncMode ? 1 : 0)
                    .set(field("created_at"), response.getProcessTime())
                    .set(field("completed_at"), completedAt)
                    .execute();

            log.debug("流量平台-----> 保存流量同步日志完成，任务ID: {}，状态: {}，耗时 {}ms",
                    response.getTaskId(), response.getStatus(), System.currentTimeMillis() - startTime);

            metricsRegistry.incrementCounter("traffic.sync.log.save.success");

        } catch (Exception e) {
            log.error("流量平台-----> 保存流量同步日志失败，任务ID: {}", response.getTaskId(), e);
            metricsRegistry.incrementCounter("traffic.sync.log.save.error");
            throw e;
        }
    }

    /**
     * 更新流量同步日志状态
     */
    @Timed(value = "repository.trafficSyncLog.updateStatus", percentiles = {0.5, 0.95, 0.99})
    public void updateTrafficSyncLogStatus(String taskId, String status, String errorMessage) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始更新流量同步日志状态，任务ID: {}，新状态: {}", taskId, status);

        try {
            LocalDateTime completedAt = "COMPLETED".equals(status) ||
                                      "FAILED".equals(status) ||
                                      "PARTIAL_SUCCESS".equals(status)
                    ? LocalDateTime.now() : null;

            int updatedRows = dsl.update(DATA_IMPORT_LOG_TABLE)
                    .set(field("status"), status)
                    .set(field("error_message"), errorMessage)
                    .set(field("completed_at"), completedAt)
                    .set(field("updated_at"), LocalDateTime.now())
                    .where(field("task_id").eq(taskId))
                    .execute();

            if (updatedRows > 0) {
                log.debug("流量平台-----> 更新流量同步日志状态完成，任务ID: {}，新状态: {}，耗时 {}ms",
                        taskId, status, System.currentTimeMillis() - startTime);
            } else {
                log.warn("流量平台-----> 未找到要更新的流量同步日志记录，任务ID: {}", taskId);
            }

            metricsRegistry.incrementCounter("traffic.sync.log.update.success");

        } catch (Exception e) {
            log.error("流量平台-----> 更新流量同步日志状态失败，任务ID: {}", taskId, e);
            metricsRegistry.incrementCounter("traffic.sync.log.update.error");
            throw e;
        }
    }

    /**
     * 更新流量同步日志完成信息
     */
    @Timed(value = "repository.trafficSyncLog.updateCompletion", percentiles = {0.5, 0.95, 0.99})
    public void updateTrafficSyncLogCompletion(TrafficSyncResponseDTO response) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始更新流量同步日志完成信息，任务ID: {}", response.getTaskId());

        try {
            // 计算总记录数和处理记录数
            int totalRecords = calculateTotalRecords(response);
            int processedRecords = calculateProcessedRecords(response);
            int insertedRecords = calculateInsertedRecords(response);
            int updatedRecords = calculateUpdatedRecords(response);
            int failedRecords = calculateFailedRecords(response);

            int updatedRows = dsl.update(DATA_IMPORT_LOG_TABLE)
                    .set(field("status"), response.getStatus())
                    .set(field("total_records"), totalRecords)
                    .set(field("processed_records"), processedRecords)
                    .set(field("inserted_records"), insertedRecords)
                    .set(field("updated_records"), updatedRecords)
                    .set(field("failed_records"), failedRecords)
                    .set(field("processing_time_ms"), response.getDurationMs() != null ? response.getDurationMs() : 0L)
                    .set(field("error_message"), response.getErrorMessage())
                    .set(field("completed_at"), response.getCompletedTime())
                    .set(field("updated_at"), LocalDateTime.now())
                    .where(field("task_id").eq(response.getTaskId()))
                    .execute();

            if (updatedRows > 0) {
                log.debug("流量平台-----> 更新流量同步日志完成信息成功，任务ID: {}，状态: {}，耗时 {}ms",
                        response.getTaskId(), response.getStatus(), System.currentTimeMillis() - startTime);
            } else {
                log.warn("流量平台-----> 未找到要更新的流量同步日志记录，任务ID: {}", response.getTaskId());
            }

            metricsRegistry.incrementCounter("traffic.sync.log.completion.success");

        } catch (Exception e) {
            log.error("流量平台-----> 更新流量同步日志完成信息失败，任务ID: {}", response.getTaskId(), e);
            metricsRegistry.incrementCounter("traffic.sync.log.completion.error");
            throw e;
        }
    }

    /**
     * 计算流量同步总记录数
     */
    private int calculateTotalRecords(TrafficSyncResponseDTO response) {
        int total = 0;

        // 分组信息记录数
        if (response.getGroupsProcessed() != null) {
            total += response.getGroupsProcessed().getTotalRecords() != null ?
                    response.getGroupsProcessed().getTotalRecords() : 0;
        }

        // 用户分组记录数
        if (response.getUsersProcessed() != null) {
            total += response.getUsersProcessed().getTotalUsers() != null ?
                    response.getUsersProcessed().getTotalUsers() : 0;
        }

        // 运营商文件记录数
        if (response.getOperatorResults() != null) {
            for (TrafficSyncResponseDTO.OperatorProcessResult result : response.getOperatorResults()) {
                total += result.getUserCount() != null ? result.getUserCount() : 0;
            }
        }

        return total;
    }

    /**
     * 计算流量同步已处理记录数
     */
    private int calculateProcessedRecords(TrafficSyncResponseDTO response) {
        int processed = 0;

        // 分组信息处理记录数
        if (response.getGroupsProcessed() != null) {
            processed += response.getGroupsProcessed().getProcessedRecords() != null ?
                    response.getGroupsProcessed().getProcessedRecords() : 0;
        }

        // 用户分组处理记录数
        if (response.getUsersProcessed() != null) {
            processed += response.getUsersProcessed().getProcessedUsers() != null ?
                    response.getUsersProcessed().getProcessedUsers() : 0;
        }

        // 运营商文件处理记录数
        if (response.getOperatorResults() != null) {
            for (TrafficSyncResponseDTO.OperatorProcessResult result : response.getOperatorResults()) {
                processed += result.getUserCount() != null ? result.getUserCount() : 0;
            }
        }

        return processed;
    }

    /**
     * 计算流量同步插入记录数
     */
    private int calculateInsertedRecords(TrafficSyncResponseDTO response) {
        int inserted = 0;

        // 分组信息插入记录数
        if (response.getGroupsProcessed() != null) {
            inserted += response.getGroupsProcessed().getInsertedRecords() != null ?
                    response.getGroupsProcessed().getInsertedRecords() : 0;
        }

        // 用户分组插入记录数
        if (response.getUsersProcessed() != null) {
            inserted += response.getUsersProcessed().getInsertedUsers() != null ?
                    response.getUsersProcessed().getInsertedUsers() : 0;
        }

        return inserted;
    }

    /**
     * 计算流量同步更新记录数
     */
    private int calculateUpdatedRecords(TrafficSyncResponseDTO response) {
        int updated = 0;

        // 分组信息更新记录数
        if (response.getGroupsProcessed() != null) {
            updated += response.getGroupsProcessed().getUpdatedRecords() != null ?
                    response.getGroupsProcessed().getUpdatedRecords() : 0;
        }

        // 用户分组更新记录数
        if (response.getUsersProcessed() != null) {
            updated += response.getUsersProcessed().getUpdatedUsers() != null ?
                    response.getUsersProcessed().getUpdatedUsers() : 0;
        }

        return updated;
    }

    /**
     * 计算流量同步失败记录数
     */
    private int calculateFailedRecords(TrafficSyncResponseDTO response) {
        int failed = 0;

        // 分组信息失败记录数
        if (response.getGroupsProcessed() != null) {
            failed += response.getGroupsProcessed().getFailedRecords() != null ?
                    response.getGroupsProcessed().getFailedRecords() : 0;
        }

        // 用户分组失败记录数
        if (response.getUsersProcessed() != null) {
            failed += response.getUsersProcessed().getFailedUsers() != null ?
                    response.getUsersProcessed().getFailedUsers() : 0;
        }

        return failed;
    }
}
