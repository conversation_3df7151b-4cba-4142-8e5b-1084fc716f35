package com.iptv.flux.service.service;

import com.iptv.flux.common.dto.*;
import com.iptv.flux.service.model.entity.BatchProcessResult;
import com.iptv.flux.service.repository.DataImportRepository;
import com.iptv.flux.service.repository.DataImportLogRepository;
import com.iptv.flux.service.util.FTPFileProcessor;
import com.iptv.flux.service.util.GroupsFileParser;
import com.iptv.flux.service.util.UserGroupFileParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.service
 * @className: TrafficSyncService
 * @author: Claude 4.0 sonnet
 * @description: 流量平台数据同步服务 - 整合FTP下载、文件解析、数据入库的完整流程
 * @date: 2025/8/14 12:00
 * @version: 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TrafficSyncService {

    private final FTPFileProcessor ftpFileProcessor;
    private final GroupsFileParser groupsFileParser;
    private final UserGroupFileParser userGroupFileParser;
    private final DataImportRepository dataImportRepository;
    private final DataImportLogRepository dataImportLogRepository;
    private final GroupInfoService groupInfoService;
    private final UserGroupService userGroupService;
    private final DataImportService dataImportService;

    /**
     * 处理流量平台数据同步通知
     *
     * @param notification 同步通知请求
     * @return 同步响应结果
     */
    public TrafficSyncResponseDTO processTrafficSyncNotification(TrafficSyncNotificationDTO notification) {
        String taskId = generateTaskId();
        log.info("流量平台-----> 开始处理数据同步通知，任务ID: {}", taskId);

        TrafficSyncResponseDTO.TrafficSyncResponseDTOBuilder responseBuilder = TrafficSyncResponseDTO.builder()
                .taskId(taskId)
                .processTime(LocalDateTime.now());

        try {
            // 验证通知数据
            validateNotification(notification);

            // 检查是否异步处理
            if (notification.getOptions() != null && Boolean.TRUE.equals(notification.getOptions().getAsync())) {
                // 异步处理
                processTrafficSyncAsync(notification, taskId);
                return responseBuilder
                        .status("PROCESSING")
                        .message("数据同步任务已提交，正在后台处理")
                        .build();
            } else {
                // 同步处理
                return processTrafficSyncSync(notification, taskId);
            }

        } catch (Exception e) {
            log.error("流量平台-----> 处理数据同步通知失败，任务ID: {}", taskId, e);
            return responseBuilder
                    .status("FAILED")
                    .completedTime(LocalDateTime.now())
                    .errorMessage("处理失败: " + e.getMessage())
                    .message("数据同步失败")
                    .build();
        }
    }

    /**
     * 异步处理数据同步
     */
    @Async("dataImportExecutor")
    public CompletableFuture<Void> processTrafficSyncAsync(TrafficSyncNotificationDTO notification, String taskId) {
        log.info("流量平台-----> 开始异步处理数据同步，任务ID: {}", taskId);

        try {
            TrafficSyncResponseDTO result = processTrafficSyncSync(notification, taskId);
            log.info("流量平台-----> 异步数据同步完成，任务ID: {}，状态: {}", taskId, result.getStatus());

            // 这里可以添加结果通知逻辑，比如发送到消息队列或回调接口

        } catch (Exception e) {
            log.error("流量平台-----> 异步数据同步失败，任务ID: {}", taskId, e);
            // 记录失败日志
            saveFailureLog(taskId, "异步处理失败: " + e.getMessage());
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * 同步处理数据同步
     */
    private TrafficSyncResponseDTO processTrafficSyncSync(TrafficSyncNotificationDTO notification, String taskId) {
        long startTime = System.currentTimeMillis();
        log.info("流量平台-----> 开始同步处理数据同步，任务ID: {}", taskId);

        TrafficSyncResponseDTO.TrafficSyncResponseDTOBuilder responseBuilder = TrafficSyncResponseDTO.builder()
                .taskId(taskId)
                .processTime(LocalDateTime.now());

        List<File> tempFiles = new ArrayList<>();

        try {
            // 1. 处理groups.txt文件
            TrafficSyncResponseDTO.GroupProcessResult groupResult = processGroupsFile(
                    notification.getGroupsFileURL(), notification.getOptions());
            responseBuilder.groupsProcessed(groupResult);

            // 2. 处理userGroup.txt文件
            TrafficSyncResponseDTO.UserGroupProcessResult userGroupResult = processUserGroupFile(
                    notification.getUserGroupFileURL(), notification.getOptions());
            responseBuilder.usersProcessed(userGroupResult);

            // 3. 处理运营商文件
            List<TrafficSyncResponseDTO.OperatorProcessResult> operatorResults = new ArrayList<>();
            for (TrafficSyncNotificationDTO.OperatorFileInfo operatorFile : notification.getOperatorFiles()) {
                TrafficSyncResponseDTO.OperatorProcessResult operatorResult = processOperatorFile(
                        operatorFile, notification.getOptions());
                operatorResults.add(operatorResult);
            }
            responseBuilder.operatorResults(operatorResults);

            // 4. 计算总体结果
            String overallStatus = calculateOverallStatus(groupResult, userGroupResult, operatorResults);
            long duration = System.currentTimeMillis() - startTime;

            log.info("流量平台-----> 数据同步处理完成，任务ID: {}，状态: {}，耗时: {}ms",
                    taskId, overallStatus, duration);

            TrafficSyncResponseDTO finalResponse = responseBuilder
                    .status(overallStatus)
                    .completedTime(LocalDateTime.now())
                    .durationMs(duration)
                    .message("数据同步处理完成")
                    .build();

            // 保存同步完成日志
            saveTrafficSyncCompletionLog(finalResponse);

            return finalResponse;

        } catch (Exception e) {
            log.error("流量平台-----> 数据同步处理失败，任务ID: {}", taskId, e);
            return responseBuilder
                    .status("FAILED")
                    .completedTime(LocalDateTime.now())
                    .durationMs(System.currentTimeMillis() - startTime)
                    .errorMessage("处理失败: " + e.getMessage())
                    .message("数据同步失败")
                    .build();
        } finally {
            // 清理临时文件
            cleanupTempFiles(tempFiles);
        }
    }

    /**
     * 处理groups.txt文件
     */
    private TrafficSyncResponseDTO.GroupProcessResult processGroupsFile(String fileURL, 
            TrafficSyncNotificationDTO.ProcessingOptions options) throws Exception {
        
        log.info("流量平台-----> 开始处理groups.txt文件: {}", maskPassword(fileURL));
        
        File tempFile = null;
        try {
            // 1. 下载文件
            tempFile = ftpFileProcessor.downloadFile(fileURL);
            
            // 2. 验证文件格式
            if (!groupsFileParser.isValidGroupsFile(tempFile)) {
                throw new IllegalArgumentException("groups.txt文件格式无效");
            }

            // 3. 获取文件统计信息
            int totalLines = groupsFileParser.getTotalLines(tempFile);
            
            // 4. 解析并处理文件
            AtomicInteger processedCount = new AtomicInteger(0);
            AtomicInteger insertedCount = new AtomicInteger(0);
            AtomicInteger updatedCount = new AtomicInteger(0);
            AtomicInteger failedCount = new AtomicInteger(0);

            groupsFileParser.parseGroupsFile(tempFile, groupInfoBatch -> {
                try {
                    // 批量保存分组信息
                    for (GroupInfoDTO groupInfo : groupInfoBatch) {
                        try {
                            groupInfoService.saveGroupInfo(groupInfo);
                            processedCount.incrementAndGet();
                            // 这里简化处理，实际应该区分插入和更新
                            insertedCount.incrementAndGet();
                        } catch (Exception e) {
                            failedCount.incrementAndGet();
                            log.warn("流量平台-----> 保存分组信息失败: {}", groupInfo.getGroupId(), e);
                        }
                    }
                } catch (Exception e) {
                    log.error("流量平台-----> 批量处理分组信息失败", e);
                    failedCount.addAndGet(groupInfoBatch.size());
                }
            });

            return TrafficSyncResponseDTO.GroupProcessResult.builder()
                    .fileName(getFileNameFromUrl(fileURL))
                    .fileSize(tempFile.length())
                    .totalRecords(totalLines)
                    .processedRecords(processedCount.get())
                    .insertedRecords(insertedCount.get())
                    .updatedRecords(updatedCount.get())
                    .failedRecords(failedCount.get())
                    .status(failedCount.get() == 0 ? "SUCCESS" : "PARTIAL_SUCCESS")
                    .build();

        } finally {
            if (tempFile != null) {
                ftpFileProcessor.cleanupTempFile(tempFile);
            }
        }
    }

    /**
     * 处理userGroup.txt文件
     */
    private TrafficSyncResponseDTO.UserGroupProcessResult processUserGroupFile(String fileURL, 
            TrafficSyncNotificationDTO.ProcessingOptions options) throws Exception {
        
        log.info("流量平台-----> 开始处理userGroup.txt文件: {}", maskPassword(fileURL));
        
        File tempFile = null;
        try {
            // 1. 下载文件
            tempFile = ftpFileProcessor.downloadFile(fileURL);
            
            // 2. 验证文件格式
            if (!userGroupFileParser.isValidUserGroupFile(tempFile)) {
                throw new IllegalArgumentException("userGroup.txt文件格式无效");
            }

            // 3. 获取文件统计信息
            int totalLines = userGroupFileParser.getTotalLines(tempFile);
            
            // 4. 处理所有运营商的用户分组数据
            AtomicInteger totalUsers = new AtomicInteger(0);
            AtomicInteger processedUsers = new AtomicInteger(0);
            AtomicInteger insertedUsers = new AtomicInteger(0);
            AtomicInteger updatedUsers = new AtomicInteger(0);
            AtomicInteger failedUsers = new AtomicInteger(0);

            // 为每个运营商处理用户分组数据
            String[] operators = {"dx", "lt", "yd"};
            for (String operator : operators) {
                processUserGroupForOperator(tempFile, operator, options, 
                        totalUsers, processedUsers, insertedUsers, updatedUsers, failedUsers);
            }

            return TrafficSyncResponseDTO.UserGroupProcessResult.builder()
                    .fileName(getFileNameFromUrl(fileURL))
                    .fileSize(tempFile.length())
                    .totalUsers(totalUsers.get())
                    .processedUsers(processedUsers.get())
                    .insertedUsers(insertedUsers.get())
                    .updatedUsers(updatedUsers.get())
                    .failedUsers(failedUsers.get())
                    .status(failedUsers.get() == 0 ? "SUCCESS" : "PARTIAL_SUCCESS")
                    .build();

        } finally {
            if (tempFile != null) {
                ftpFileProcessor.cleanupTempFile(tempFile);
            }
        }
    }

    /**
     * 为特定运营商处理用户分组数据
     */
    private void processUserGroupForOperator(File tempFile, String operator, 
            TrafficSyncNotificationDTO.ProcessingOptions options,
            AtomicInteger totalUsers, AtomicInteger processedUsers, 
            AtomicInteger insertedUsers, AtomicInteger updatedUsers, AtomicInteger failedUsers) throws Exception {
        
        log.info("流量平台-----> 为运营商 {} 处理用户分组数据", operator);

        // 解析为用户分组映射
        Map<String, List<String>> userGroupMapping = userGroupFileParser.parseToUserGroupMapping(tempFile, operator);
        totalUsers.addAndGet(userGroupMapping.size());

        if (!userGroupMapping.isEmpty()) {
            // 使用现有的高性能批量处理
            BatchProcessResult result = dataImportRepository.batchProcessUserGroups(
                    userGroupMapping, operator,
                    options != null ? options.getOverwrite() : true);

            processedUsers.addAndGet(result.getProcessedCount());
            insertedUsers.addAndGet(result.getInsertedCount());
            updatedUsers.addAndGet(result.getUpdatedCount());
            failedUsers.addAndGet(result.getFailedCount());

            log.info("流量平台-----> 运营商 {} 用户分组处理完成，处理: {}，插入: {}，更新: {}，失败: {}", 
                    operator, result.getProcessedCount(), result.getInsertedCount(), 
                    result.getUpdatedCount(), result.getFailedCount());
        }
    }

    /**
     * 处理运营商文件
     */
    private TrafficSyncResponseDTO.OperatorProcessResult processOperatorFile(
            TrafficSyncNotificationDTO.OperatorFileInfo operatorFile, 
            TrafficSyncNotificationDTO.ProcessingOptions options) throws Exception {
        
        String sysID = operatorFile.getSysID();
        String fileURL = operatorFile.getFileURL();
        
        log.info("流量平台-----> 开始处理运营商文件，运营商: {}，文件: {}", sysID, maskPassword(fileURL));
        
        File tempFile = null;
        try {
            // 1. 下载文件
            tempFile = ftpFileProcessor.downloadFile(fileURL);
            
            // 2. MD5校验（如果提供）
            boolean md5Verified = true;
            if (operatorFile.getMd5sum() != null && !operatorFile.getMd5sum().trim().isEmpty()) {
                md5Verified = ftpFileProcessor.verifyMD5(tempFile, operatorFile.getMd5sum());
                if (!md5Verified && options != null && Boolean.TRUE.equals(options.getVerifyMD5())) {
                    throw new IllegalArgumentException("运营商文件MD5校验失败: " + sysID);
                }
            }

            // 3. 处理文件（这里简化处理，实际可能需要根据文件格式进行不同处理）
            Map<String, List<String>> userGroupMapping = userGroupFileParser.parseToUserGroupMapping(tempFile, sysID);
            
            // 4. 批量处理数据
            BatchProcessResult result = dataImportRepository.batchProcessUserGroups(
                    userGroupMapping, sysID,
                    options != null ? options.getOverwrite() : true);

            return TrafficSyncResponseDTO.OperatorProcessResult.builder()
                    .sysID(sysID)
                    .operatorName(TrafficSyncNotificationDTO.getOperatorName(sysID))
                    .fileName(getFileNameFromUrl(fileURL))
                    .fileSize(tempFile.length())
                    .userCount(result.getProcessedCount())
                    .status(result.getFailedCount() == 0 ? "SUCCESS" : "PARTIAL_SUCCESS")
                    .md5Verified(md5Verified)
                    .build();

        } finally {
            if (tempFile != null) {
                ftpFileProcessor.cleanupTempFile(tempFile);
            }
        }
    }

    /**
     * 验证通知数据
     */
    private void validateNotification(TrafficSyncNotificationDTO notification) {
        if (notification == null) {
            throw new IllegalArgumentException("同步通知数据不能为空");
        }

        if (notification.getUpdateTime() == null || notification.getUpdateTime().trim().isEmpty()) {
            throw new IllegalArgumentException("更新时间不能为空");
        }

        if (notification.getGroupsFileURL() == null || notification.getGroupsFileURL().trim().isEmpty()) {
            throw new IllegalArgumentException("groups.txt文件路径不能为空");
        }

        if (notification.getUserGroupFileURL() == null || notification.getUserGroupFileURL().trim().isEmpty()) {
            throw new IllegalArgumentException("userGroup.txt文件路径不能为空");
        }

        if (notification.getOperatorFiles() == null || notification.getOperatorFiles().isEmpty()) {
            throw new IllegalArgumentException("运营商文件列表不能为空");
        }

        // 验证运营商文件
        Set<String> sysIDs = new HashSet<>();
        for (TrafficSyncNotificationDTO.OperatorFileInfo operatorFile : notification.getOperatorFiles()) {
            if (operatorFile.getSysID() == null || operatorFile.getSysID().trim().isEmpty()) {
                throw new IllegalArgumentException("运营商系统ID不能为空");
            }

            if (!TrafficSyncNotificationDTO.isValidSysID(operatorFile.getSysID())) {
                throw new IllegalArgumentException("无效的运营商系统ID: " + operatorFile.getSysID());
            }

            if (sysIDs.contains(operatorFile.getSysID())) {
                throw new IllegalArgumentException("重复的运营商系统ID: " + operatorFile.getSysID());
            }
            sysIDs.add(operatorFile.getSysID());

            if (operatorFile.getFileURL() == null || operatorFile.getFileURL().trim().isEmpty()) {
                throw new IllegalArgumentException("运营商文件路径不能为空: " + operatorFile.getSysID());
            }
        }

        log.debug("流量平台-----> 通知数据验证通过");
    }

    /**
     * 计算总体处理状态
     */
    private String calculateOverallStatus(TrafficSyncResponseDTO.GroupProcessResult groupResult,
                                        TrafficSyncResponseDTO.UserGroupProcessResult userGroupResult,
                                        List<TrafficSyncResponseDTO.OperatorProcessResult> operatorResults) {

        boolean hasFailure = false;
        boolean hasPartialSuccess = false;

        // 检查分组处理结果
        if (groupResult != null) {
            if ("FAILED".equals(groupResult.getStatus())) {
                hasFailure = true;
            } else if ("PARTIAL_SUCCESS".equals(groupResult.getStatus())) {
                hasPartialSuccess = true;
            }
        }

        // 检查用户分组处理结果
        if (userGroupResult != null) {
            if ("FAILED".equals(userGroupResult.getStatus())) {
                hasFailure = true;
            } else if ("PARTIAL_SUCCESS".equals(userGroupResult.getStatus())) {
                hasPartialSuccess = true;
            }
        }

        // 检查运营商处理结果
        if (operatorResults != null) {
            for (TrafficSyncResponseDTO.OperatorProcessResult result : operatorResults) {
                if ("FAILED".equals(result.getStatus())) {
                    hasFailure = true;
                } else if ("PARTIAL_SUCCESS".equals(result.getStatus())) {
                    hasPartialSuccess = true;
                }
            }
        }

        if (hasFailure) {
            return "FAILED";
        } else if (hasPartialSuccess) {
            return "PARTIAL_SUCCESS";
        } else {
            return "COMPLETED";
        }
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFiles(List<File> tempFiles) {
        for (File tempFile : tempFiles) {
            if (tempFile != null) {
                ftpFileProcessor.cleanupTempFile(tempFile);
            }
        }
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId() {
        return "sync_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 从URL中提取文件名
     */
    private String getFileNameFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return "unknown";
        }

        try {
            int lastSlashIndex = url.lastIndexOf('/');
            if (lastSlashIndex >= 0 && lastSlashIndex < url.length() - 1) {
                return url.substring(lastSlashIndex + 1);
            }
        } catch (Exception e) {
            log.warn("流量平台-----> 从URL提取文件名失败: {}", url);
        }

        return "unknown";
    }

    /**
     * 屏蔽URL中的密码信息
     */
    private String maskPassword(String url) {
        if (url == null) {
            return null;
        }
        return url.replaceAll("://[^:]+:[^@]+@", "://***:***@");
    }

    /**
     * 保存失败日志
     */
    private void saveFailureLog(String taskId, String errorMessage) {
        try {
            dataImportLogRepository.updateTrafficSyncLogStatus(taskId, "FAILED", errorMessage);
            log.debug("流量平台-----> 流量同步失败日志已保存，任务ID: {}", taskId);
        } catch (Exception e) {
            log.error("流量平台-----> 保存流量同步失败日志异常，任务ID: {}", taskId, e);
        }
    }

    /**
     * 保存流量同步开始日志
     */
    private void saveTrafficSyncStartLog(TrafficSyncResponseDTO response, String description, boolean asyncMode) {
        try {
            dataImportLogRepository.saveTrafficSyncLog(response, description, asyncMode);
            log.debug("流量平台-----> 流量同步开始日志已保存，任务ID: {}", response.getTaskId());
        } catch (Exception e) {
            log.error("流量平台-----> 保存流量同步开始日志失败，任务ID: {}", response.getTaskId(), e);
            // 不影响主流程，继续执行
        }
    }

    /**
     * 保存流量同步完成日志
     */
    private void saveTrafficSyncCompletionLog(TrafficSyncResponseDTO response) {
        try {
            dataImportLogRepository.updateTrafficSyncLogCompletion(response);
            log.debug("流量平台-----> 流量同步完成日志已更新，任务ID: {}，状态: {}",
                    response.getTaskId(), response.getStatus());
        } catch (Exception e) {
            log.error("流量平台-----> 更新流量同步完成日志失败，任务ID: {}", response.getTaskId(), e);
            // 不影响主流程
        }
    }
}
