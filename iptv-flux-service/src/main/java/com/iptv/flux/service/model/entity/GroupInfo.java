package com.iptv.flux.service.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.model.entity
 * @className: GroupInfo
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 12:59
 * @version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupInfo {
    private Long id;
    private String groupId;
    private String strategyId;
    private String groupName;
    private String description;
    private String platform;
    private String source;
    private Boolean active;
    private String fileUrl;
    private Long coverage;
    private LocalDateTime generateTime;
    private String md5sum;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
