package com.iptv.flux.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.iptv.flux.service", "com.iptv.flux.common"})
@EnableDiscoveryClient
@EnableScheduling
@Slf4j
public class FluxServiceApplication implements CommandLineRunner {

    @Value("${server.port}")
    public String port;

    public static void main(String[] args) {
        long startTime = System.currentTimeMillis();
        log.info("流量平台-----> 开始启动 FluxServiceApplication");

        SpringApplication.run(FluxServiceApplication.class, args);

        long endTime = System.currentTimeMillis();
        log.info("流量平台-----> FluxServiceApplication 启动完成，总耗时: {}ms", endTime - startTime);
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("流量平台-----> 应用启动完成，Swagger地址：http://127.0.0.1:{}/swagger-ui/index.html#", port);
        log.info("流量平台-----> 系统已准备就绪，可以接收请求");
    }
}