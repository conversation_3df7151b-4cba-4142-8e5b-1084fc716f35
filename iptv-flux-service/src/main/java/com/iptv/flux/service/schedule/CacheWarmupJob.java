package com.iptv.flux.service.schedule;

import com.iptv.flux.common.utils.CacheKeyBuilder;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.model.entity.UserGroupRelation;
import com.iptv.flux.service.repository.UserGroupRepository;
import com.iptv.flux.service.service.UserGroupService;
import io.micrometer.core.instrument.Gauge;
import lombok.extern.slf4j.Slf4j;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static com.iptv.flux.common.constants.UserGroupConstants.*;

/**
 * <AUTHOR>
 * 缓存预热任务
 */
@Slf4j
@Component
@DisallowConcurrentExecution
public class CacheWarmupJob implements Job {

    @Autowired
    private UserGroupRepository userGroupRepository;

    @Autowired
    private UserGroupService userGroupService;

    @Autowired
    private MetricsRegistryUtil metricsRegistry;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RedissonClient redisson;

    @Value("${cache.warmup.batch-size:1000}")
    private int batchSize;

    @Value("${cache.warmup.max-batches:50}")
    private int maxBatches;

    @Value("${cache.warmup.enabled:true}")
    private boolean enabled;

    @Value("${cache.warmup.full-load:false}")
    private boolean fullLoad;

    @Value("${cache.warmup.max-time-minutes:60}")
    private int warmupMaxTimeMinutes;

    @Value("${cache.warmup.max-failed-batches:5}")
    private int maxFailedBatches;

    @Value("${user-group.redis.ttl:43200}")
    private long redisTtl;

    private static final long LOCK_EXPIRE_TIME = 5;


    // 用于保存gauge引用和值
    private final AtomicReference<Gauge> warmupCoverageGauge = new AtomicReference<>();
    private final AtomicReference<Double> warmupCoverageGaugeValue = new AtomicReference<>(0.0);

    /**
     * 执行缓存预热任务
     * @param context
     * @throws JobExecutionException
     */
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (!enabled && !isManualTrigger(context)) {
            log.info("流量平台-----> 缓存预热已禁用。跳过执行。");
            return;
        }

        log.info("流量平台-----> 开始执行缓存预热任务，时间: {}", LocalDateTime.now());
        long startTime = System.currentTimeMillis();
        AtomicInteger totalProcessed = new AtomicInteger(0);
        AtomicInteger successCount = new AtomicInteger(0);

        try {
            // 记录性能指标
            metricsRegistry.createCounter("cache.warmup.execution.count").increment();

            // 检查是否是手动触发，如果是则使用JobDataMap中的参数
            boolean isFullLoad = fullLoad;
            if (isManualTrigger(context)) {
                isFullLoad = context.getMergedJobDataMap().getBooleanValue("fullLoad");
                log.info("流量平台-----> 检测到手动触发。使用触发器中的参数: fullLoad={}", isFullLoad);
            }

            if (isFullLoad) {
                executeFullWarmup(totalProcessed, successCount);
            } else {
                executeIncrementalWarmup(totalProcessed, successCount);
            }

            long duration = System.currentTimeMillis() - startTime;
            log.info("流量平台-----> 缓存预热完成。处理数: {}, 成功数: {}, 耗时: {}ms",
                    totalProcessed.get(), successCount.get(), duration);

            // 记录性能指标
            metricsRegistry.createCounter("cache.warmup.processed.count").increment(totalProcessed.get());
            metricsRegistry.createCounter("cache.warmup.success.count").increment(successCount.get());
            metricsRegistry.createTimer("cache.warmup.duration").record(duration, java.util.concurrent.TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("流量平台-----> 缓存预热期间发生错误", e);
            metricsRegistry.createCounter("cache.warmup.error.count").increment();
            throw new JobExecutionException(e);
        }
    }

    /**
     * 检查是否是手动触发
     * @param context
     * @return
     */
    private boolean isManualTrigger(JobExecutionContext context) {
        return context.getMergedJobDataMap().getBooleanValue("manualTrigger");
    }

    /*private void executeFullWarmup(AtomicInteger totalProcessed, AtomicInteger successCount) {
        log.info("流量平台-----> 执行完整缓存预热");

        int offset = 0;
        boolean hasMore = true;
        int batchCount = 0;

        while (hasMore && batchCount < maxBatches) {
            List<UserGroupRelation> batch = userGroupRepository.findAllWithPagination(offset, batchSize);

            if (batch.isEmpty()) {
                hasMore = false;
            } else {
                processBatch(batch, totalProcessed, successCount);
                offset += batchSize;
                batchCount++;
                log.debug("流量平台-----> 已处理批次 :{}, 大小: {}", batchCount, batch.size());
            }
        }

        if (hasMore && batchCount >= maxBatches) {
            log.info("流量平台-----> 达到最大批次限制 ({}). 将在下次执行时继续。", maxBatches);
        }
    }*/

    /**
     * 执行完整缓存预热
     * @param totalProcessed
     * @param successCount
     */
    private void executeFullWarmup(AtomicInteger totalProcessed, AtomicInteger successCount) {
        log.info("流量平台-----> 执行完整缓存预热");

        // 获取分布式锁确保只有一个实例执行预热
        RLock distributedLock = redisson.getLock(CACHE_WARMUP_GLOBAL_LOCK);

        try {
            // 尝试获取锁，设置较短的等待时间，避免长时间阻塞
            boolean acquired = distributedLock.tryLock(LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
            if (!acquired) {
                log.info("流量平台-----> 另一个实例正在执行预热任务，本实例跳过");
                return;
            }

            // 成功获取锁，执行预热逻辑
            try {
                long startTimeMillis = System.currentTimeMillis();
                // 设置最大执行时间，30分钟
                long timeoutMillis = TimeUnit.MINUTES.toMillis(warmupMaxTimeMinutes);
                int failedBatches = 0;

                try {
                    // 获取数据库中的总记录数
                    long totalRecords = userGroupRepository.countAll();
                    log.info("流量平台-----> 数据库中总记录数: {}", totalRecords);

                    if (totalRecords <= 0) {
                        log.info("流量平台-----> 数据库中没有记录，跳过预热");
                        return;
                    }

                    // 安全计算需要的批次数，避免整数溢出， 向上取整的安全方式
                    int requiredBatches = (int) Math.min(Integer.MAX_VALUE,
                            (totalRecords + batchSize - 1) / batchSize);
                    log.info("流量平台-----> 需要处理的批次数: {}", requiredBatches);

                    // 如果需要的批次数超过最大限制，记录警告
                    if (requiredBatches > maxBatches) {
                        log.warn("流量平台-----> 需要的批次数 ({}) 超过最大限制 ({}), 将分多次执行",
                                requiredBatches, maxBatches);
                    }

                    // 尝试读取上次全局预热进度
                    int startOffset = getGlobalWarmupProgress();
                    log.info("流量平台-----> 从全局偏移量 {} 继续预热", startOffset);

                    // 主处理循环
                    int currentBatch = startOffset / batchSize;
                    int offset = startOffset;

                    for (int batchCount = currentBatch;
                         batchCount < requiredBatches &&
                                 (System.currentTimeMillis() - startTimeMillis) < timeoutMillis;
                         batchCount++) {

                        // 增加轻微延迟减轻数据库压力
                        if (batchCount > currentBatch && batchCount % 10 == 0) {
                            // 每10批次暂停100ms
                            Thread.sleep(100);
                        }

                        try {
                            List<UserGroupRelation> batch = userGroupRepository.findAllWithPagination(offset, batchSize);

                            if (batch.isEmpty()) {
                                log.info("流量平台-----> 批次 {} 返回空结果，提前结束预热", batchCount);
                                break;
                            }

                            processBatch(batch, totalProcessed, successCount);
                            // 保存当前全局进度
                            saveGlobalWarmupProgress(offset + batchSize);

                            offset += batchSize;

                            // 添加进度报告
                            if ((batchCount + 1) % 10 == 0 || batchCount + 1 == requiredBatches) {
                                log.info("流量平台-----> 缓存预热进度: {}/{} 批次 ({}%), 处理记录: {}, 成功: {}",
                                        (batchCount + 1), requiredBatches,
                                        String.format("%.2f", (batchCount + 1) * 100.0 / requiredBatches),
                                        totalProcessed.get(), successCount.get());
                            }

                        } catch (Exception e) {
                            failedBatches++;
                            log.error("流量平台-----> 处理批次 {} 时出错，继续下一批次", batchCount, e);

                            if (failedBatches > maxFailedBatches) {
                                log.error("流量平台-----> 失败批次数超过阈值({}), 中止预热", maxFailedBatches);
                                break;
                            }
                            // 继续下一批次
                            offset += batchSize;
                            metricsRegistry.incrementCounter("cache.warmup.batch.error");
                        }
                    }

                    // 检查是否因为超时而中断
                    boolean isTimeout = (System.currentTimeMillis() - startTimeMillis) >= timeoutMillis;
                    if (isTimeout) {
                        log.warn("流量平台-----> 预热已达到最大执行时间限制({}分钟)，将在下次继续",
                                warmupMaxTimeMinutes);
                    }

                    // 计算和记录结果
                    if (offset < totalRecords) {
                        long remainingRecords = totalRecords - offset;
                        log.info("流量平台-----> 本次预热处理到偏移量 {}, 共完成 {} 条记录, 还有约 {} 条记录将在下次继续",
                                offset, totalProcessed.get(), remainingRecords);
                    } else {
                        log.info("流量平台-----> 缓存预热完成，共处理 {} 条记录，成功 {} 条",
                                totalProcessed.get(), successCount.get());

                        // 完成后清除进度
                        clearGlobalWarmupProgress();
                    }

                    // 记录预热覆盖率指标 - 使用updateGauge避免重复注册
                    if (totalRecords > 0) {
                        double coverageRate = Math.min(100.0, (double) offset * 100.0 / totalRecords);
                        updateGlobalWarmupCoverageMetric(coverageRate);
                    }

                } catch (Exception e) {
                    log.error("流量平台-----> 预热过程中发生严重错误", e);
                    metricsRegistry.incrementCounter("cache.warmup.critical.error");
                } finally {
                    // 记录执行时间
                    long executionTime = System.currentTimeMillis() - startTimeMillis;
                    log.info("流量平台-----> 预热执行总时间: {}ms", executionTime);
                    metricsRegistry.recordExecutionTime("cache.warmup.execution.time", startTimeMillis);
                }
            } finally {
                // 释放分布式锁
                if (distributedLock.isHeldByCurrentThread()) {
                    distributedLock.unlock();
                    log.debug("流量平台-----> 预热任务完成，释放分布式锁");
                }
            }
        } catch (InterruptedException e) {
            log.warn("流量平台-----> 等待预热锁时被中断", e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 执行增量缓存预热
     * @param totalProcessed
     * @param successCount
     */
    private void executeIncrementalWarmup(AtomicInteger totalProcessed, AtomicInteger successCount) {
        log.info("流量平台-----> 执行增量缓存预热");

        // 获取最近更新的记录，默认24小时内的更新
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(24);
        List<UserGroupRelation> recentUpdates = userGroupRepository.findRecentUpdates(cutoffTime, maxBatches * batchSize);

        // 分批处理，避免一次处理太多数据
        for (int i = 0; i < recentUpdates.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, recentUpdates.size());
            List<UserGroupRelation> batch = recentUpdates.subList(i, endIndex);

            processBatch(batch, totalProcessed, successCount);
            log.debug("流量平台-----> 已处理增量批次 #{}, 大小: {}", (i/batchSize)+1, batch.size());

            if ((i/batchSize)+1 >= maxBatches) {
                log.info("流量平台-----> 达到最大批次限制 ({}). 将在下次执行时继续。", maxBatches);
                break;
            }
        }
    }

    /**
     * 处理批用户分组关系
     * @param batch
     * @param totalProcessed
     * @param successCount
     */
    private void processBatch(List<UserGroupRelation> batch, AtomicInteger totalProcessed, AtomicInteger successCount) {
        for (UserGroupRelation relation : batch) {
            try {
                String userId = relation.getUserId();
                String source = relation.getSource();
                String groupIdsStr = relation.getGroupIds();

                if (groupIdsStr != null && !groupIdsStr.isEmpty()) {
                    Set<String> groupIds = new LinkedHashSet<>(Arrays.asList(groupIdsStr.split(",")));

                    // 准备缓存
                    String compositeKey = CacheKeyBuilder.buildUserGroupCompositeKey(source, userId);
                    String redisKey = CacheKeyBuilder.buildUserGroupRedisKey(compositeKey);

                    // 使用UserGroupService的方法更新缓存
                    userGroupService.preloadUserGroupInfo(redisKey, compositeKey, groupIds);
                    successCount.incrementAndGet();
                }
            } catch (Exception e) {
                log.error("流量平台-----> 处理关系ID: {} 时出错", relation.getId(), e);
                metricsRegistry.createCounter("cache.warmup.item.error.count").increment();
            } finally {
                totalProcessed.incrementAndGet();
            }
        }
    }

    /**
     * 保存全局预热进度
     */
    private void saveGlobalWarmupProgress(int offset) {
        try {
            // 使用全局进度键，所有实例共享，过期时间设为12小时
            redisTemplate.opsForValue().set(
                    CACHE_WARMUP_PROGRESS_KEY,
                    String.valueOf(offset),
                    Duration.ofHours(12)
            );
        } catch (Exception e) {
            log.warn("流量平台-----> 保存全局预热进度失败", e);
        }
    }

    /**
     * 获取全局预热进度
     */
    private int getGlobalWarmupProgress() {
        try {
            String progress = redisTemplate.opsForValue().get(CACHE_WARMUP_PROGRESS_KEY);
            if (progress != null) {
                return Integer.parseInt(progress);
            }
        } catch (Exception e) {
            log.warn("流量平台-----> 获取全局预热进度失败", e);
        }
        // 默认从头开始
        return 0;
    }

    /**
     * 清除全局预热进度
     */
    private void clearGlobalWarmupProgress() {
        try {
            redisTemplate.delete(CACHE_WARMUP_PROGRESS_KEY);
        } catch (Exception e) {
            log.warn("流量平台-----> 清除全局预热进度失败", e);
        }
    }

    /**
     * 更新全局预热覆盖率指标
     */
    private void updateGlobalWarmupCoverageMetric(double coverageRate) {
        try {
            // 更新全局覆盖率到Redis，过期时间设为12小时
            redisTemplate.opsForValue().set(
                    CACHE_WARMUP_COVERAGE_KEY,
                    String.valueOf(coverageRate),
                    Duration.ofSeconds(redisTtl)
            );

            // 更新本地指标
            warmupCoverageGaugeValue.set(coverageRate);
            if (warmupCoverageGauge.get() == null) {
                Gauge gauge = metricsRegistry.createGauge(CACHE_WARMUP_COVERAGE_KEY,
                        warmupCoverageGaugeValue, AtomicReference::get);
                warmupCoverageGauge.set(gauge);
            }
        } catch (Exception e) {
            log.warn("流量平台-----> 更新预热覆盖率指标失败", e);
        }
    }
}