package com.iptv.flux.service.controller;

import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.dto.ResultDTO;
import com.iptv.flux.service.service.MonitoringService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 监控指标接口控制器
 * 提供系统监控、缓存统计和性能指标接口
 * <AUTHOR>
 */
@RestController
@RequestMapping(UserGroupConstants.API_MONITOR_PATH)
@RequiredArgsConstructor
@Slf4j
public class MonitoringController {

    private final MonitoringService monitoringService;

    /**
     * 获取系统健康状态
     */
    @GetMapping("/health")
    @Operation(summary = "获取系统健康状态", description = "包括服务可用性、缓存健康状态和响应时间健康状态")
    public ResultDTO<Map<String, Object>> getHealthStatus() {
        log.info("流量平台-----> 收到获取系统健康状态请求");

        try {
            Map<String, Object> healthStatus = monitoringService.getHealthStatus();
            return ResultDTO.success(healthStatus);
        } catch (Exception e) {
            log.error("流量平台-----> 获取系统健康状态失败", e);
            return ResultDTO.fail("获取系统健康状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取系统关键指标
     * 包括请求统计、错误率、缓存命中率等
     */
    @GetMapping("/metrics")
    @Operation(summary = "获取系统关键指标", description = "包括请求统计、错误率、缓存命中率等")
    public ResultDTO<Map<String, Object>> getSystemMetrics() {
        log.info("流量平台-----> 收到获取系统监控指标请求");

        try {
            Map<String, Object> metrics = monitoringService.getSystemMetrics();
            return ResultDTO.success(metrics);
        } catch (Exception e) {
            log.error("流量平台-----> 获取系统监控指标失败", e);
            return ResultDTO.fail("获取系统监控指标失败：" + e.getMessage());
        }
    }

    /**
     * 获取缓存统计信息
     * 包括本地缓存、Redis缓存和布隆过滤器状态
     */
    @GetMapping("/cache")
    @Operation(summary = "获取缓存统计信息", description = "包括本地缓存、Redis缓存和布隆过滤器状态")
    public ResultDTO<Map<String, Object>> getCacheStats() {
        log.info("流量平台-----> 收到获取缓存统计信息请求");

        try {
            Map<String, Object> cacheStats = monitoringService.getCacheStats();
            return ResultDTO.success(cacheStats);
        } catch (Exception e) {
            log.error("流量平台-----> 获取缓存统计信息失败", e);
            return ResultDTO.fail("获取缓存统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取性能指标信息
     * 包括各API和服务层方法的响应时间
     */
    @GetMapping("/performance")
    @Operation(summary = "获取性能指标信息", description = "包括各API和服务层方法的响应时间")
    public ResultDTO<Map<String, Object>> getPerformanceMetrics() {
        log.info("流量平台-----> 收到获取性能指标信息请求");

        try {
            Map<String, Object> performanceMetrics = monitoringService.getPerformanceMetrics();
            return ResultDTO.success(performanceMetrics);
        } catch (Exception e) {
            log.error("流量平台-----> 获取性能指标信息失败", e);
            return ResultDTO.fail("获取性能指标信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有监控指标的完整列表
     */
    @GetMapping("/all")
    @Operation(summary = "获取所有监控指标", description = "返回系统中所有注册的监控指标详情")
    public ResultDTO<List<Map<String, Object>>> getAllMetrics() {
        log.info("流量平台-----> 收到获取所有监控指标请求");

        try {
            List<Map<String, Object>> allMetrics = monitoringService.getAllMetrics();
            return ResultDTO.success(allMetrics);
        } catch (Exception e) {
            log.error("流量平台-----> 获取所有监控指标失败", e);
            return ResultDTO.fail("获取所有监控指标失败：" + e.getMessage());
        }
    }
}