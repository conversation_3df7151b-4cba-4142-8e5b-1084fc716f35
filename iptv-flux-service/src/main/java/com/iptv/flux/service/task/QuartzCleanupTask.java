package com.iptv.flux.service.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.task
 * @className: QuartzCleanupTask
 * @author: chiron
 * @description: Quartz任务清理定时任务 - 定期清理过期的手动任务和触发器
 * @date: 2025-07-31
 * @version: 1.0
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class QuartzCleanupTask {

    private final Scheduler scheduler;

    /**
     * 每30分钟清理一次过期的手动任务和触发器
     */
    @Scheduled(fixedRate = 1800000) // 30分钟 = 1800000毫秒
    public void cleanupExpiredQuartzJobs() {
        try {
            log.debug("流量平台-----> 开始清理过期的Quartz任务和触发器");
            
            int cleanedJobs = 0;
            int cleanedTriggers = 0;

            // 清理过期的手动任务
            for (String groupName : scheduler.getJobGroupNames()) {
                for (JobKey jobKey : scheduler.getJobKeys(GroupMatcher.jobGroupEquals(groupName))) {
                    String jobName = jobKey.getName();
                    
                    // 只清理手动创建的任务
                    if (jobName.startsWith("manualWarmupJob")) {
                        try {
                            // 检查任务是否还在运行
                            boolean isRunning = scheduler.getCurrentlyExecutingJobs().stream()
                                    .anyMatch(context -> context.getJobDetail().getKey().equals(jobKey));
                            
                            if (!isRunning) {
                                // 检查任务的触发器是否还会触发
                                boolean hasActiveTriggers = scheduler.getTriggersOfJob(jobKey).stream()
                                        .anyMatch(Trigger::mayFireAgain);
                                
                                if (!hasActiveTriggers) {
                                    scheduler.deleteJob(jobKey);
                                    cleanedJobs++;
                                    log.debug("流量平台-----> 清理过期手动任务: {}", jobName);
                                }
                            }
                        } catch (Exception e) {
                            log.warn("流量平台-----> 清理任务失败: {}", jobName, e);
                        }
                    }
                }
            }

            // 清理过期的手动触发器
            for (String groupName : scheduler.getTriggerGroupNames()) {
                for (TriggerKey triggerKey : scheduler.getTriggerKeys(GroupMatcher.triggerGroupEquals(groupName))) {
                    String triggerName = triggerKey.getName();
                    
                    // 只清理手动创建的触发器
                    if (triggerName.startsWith("manualWarmupTrigger")) {
                        try {
                            Trigger trigger = scheduler.getTrigger(triggerKey);
                            if (trigger != null && !trigger.mayFireAgain()) {
                                scheduler.unscheduleJob(triggerKey);
                                cleanedTriggers++;
                                log.debug("流量平台-----> 清理过期手动触发器: {}", triggerName);
                            }
                        } catch (Exception e) {
                            log.warn("流量平台-----> 清理触发器失败: {}", triggerName, e);
                        }
                    }
                }
            }

            if (cleanedJobs > 0 || cleanedTriggers > 0) {
                log.info("流量平台-----> Quartz清理完成，删除任务: {} 个，删除触发器: {} 个", 
                        cleanedJobs, cleanedTriggers);
            }
            
        } catch (Exception e) {
            log.error("流量平台-----> 清理过期Quartz任务失败", e);
        }
    }

    /**
     * 每天凌晨3点执行深度清理
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void deepCleanupQuartzJobs() {
        try {
            log.info("流量平台-----> 开始执行Quartz深度清理");
            
            int totalJobs = 0;
            int totalTriggers = 0;
            
            // 统计所有任务和触发器数量
            for (String groupName : scheduler.getJobGroupNames()) {
                totalJobs += scheduler.getJobKeys(GroupMatcher.jobGroupEquals(groupName)).size();
            }
            
            for (String groupName : scheduler.getTriggerGroupNames()) {
                totalTriggers += scheduler.getTriggerKeys(GroupMatcher.triggerGroupEquals(groupName)).size();
            }
            
            log.info("流量平台-----> Quartz状态统计 - 总任务数: {}, 总触发器数: {}, 调度器运行状态: {}", 
                    totalJobs, totalTriggers, !scheduler.isInStandbyMode());
            
            // 执行常规清理
            cleanupExpiredQuartzJobs();
            
        } catch (Exception e) {
            log.error("流量平台-----> Quartz深度清理失败", e);
        }
    }
}
