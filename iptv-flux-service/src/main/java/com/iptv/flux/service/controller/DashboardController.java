package com.iptv.flux.service.controller;

import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.dto.ResultDTO;
import com.iptv.flux.common.swagger.SwaggerAnnotations;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.service.DashboardService;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.controller
 * @className: DashboardController
 * @author: chiron
 * @description: 仪表盘数据控制器
 * @date: 2025/1/21 15:00
 * @version: 1.0
 */
@RestController
@RequestMapping(UserGroupConstants.API_DASHBOARD_PATH)
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "仪表盘管理", description = "仪表盘数据统计与展示接口")
public class DashboardController {

    private final DashboardService dashboardService;
    private final MetricsRegistryUtil metricsRegistry;

    /**
     * 获取仪表盘核心统计数据
     */
    @GetMapping(value = "/statistics", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.dashboard.statistics", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取仪表盘核心统计数据",
            description = "获取总用户数、分组总数、缓存用户数、今日操作数等核心指标",
            responseType = Map.class
    )
    public ResultDTO<Map<String, Object>> getDashboardStatistics() {
        log.info("流量平台-----> 收到获取仪表盘核心统计数据请求");

        try {
            Map<String, Object> statistics = dashboardService.getDashboardStatistics();
            log.debug("流量平台-----> 成功获取仪表盘核心统计数据，包含 {} 个指标", statistics.size());
            return ResultDTO.success(statistics);
        } catch (Exception e) {
            log.error("流量平台-----> 获取仪表盘核心统计数据失败", e);
            metricsRegistry.incrementCounter("dashboard.statistics.error");
            return ResultDTO.fail("获取统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取运营商用户分布
     */
    @GetMapping(value = "/operators", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.dashboard.operators", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取运营商用户分布",
            description = "获取电信、联通、移动三个运营商的用户分布统计",
            responseType = Map.class
    )
    public ResultDTO<Map<String, Object>> getOperatorDistribution() {
        log.info("流量平台-----> 收到获取运营商用户分布请求");

        try {
            Map<String, Object> distribution = dashboardService.getOperatorDistribution();
            log.debug("流量平台-----> 成功获取运营商用户分布数据");
            return ResultDTO.success(distribution);
        } catch (Exception e) {
            log.error("流量平台-----> 获取运营商用户分布失败", e);
            metricsRegistry.incrementCounter("dashboard.operators.error");
            return ResultDTO.fail("获取运营商分布失败：" + e.getMessage());
        }
    }

    /**
     * 获取热门分组TOP N
     */
    @GetMapping(value = "/top-groups", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.dashboard.topGroups", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取热门分组TOP N",
            description = "获取用户数最多的前N个分组统计",
            responseType = List.class
    )
    public ResultDTO<List<Map<String, Object>>> getTopGroups(
            @RequestParam(defaultValue = "5") int limit) {
        log.info("流量平台-----> 收到获取热门分组TOP{}请求", limit);

        // 参数校验
        if (limit < 1 || limit > 20) {
            log.warn("流量平台-----> 热门分组查询限制参数无效: {}，使用默认值5", limit);
            limit = 5;
        }

        try {
            List<Map<String, Object>> topGroups = dashboardService.getTopGroups(limit);
            log.debug("流量平台-----> 成功获取热门分组TOP{}数据，共 {} 个分组", limit, topGroups.size());
            return ResultDTO.success(topGroups);
        } catch (Exception e) {
            log.error("流量平台-----> 获取热门分组TOP{}失败", limit, e);
            metricsRegistry.incrementCounter("dashboard.topGroups.error");
            return ResultDTO.fail("获取热门分组失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户增长趋势
     */
    @GetMapping(value = "/trends", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.dashboard.trends", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取用户增长趋势",
            description = "获取各运营商最近N天的用户增长趋势数据",
            responseType = Map.class
    )
    public ResultDTO<Map<String, Object>> getUserTrends(
            @RequestParam(defaultValue = "7") int days) {
        log.info("流量平台-----> 收到获取{}日用户增长趋势请求", days);

        // 参数校验
        if (days < 1 || days > 30) {
            log.warn("流量平台-----> 用户趋势查询天数参数无效: {}，使用默认值7", days);
            days = 7;
        }

        try {
            Map<String, Object> trends = dashboardService.getUserTrends(days);
            log.debug("流量平台-----> 成功获取{}日用户增长趋势数据", days);
            return ResultDTO.success(trends);
        } catch (Exception e) {
            log.error("流量平台-----> 获取{}日用户增长趋势失败", days, e);
            metricsRegistry.incrementCounter("dashboard.trends.error");
            return ResultDTO.fail("获取用户趋势失败：" + e.getMessage());
        }
    }

    /**
     * 获取系统状态
     */
    @GetMapping(value = "/system-status", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.dashboard.systemStatus", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取系统状态",
            description = "获取缓存状态、调度器状态、命中率等系统运行状态",
            responseType = Map.class
    )
    public ResultDTO<Map<String, Object>> getSystemStatus() {
        log.info("流量平台-----> 收到获取系统状态请求");

        try {
            Map<String, Object> systemStatus = dashboardService.getSystemStatus();
            log.debug("流量平台-----> 成功获取系统状态数据");
            return ResultDTO.success(systemStatus);
        } catch (Exception e) {
            log.error("流量平台-----> 获取系统状态失败", e);
            metricsRegistry.incrementCounter("dashboard.systemStatus.error");
            return ResultDTO.fail("获取系统状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取实时活动数据
     */
    @GetMapping(value = "/activities", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.dashboard.activities", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取实时活动数据",
            description = "获取最近的用户操作活动记录",
            responseType = List.class
    )
    public ResultDTO<List<Map<String, Object>>> getRecentActivities(
            @RequestParam(defaultValue = "10") int limit) {
        log.info("流量平台-----> 收到获取最近{}条活动记录请求", limit);

        // 参数校验
        if (limit < 1 || limit > 50) {
            log.warn("流量平台-----> 活动记录查询限制参数无效: {}，使用默认值10", limit);
            limit = 10;
        }

        try {
            List<Map<String, Object>> activities = dashboardService.getRecentActivities(limit);
            log.debug("流量平台-----> 成功获取最近{}条活动记录，实际返回 {} 条", limit, activities.size());
            return ResultDTO.success(activities);
        } catch (Exception e) {
            log.error("流量平台-----> 获取最近活动记录失败", e);
            metricsRegistry.incrementCounter("dashboard.activities.error");
            return ResultDTO.fail("获取活动记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取数据导入统计
     */
    @GetMapping(value = "/import-stats", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.dashboard.importStats", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取数据导入统计",
            description = "获取数据导入相关的统计信息，包括成功率、处理记录数等",
            responseType = Map.class
    )
    public ResultDTO<Map<String, Object>> getDataImportStatistics() {
        log.info("流量平台-----> 收到获取数据导入统计请求");

        try {
            Map<String, Object> importStats = dashboardService.getDataImportStatistics();
            log.debug("流量平台-----> 成功获取数据导入统计信息");
            return ResultDTO.success(importStats);
        } catch (Exception e) {
            log.error("流量平台-----> 获取数据导入统计失败", e);
            metricsRegistry.incrementCounter("dashboard.importStats.error");
            return ResultDTO.fail("获取导入统计失败：" + e.getMessage());
        }
    }

    /**
     * 健康检查端点 - 兼容性保留
     */
    @GetMapping(value = "/health", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.QueryOperation(
            summary = "仪表盘服务健康检查",
            description = "检查仪表盘相关服务的健康状态，建议使用 /api/monitor/health 获取完整系统状态",
            responseType = String.class
    )
    public ResultDTO<String> healthCheck() {
        log.debug("流量平台-----> 收到仪表盘健康检查请求");
        return ResultDTO.success("仪表盘服务运行正常");
    }

}
