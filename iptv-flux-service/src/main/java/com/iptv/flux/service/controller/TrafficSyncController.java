package com.iptv.flux.service.controller;

import com.iptv.flux.common.dto.ResultDTO;
import com.iptv.flux.common.dto.TrafficSyncNotificationDTO;
import com.iptv.flux.common.dto.TrafficSyncResponseDTO;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.service.TrafficSyncService;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.controller
 * @className: TrafficSyncController
 * @author: Claude 4.0 sonnet
 * @description: 流量平台数据同步控制器 - 提供/api/traffic/sync/notification接口
 * @date: 2025/8/14 12:00
 * @version: 1.0
 */
@RestController
@RequestMapping("/api/traffic/sync")
@RequiredArgsConstructor
@Validated
@Slf4j
@Tag(name = "流量平台数据同步", description = "流量平台数据同步相关接口")
public class TrafficSyncController {

    private final TrafficSyncService trafficSyncService;
    private final MetricsRegistryUtil metricsRegistry;

    /**
     * 接收流量平台数据同步通知
     *
     * @param notification 同步通知请求
     * @param request HTTP请求对象
     * @return 同步处理结果
     */
    @PostMapping(value = "/notification", 
                 consumes = MediaType.APPLICATION_JSON_VALUE,
                 produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.traffic.sync.notification", percentiles = {0.5, 0.95, 0.99})
    @Operation(
        summary = "接收流量平台数据同步通知",
        description = "接收流量平台的文件更新通知，并从FTP服务器获取相关文件数据，解析后存储到本地数据库中"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "处理成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ResponseEntity<ResultDTO<TrafficSyncResponseDTO>> processNotification(
            @Parameter(description = "流量平台数据同步通知", required = true)
            @Valid @RequestBody TrafficSyncNotificationDTO notification,
            HttpServletRequest request) {

        long startTime = System.currentTimeMillis();
        String clientIp = getClientIp(request);
        
        log.info("流量平台-----> 接收到数据同步通知，客户端IP: {}，更新时间: {}", 
                clientIp, notification.getUpdateTime());

        // 记录请求指标
        metricsRegistry.incrementCounter("traffic.sync.notification.total");

        try {
            // 处理同步通知
            TrafficSyncResponseDTO response = trafficSyncService.processTrafficSyncNotification(notification);
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("流量平台-----> 数据同步通知处理完成，任务ID: {}，状态: {}，耗时: {}ms", 
                    response.getTaskId(), response.getStatus(), duration);

            // 记录成功指标
            metricsRegistry.incrementCounter("traffic.sync.notification.success");
            metricsRegistry.recordExecutionTime("traffic.sync.notification.duration", startTime);

            return ResponseEntity.ok(ResultDTO.success(response));

        } catch (IllegalArgumentException e) {
            // 参数验证错误
            log.warn("流量平台-----> 数据同步通知参数错误，客户端IP: {}，错误: {}", clientIp, e.getMessage());
            metricsRegistry.incrementCounter("traffic.sync.notification.validation_error");
            
            return ResponseEntity.badRequest()
                    .body(ResultDTO.fail(400, "请求参数错误: " + e.getMessage()));

        } catch (Exception e) {
            // 系统错误
            log.error("流量平台-----> 数据同步通知处理失败，客户端IP: {}", clientIp, e);
            metricsRegistry.incrementCounter("traffic.sync.notification.error");
            
            return ResponseEntity.internalServerError()
                    .body(ResultDTO.fail(500, "服务器内部错误: " + e.getMessage()));
        }
    }

    /**
     * 查询同步任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    @GetMapping(value = "/status/{taskId}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.traffic.sync.status", percentiles = {0.5, 0.95, 0.99})
    @Operation(
        summary = "查询同步任务状态",
        description = "根据任务ID查询数据同步任务的处理状态和结果"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "404", description = "任务不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ResponseEntity<ResultDTO<TrafficSyncResponseDTO>> getTaskStatus(
            @Parameter(description = "任务ID", required = true, example = "sync_1723622400000_abc123")
            @PathVariable String taskId,
            HttpServletRequest request) {

        String clientIp = getClientIp(request);
        log.info("流量平台-----> 查询同步任务状态，任务ID: {}，客户端IP: {}", taskId, clientIp);

        try {
            // 这里可以实现任务状态查询逻辑
            // 目前简化处理，返回任务不存在
            log.warn("流量平台-----> 任务状态查询功能暂未实现，任务ID: {}", taskId);
            
            return ResponseEntity.notFound().build();

        } catch (Exception e) {
            log.error("流量平台-----> 查询同步任务状态失败，任务ID: {}，客户端IP: {}", taskId, clientIp, e);
            
            return ResponseEntity.internalServerError()
                    .body(ResultDTO.fail(500, "查询任务状态失败: " + e.getMessage()));
        }
    }

    /**
     * 获取同步统计信息
     *
     * @return 同步统计数据
     */
    @GetMapping(value = "/statistics", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.traffic.sync.statistics", percentiles = {0.5, 0.95, 0.99})
    @Operation(
        summary = "获取同步统计信息",
        description = "获取流量平台数据同步的统计信息，包括成功次数、失败次数、平均处理时间等"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ResponseEntity<ResultDTO<Object>> getStatistics(HttpServletRequest request) {

        String clientIp = getClientIp(request);
        log.info("流量平台-----> 查询同步统计信息，客户端IP: {}", clientIp);

        try {
            // 这里可以实现统计信息查询逻辑
            // 目前简化处理，返回基本信息
            Object statistics = java.util.Map.of(
                "message", "统计信息功能暂未实现",
                "timestamp", System.currentTimeMillis()
            );
            
            return ResponseEntity.ok(ResultDTO.success(statistics));

        } catch (Exception e) {
            log.error("流量平台-----> 查询同步统计信息失败，客户端IP: {}", clientIp, e);
            
            return ResponseEntity.internalServerError()
                    .body(ResultDTO.fail(500, "查询统计信息失败: " + e.getMessage()));
        }
    }

    /**
     * 健康检查接口
     *
     * @return 健康状态
     */
    @GetMapping(value = "/health", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(
        summary = "流量同步服务健康检查",
        description = "检查流量平台数据同步服务的健康状态"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "服务正常"),
        @ApiResponse(responseCode = "503", description = "服务不可用")
    })
    public ResponseEntity<ResultDTO<Object>> healthCheck() {
        
        try {
            // 这里可以添加健康检查逻辑，比如检查FTP连接、数据库连接等
            Object healthInfo = java.util.Map.of(
                "status", "UP",
                "service", "traffic-sync",
                "timestamp", System.currentTimeMillis(),
                "version", "1.0.0"
            );
            
            return ResponseEntity.ok(ResultDTO.success(healthInfo));

        } catch (Exception e) {
            log.error("流量平台-----> 健康检查失败", e);
            
            Object healthInfo = java.util.Map.of(
                "status", "DOWN",
                "service", "traffic-sync",
                "timestamp", System.currentTimeMillis(),
                "error", e.getMessage()
            );
            
            return ResponseEntity.status(503)
                    .body(ResultDTO.fail(503, "服务不可用"));
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
