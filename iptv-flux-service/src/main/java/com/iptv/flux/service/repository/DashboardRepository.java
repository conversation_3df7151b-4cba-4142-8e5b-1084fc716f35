package com.iptv.flux.service.repository;

import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.Result;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static org.jooq.impl.DSL.field;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.repository
 * @className: DashboardRepository
 * @author: chiron
 * @description: 仪表盘数据访问层
 * @date: 2025/1/21 17:00
 * @version: 1.0
 */
@Repository
@Slf4j
@RequiredArgsConstructor
public class DashboardRepository {

    private final DSLContext dsl;
    private final MetricsRegistryUtil metricsRegistry;

    // 表和字段定义
    private static final org.jooq.Table<org.jooq.Record> USER_GROUP_TABLE = DSL.table("user_group_relation");
    private static final org.jooq.Field<String> USER_ID = DSL.field("user_id", String.class);
    private static final org.jooq.Field<String> SOURCE = DSL.field("source", String.class);
    private static final org.jooq.Field<String> GROUP_IDS = DSL.field("group_ids", String.class);

    // 数据导入日志表
    private static final org.jooq.Table<org.jooq.Record> DATA_IMPORT_LOG_TABLE = DSL.table("data_import_log");
    private static final org.jooq.Field<String> TASK_ID = DSL.field("task_id", String.class);
    private static final org.jooq.Field<String> STATUS = DSL.field("status", String.class);
    private static final org.jooq.Field<Integer> TOTAL_RECORDS = DSL.field("total_records", Integer.class);
    private static final org.jooq.Field<Integer> PROCESSED_RECORDS = DSL.field("processed_records", Integer.class);
    private static final org.jooq.Field<Long> PROCESSING_TIME_MS = DSL.field("processing_time_ms", Long.class);
    private static final org.jooq.Field<Timestamp> LOG_CREATED_AT = DSL.field("created_at", Timestamp.class);

    /**
     * 获取仪表盘核心统计数据
     */
    @Timed(value = "repository.dashboard.statistics", percentiles = {0.5, 0.95, 0.99})
    public Map<String, Object> getDashboardStatistics() {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始查询仪表盘核心统计数据");

        try {
            Map<String, Object> statistics = new HashMap<>();

            // 查询总用户数
            Long totalUsers = dsl.selectCount()
                    .from(USER_GROUP_TABLE)
                    .fetchOne(0, Long.class);

            // 查询分组总数（通过解析GROUP_IDS字段统计唯一分组数）
            Long totalGroups = calculateTotalGroups();

            // 查询今日操作数（假设有updated_at字段）
            Timestamp todayStart = Timestamp.valueOf(LocalDateTime.now().withHour(0).withMinute(0).withSecond(0));
            Long todayOperations = dsl.selectCount()
                    .from(USER_GROUP_TABLE)
                    .where(field("updated_at", Timestamp.class).greaterOrEqual(todayStart))
                    .fetchOne(0, Long.class);

            // 计算昨日增长
            Timestamp yesterdayStart = Timestamp.valueOf(LocalDateTime.now().minusDays(2).withHour(0).withMinute(0).withSecond(0));
            Timestamp yesterdayEnd = Timestamp.valueOf(LocalDateTime.now().minusDays(1).withHour(23).withMinute(59).withSecond(59));
            Long yesterdayUsers = dsl.selectCount()
                    .from(USER_GROUP_TABLE)
                    .where(field("created_at", Timestamp.class).between(yesterdayStart, yesterdayEnd))
                    .fetchOne(0, Long.class);

            double yesterdayUserGrowth = yesterdayUsers != null && yesterdayUsers > 0 && totalUsers != null && totalUsers > 0 ?
                    ((double) yesterdayUsers / totalUsers) * 100 : 0.0;

            statistics.put("totalUsers", totalUsers != null ? totalUsers : 0L);
            statistics.put("totalGroups", totalGroups != null ? totalGroups : 0L);
            statistics.put("cachedUsers", totalUsers != null ? (long)(totalUsers * 0.85) : 0L); // 假设85%缓存命中
            statistics.put("todayOperations", todayOperations != null ? todayOperations : 0L);
            statistics.put("yesterdayUserGrowth", Math.round(yesterdayUserGrowth * 100.0) / 100.0);
            statistics.put("yesterdayGroupGrowth", 1); // 模拟数据
            statistics.put("todayOperationGrowth", 15.8); // 模拟数据

            log.debug("流量平台-----> 查询仪表盘核心统计数据完成，总用户数: {}, 总分组数: {}, 耗时 {}ms",
                    totalUsers, totalGroups, System.currentTimeMillis() - startTime);

            return statistics;
        } catch (Exception e) {
            log.error("流量平台-----> 查询仪表盘核心统计数据失败", e);
            metricsRegistry.incrementCounter(UserGroupConstants.REPOSITORY_DASHBOARD_STATISTICS_ERROR_METRIC);
            throw e;
        }
    }

    /**
     * 获取运营商用户分布
     */
    @Timed(value = "repository.dashboard.operators", percentiles = {0.5, 0.95, 0.99})
    public Map<String, Object> getOperatorDistribution() {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始查询运营商用户分布");

        try {
            Map<String, Object> distribution = new HashMap<>();

            // 查询各运营商用户数
            Result<? extends Record> result = dsl.select(SOURCE, DSL.count())
                    .from(USER_GROUP_TABLE)
                    .groupBy(SOURCE)
                    .fetch();

            long totalUsers = result.stream().mapToLong(r -> ((Number) r.get(1)).longValue()).sum();

            Map<String, Map<String, Object>> operators = new HashMap<>();

            for (Record record : result) {
                String source = (String) record.get(0);
                Number countNumber = (Number) record.get(1);
                long count = countNumber.longValue();

                Map<String, Object> operatorInfo = new HashMap<>();
                operatorInfo.put("users", count);
                operatorInfo.put("percentage", totalUsers > 0 ? 
                        Math.round(((double) count / totalUsers) * 1000.0) / 10.0 : 0.0);

                // 设置运营商信息
                switch (source) {
                    case "dx":
                        operatorInfo.put("name", "电信");
                        operatorInfo.put("icon", "📱");
                        operatorInfo.put("color", "#3498db");
                        break;
                    case "lt":
                        operatorInfo.put("name", "联通");
                        operatorInfo.put("icon", "📶");
                        operatorInfo.put("color", "#e74c3c");
                        break;
                    case "yd":
                        operatorInfo.put("name", "移动");
                        operatorInfo.put("icon", "📡");
                        operatorInfo.put("color", "#2ecc71");
                        break;
                    default:
                        operatorInfo.put("name", source);
                        operatorInfo.put("icon", "📞");
                        operatorInfo.put("color", "#95a5a6");
                        break;
                }

                operators.put(source, operatorInfo);
            }

            distribution.putAll(operators);

            log.debug("流量平台-----> 查询运营商用户分布完成，包含 {} 个运营商，总用户数: {}, 耗时 {}ms",
                    operators.size(), totalUsers, System.currentTimeMillis() - startTime);

            return distribution;
        } catch (Exception e) {
            log.error("流量平台-----> 查询运营商用户分布失败", e);
            metricsRegistry.incrementCounter(UserGroupConstants.REPOSITORY_DASHBOARD_OPERATORS_ERROR_METRIC);
            throw e;
        }
    }

    /**
     * 获取热门分组TOP N
     */
    @Timed(value = "repository.dashboard.topGroups", percentiles = {0.5, 0.95, 0.99})
    public List<Map<String, Object>> getTopGroups(int limit) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始查询热门分组TOP{}", limit);

        try {
            // 查询所有分组记录
            Result<? extends Record> records = dsl.select(GROUP_IDS, SOURCE)
                    .from(USER_GROUP_TABLE)
                    .where(GROUP_IDS.isNotNull().and(GROUP_IDS.ne("")))
                    .fetch();

            // 统计每个分组的用户数
            Map<String, Integer> groupCounts = new HashMap<>();
            Map<String, String> groupSources = new HashMap<>();

            for (Record record : records) {
                String groupIds = (String) record.get(0);
                String source = (String) record.get(1);

                if (groupIds != null && !groupIds.trim().isEmpty()) {
                    String[] groups = groupIds.split(",");
                    for (String groupId : groups) {
                        groupId = groupId.trim();
                        if (!groupId.isEmpty()) {
                            groupCounts.put(groupId, groupCounts.getOrDefault(groupId, 0) + 1);
                            groupSources.put(groupId, source);
                        }
                    }
                }
            }

            // 排序并取TOP N
            List<Map<String, Object>> topGroups = groupCounts.entrySet().stream()
                    .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                    .limit(limit)
                    .map(entry -> {
                        Map<String, Object> group = new HashMap<>();
                        group.put("groupId", entry.getKey());
                        group.put("name", generateGroupName(entry.getKey()));
                        group.put("count", entry.getValue());

                        // 计算百分比
                        long totalUsers = records.size();
                        double percentage = totalUsers > 0 ? ((double) entry.getValue() / totalUsers) * 100 : 0.0;
                        group.put("percentage", Math.round(percentage * 10.0) / 10.0);

                        return group;
                    })
                    .collect(Collectors.toList());

            log.debug("流量平台-----> 查询热门分组TOP{}完成，找到 {} 个分组，耗时 {}ms",
                    limit, topGroups.size(), System.currentTimeMillis() - startTime);

            return topGroups;
        } catch (Exception e) {
            log.error("流量平台-----> 查询热门分组TOP{}失败", limit, e);
            metricsRegistry.incrementCounter("repository.dashboard.topGroups.error");
            throw e;
        }
    }

    /**
     * 获取用户增长趋势
     */
    @Timed(value = "repository.dashboard.trends", percentiles = {0.5, 0.95, 0.99})
    public Map<String, Object> getUserTrends(int days) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始查询{}日用户增长趋势", days);

        try {
            Map<String, Object> trends = new HashMap<>();

            // 生成日期列表
            List<String> dates = new ArrayList<>();
            for (int i = days - 1; i >= 0; i--) {
                LocalDateTime date = LocalDateTime.now().minusDays(i);
                dates.add(date.format(DateTimeFormatter.ofPattern("M/d")));
            }

            // 生成模拟趋势数据（实际应该从数据库查询）
            Map<String, Map<String, Object>> series = new HashMap<>();

            // 电信趋势
            Map<String, Object> dxSeries = new HashMap<>();
            dxSeries.put("name", "电信");
            dxSeries.put("data", generateTrendData(days, 52.0, 56.8));
            dxSeries.put("color", "#3498db");
            series.put("dx", dxSeries);

            // 联通趋势
            Map<String, Object> ltSeries = new HashMap<>();
            ltSeries.put("name", "联通");
            ltSeries.put("data", generateTrendData(days, 32.0, 34.6));
            ltSeries.put("color", "#e74c3c");
            series.put("lt", ltSeries);

            // 移动趋势
            Map<String, Object> ydSeries = new HashMap<>();
            ydSeries.put("name", "移动");
            ydSeries.put("data", generateTrendData(days, 32.0, 34.1));
            ydSeries.put("color", "#2ecc71");
            series.put("yd", ydSeries);

            trends.put("dates", dates);
            trends.put("series", series);

            log.debug("流量平台-----> 查询{}日用户增长趋势完成，包含 {} 个运营商数据，耗时 {}ms",
                    days, series.size(), System.currentTimeMillis() - startTime);

            return trends;
        } catch (Exception e) {
            log.error("流量平台-----> 查询{}日用户增长趋势失败", days, e);
            metricsRegistry.incrementCounter("repository.dashboard.trends.error");
            throw e;
        }
    }

    /**
     * 获取系统状态
     */
    @Timed(value = "repository.dashboard.systemStatus", percentiles = {0.5, 0.95, 0.99})
    public Map<String, Object> getSystemStatus() {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始查询系统状态");

        try {
            Map<String, Object> systemStatus = new HashMap<>();

            // 调度器状态
            systemStatus.put("schedulerStatus", "运行中");
            systemStatus.put("nextUpdateTime", "15分钟后");

            // 内存使用情况（模拟数据）
            systemStatus.put("totalMemory", "16GB");
            systemStatus.put("usedMemory", "8.5GB");
            systemStatus.put("memoryUsage", 53.1);

            // 连接和性能指标（模拟数据）
            systemStatus.put("activeConnections", 1247);
            systemStatus.put("qps", 156);

            log.debug("流量平台-----> 查询系统状态完成，耗时 {}ms",
                    System.currentTimeMillis() - startTime);

            return systemStatus;
        } catch (Exception e) {
            log.error("流量平台-----> 查询系统状态失败", e);
            metricsRegistry.incrementCounter("repository.dashboard.systemStatus.error");
            throw e;
        }
    }

    /**
     * 获取最近活动记录
     */
    @Timed(value = "repository.dashboard.activities", percentiles = {0.5, 0.95, 0.99})
    public List<Map<String, Object>> getRecentActivities(int limit) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始查询最近{}条活动记录", limit);

        try {
            List<Map<String, Object>> activities = new ArrayList<>();

            // 查询最近更新的记录
            Result<Record> records = dsl.select()
                    .from(USER_GROUP_TABLE)
                    .orderBy(field("updated_at").desc())
                    .limit(limit)
                    .fetch();

            for (Record record : records) {
                Map<String, Object> activity = new HashMap<>();

                String userId = record.get(USER_ID);
                String source = record.get(SOURCE);
                String groupIds = record.get(GROUP_IDS);

                // 正确处理 Timestamp 到 LocalDateTime 的转换
                Timestamp timestamp = record.get(field("updated_at", Timestamp.class));
                LocalDateTime updatedAt = timestamp != null ? timestamp.toLocalDateTime() : null;

                activity.put("id", "act_" + System.currentTimeMillis() + "_" + userId.hashCode());
                activity.put("type", "用户分组更新");
                activity.put("description", String.format("用户 %s 更新分组信息",
                        maskUserId(userId)));
                activity.put("source", source);
                activity.put("timestamp", updatedAt != null ?
                        updatedAt.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) :
                        LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                activity.put("status", "成功");

                activities.add(activity);
            }

            log.debug("流量平台-----> 查询最近{}条活动记录完成，实际返回 {} 条，耗时 {}ms",
                    limit, activities.size(), System.currentTimeMillis() - startTime);

            return activities;
        } catch (Exception e) {
            log.error("流量平台-----> 查询最近活动记录失败", e);
            metricsRegistry.incrementCounter("repository.dashboard.activities.error");
            throw e;
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 计算总分组数
     */
    private Long calculateTotalGroups() {
        try {
            List<String> groupIdsList = dsl.select(GROUP_IDS)
                    .from(USER_GROUP_TABLE)
                    .where(GROUP_IDS.isNotNull().and(GROUP_IDS.ne("")))
                    .fetch(GROUP_IDS);

            Set<String> uniqueGroups = new HashSet<>();
            for (String groupIds : groupIdsList) {
                if (groupIds != null && !groupIds.trim().isEmpty()) {
                    String[] groups = groupIds.split(",");
                    for (String groupId : groups) {
                        groupId = groupId.trim();
                        if (!groupId.isEmpty()) {
                            uniqueGroups.add(groupId);
                        }
                    }
                }
            }

            return (long) uniqueGroups.size();
        } catch (Exception e) {
            log.warn("流量平台-----> 计算总分组数失败", e);
            return 0L;
        }
    }

    /**
     * 生成分组名称
     */
    private String generateGroupName(String groupId) {
        // 根据分组ID生成友好的名称
        if (groupId.contains("vip")) {
            return "VIP高清组";
        } else if (groupId.contains("hd")) {
            return "高清组";
        } else if (groupId.contains("sd")) {
            return "标清组";
        } else if (groupId.contains("4k")) {
            return "4K超清组";
        } else if (groupId.contains("test")) {
            return "测试组";
        } else {
            return "分组_" + groupId;
        }
    }

    /**
     * 生成趋势数据
     */
    private List<Double> generateTrendData(int days, double startValue, double endValue) {
        List<Double> data = new ArrayList<>();
        double step = (endValue - startValue) / (days - 1);

        for (int i = 0; i < days; i++) {
            double value = startValue + (step * i);
            // 添加一些随机波动
            double randomFactor = 0.95 + (Math.random() * 0.1); // 0.95 到 1.05 的随机因子
            value *= randomFactor;
            data.add(Math.round(value * 10.0) / 10.0);
        }

        return data;
    }

    /**
     * 脱敏用户ID
     */
    private String maskUserId(String userId) {
        if (userId == null || userId.length() <= 8) {
            return userId;
        }
        return userId.substring(0, 8) + "***";
    }

    /**
     * 获取全部导入统计数据（真实数据源）
     */
    @Timed(value = "repository.dashboard.allImportStats", percentiles = {0.5, 0.95, 0.99})
    public Map<String, Object> getAllImportStatistics() {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始查询全部导入统计数据");

        try {
            Map<String, Object> stats = new HashMap<>();

            // 查询总导入次数
            Integer totalImports = dsl.selectCount()
                    .from(DATA_IMPORT_LOG_TABLE)
                    .fetchOne(0, Integer.class);

            // 查询成功导入次数
            Integer successfulImports = dsl.selectCount()
                    .from(DATA_IMPORT_LOG_TABLE)
                    .where(STATUS.eq("COMPLETED"))
                    .fetchOne(0, Integer.class);

            // 查询失败导入次数
            Integer failedImports = dsl.selectCount()
                    .from(DATA_IMPORT_LOG_TABLE)
                    .where(STATUS.eq("FAILED"))
                    .fetchOne(0, Integer.class);

            // 计算成功率
            double successRate = totalImports > 0 ?
                    ((double) successfulImports / totalImports) * 100 : 100.0;

            stats.put("totalImports", totalImports != null ? totalImports : 0);
            stats.put("successfulImports", successfulImports != null ? successfulImports : 0);
            stats.put("failedImports", failedImports != null ? failedImports : 0);
            stats.put("successRate", Math.round(successRate * 10.0) / 10.0);

            log.debug("流量平台-----> 查询全部导入统计数据完成，总导入: {}, 成功: {}, 失败: {}, 成功率: {}%, 耗时 {}ms",
                    totalImports, successfulImports, failedImports, successRate, System.currentTimeMillis() - startTime);

            return stats;
        } catch (Exception e) {
            log.error("流量平台-----> 查询全部导入统计数据失败", e);
            metricsRegistry.incrementCounter("repository.dashboard.allImportStats.error");

            // 返回默认值，避免影响整体功能
            Map<String, Object> defaultStats = new HashMap<>();
            defaultStats.put("totalImports", 0);
            defaultStats.put("successfulImports", 0);
            defaultStats.put("failedImports", 0);
            defaultStats.put("successRate", 100.0);
            return defaultStats;
        }
    }

    /**
     * 获取今日导入统计数据
     */
    @Timed(value = "repository.dashboard.todayImportStats", percentiles = {0.5, 0.95, 0.99})
    public Map<String, Object> getTodayImportStatistics() {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始查询今日导入统计数据");

        try {
            Map<String, Object> stats = new HashMap<>();

            // 今日开始时间
            Timestamp todayStart = Timestamp.valueOf(LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0));

            // 查询今日导入总数
            Integer todayImports = dsl.selectCount()
                    .from(DATA_IMPORT_LOG_TABLE)
                    .where(LOG_CREATED_AT.greaterOrEqual(todayStart))
                    .fetchOne(0, Integer.class);

            // 查询今日成功导入的记录总数
            Integer todayRecords = dsl.select(DSL.sum(TOTAL_RECORDS))
                    .from(DATA_IMPORT_LOG_TABLE)
                    .where(LOG_CREATED_AT.greaterOrEqual(todayStart))
                    .and(STATUS.eq("COMPLETED"))
                    .fetchOne(0, Integer.class);

            // 查询今日平均处理时间
            Double avgProcessingTimeMs = dsl.select(DSL.avg(PROCESSING_TIME_MS))
                    .from(DATA_IMPORT_LOG_TABLE)
                    .where(LOG_CREATED_AT.greaterOrEqual(todayStart))
                    .and(STATUS.eq("COMPLETED"))
                    .and(PROCESSING_TIME_MS.greaterThan(0L))
                    .fetchOne(0, Double.class);

            // 转换为秒并格式化
            String avgProcessingTime = "0秒";
            if (avgProcessingTimeMs != null && avgProcessingTimeMs > 0) {
                double avgSeconds = avgProcessingTimeMs / 1000.0;
                if (avgSeconds < 60) {
                    avgProcessingTime = String.format("%.1f秒", avgSeconds);
                } else {
                    int minutes = (int) (avgSeconds / 60);
                    double remainingSeconds = avgSeconds % 60;
                    avgProcessingTime = String.format("%d分%.1f秒", minutes, remainingSeconds);
                }
            }

            stats.put("todayImports", todayImports != null ? todayImports : 0);
            stats.put("todayRecords", todayRecords != null ? todayRecords : 0);
            stats.put("avgProcessingTime", avgProcessingTime);

            log.debug("流量平台-----> 查询今日导入统计数据完成，今日导入: {}, 今日记录: {}, 平均处理时间: {}, 耗时 {}ms",
                    todayImports, todayRecords, avgProcessingTime, System.currentTimeMillis() - startTime);

            return stats;
        } catch (Exception e) {
            log.error("流量平台-----> 查询今日导入统计数据失败", e);
            metricsRegistry.incrementCounter("repository.dashboard.todayImportStats.error");

            // 返回默认值，避免影响整体功能
            Map<String, Object> defaultStats = new HashMap<>();
            defaultStats.put("todayImports", 0);
            defaultStats.put("todayRecords", 0);
            defaultStats.put("avgProcessingTime", "0秒");
            return defaultStats;
        }
    }
}