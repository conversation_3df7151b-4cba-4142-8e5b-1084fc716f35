package com.iptv.flux.service.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

import java.time.Duration;

/**
 * 集中的Web配置类，包含服务器、客户端和HTTP消息转换器的配置
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class WebConfig {

    @Value("${server.tomcat.max-threads:1000}")
    private int maxThreads;

    @Value("${server.tomcat.max-connections:20000}")
    private int maxConnections;

    @Value("${server.tomcat.accept-count:5000}")
    private int acceptCount;

    @Value("${server.tomcat.connection-timeout:60000}")
    private int connectionTimeout;

    @Value("${server.tomcat.max-http-form-post-size:272629760}")
    private long maxPostSize;

    @Value("${user-group.data-import.http.connect-timeout:60000}")
    private int httpConnectTimeout;

    @Value("${user-group.data-import.http.read-timeout:300000}")
    private int httpReadTimeout;

    @Bean
    public WebClient webClient() {
        log.info("配置WebClient的最佳连接池设置");

        ConnectionProvider provider = ConnectionProvider.builder("http-pool")
                .maxConnections(5000)
                .maxIdleTime(Duration.ofSeconds(30))
                .maxLifeTime(Duration.ofMinutes(5))
                .pendingAcquireTimeout(Duration.ofSeconds(5))
                .build();

        HttpClient httpClient = HttpClient.create(provider)
                .responseTimeout(Duration.ofMillis(httpReadTimeout));

        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .filter(logRequest())
                .build();
    }


    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> tomcatCustomizer() {
        return factory -> {
            log.info("配置Tomcat以支持高并发和大文件上传");

            factory.addConnectorCustomizers(connector -> {
                connector.setProperty("maxThreads", String.valueOf(maxThreads));
                connector.setProperty("minSpareThreads", "100");
                connector.setProperty("maxConnections", String.valueOf(maxConnections));
                connector.setProperty("acceptCount", String.valueOf(acceptCount));
                connector.setProperty("connectionTimeout", String.valueOf(httpConnectTimeout));

                // Enable keep-alive for long-lived connections
                connector.setProperty("keepAliveTimeout", "20000");
                connector.setProperty("maxKeepAliveRequests", "1000");
                connector.setProperty("useOpenSSL", "true");

                // 配置文件上传相关参数
                connector.setProperty("maxPostSize", String.valueOf(maxPostSize));
                connector.setProperty("maxSavePostSize", String.valueOf(maxPostSize));

                log.info("Tomcat 连接器配置为 maxThreads={}，maxConnections={}，acceptCount={}，maxPostSize={}MB",
                        maxThreads, maxConnections, acceptCount, maxPostSize / (1024 * 1024));
            });
        };
    }

    private ExchangeFilterFunction logRequest() {
        return ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {
            if (log.isDebugEnabled()) {
                log.debug("Request: {} {}", clientRequest.method(), clientRequest.url());
            }
            return Mono.just(clientRequest);
        });
    }
}
