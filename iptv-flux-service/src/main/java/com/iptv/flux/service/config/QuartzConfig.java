package com.iptv.flux.service.config;

import com.iptv.flux.service.schedule.CacheWarmupJob;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class QuartzConfig {
    // 默认每小时执行一次
    @Value("${cache.warmup.cron:0 0 */1 * * ?}")
    private String warmupCronExpression;

    @Value("${cache.warmup.enabled:true}")
    private boolean warmupEnabled;

    @Bean
    public JobDetail cacheWarmupJobDetail() {
        return JobBuilder.newJob(CacheWarmupJob.class)
                .withIdentity("cacheWarmupJob")
                .storeDurably()
                .build();
    }

    @Bean
    public Trigger cacheWarmupTrigger() {
        if (!warmupEnabled) {
            // 如果未启用预热，返回一个不会触发的触发器
            return TriggerBuilder.newTrigger()
                    .withIdentity("cacheWarmupTrigger")
                    .forJob(cacheWarmupJobDetail())
                    .startNow()
                    .withSchedule(SimpleScheduleBuilder.simpleSchedule()
                             // 1年执行一次
                            .withIntervalInHours(8760)
                            .repeatForever())
                    .build();
        }

        return TriggerBuilder.newTrigger()
                .withIdentity("cacheWarmupTrigger")
                .forJob(cacheWarmupJobDetail())
                .withSchedule(CronScheduleBuilder.cronSchedule(warmupCronExpression))
                .build();
    }

    // 系统启动后立即执行一次的触发器
    @Bean
    public Trigger initialCacheWarmupTrigger() {
        if (!warmupEnabled) {
            return null;
        }

        return TriggerBuilder.newTrigger()
                .withIdentity("initialCacheWarmupTrigger")
                .forJob(cacheWarmupJobDetail())
                .startNow()
                .withSchedule(SimpleScheduleBuilder.simpleSchedule()
                         // 只执行一次
                        .withRepeatCount(0))
                .build();
    }
}