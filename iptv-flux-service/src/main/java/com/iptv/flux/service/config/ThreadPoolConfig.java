package com.iptv.flux.service.config;

import com.iptv.flux.common.constants.UserGroupConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.config
 * @className: ThreadPoolConfig
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 12:57
 * @version: 1.0
 */
@Configuration
@Slf4j
public class ThreadPoolConfig {

    @Value("${user-group.async.core-pool-size:50}")
    private int corePoolSize;

    @Value("${user-group.async.max-pool-size:200}")
    private int maxPoolSize;

    @Value("${user-group.async.queue-capacity:1000}")
    private int queueCapacity;

    @Value("${user-group.async.keep-alive:60}")
    private int keepAliveSeconds;

    @Bean(name = UserGroupConstants.ASYNC_EXECUTOR_BEAN)
    public ExecutorService cacheRefreshExecutor() {
        log.info("配置缓存刷新执行器，核心={}, 最大={}, 队列容量={}, 保持活跃={}秒",
                corePoolSize, maxPoolSize, queueCapacity, keepAliveSeconds);

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix("AsyncCache-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();

        return executor.getThreadPoolExecutor();
    }

    @Bean(name = "commonTaskExecutor")
    public ThreadPoolTaskExecutor commonTaskExecutor() {
        log.info("Configuring common task executor");

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(200);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("CommonTask-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();

        return executor;
    }
}