package com.iptv.flux.service.config;

import com.iptv.flux.common.config.CommonSwaggerConfig;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.config
 * @className: SwaggerConfig
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 13:53
 * @version: 1.0
 */
@Configuration
public class SwaggerConfig {

    @Bean
    public GroupedOpenApi userGroupApi() {
        return CommonSwaggerConfig.buildGroupedApi(
                "user-group",
                "com.iptv.flux.service.controller",
                "/api/usergroup/**"
        );
    }

    @Bean
    public GroupedOpenApi cacheApi() {
        return CommonSwaggerConfig.buildGroupedApi(
                "cache-management",
                "com.iptv.flux.service.controller",
                "/api/cache/**"
        );
    }

    @Bean
    public GroupedOpenApi monitoringApi() {
        return CommonSwaggerConfig.buildGroupedApi(
                "monitoring",
                "com.iptv.flux.service.controller",
                "/api/monitor/**"
        );
    }

    @Bean
    public GroupedOpenApi dashboardApi() {
        return CommonSwaggerConfig.buildGroupedApi(
                "dashboard",
                "com.iptv.flux.service.controller",
                "/api/dashboard/**"
        );
    }
}
