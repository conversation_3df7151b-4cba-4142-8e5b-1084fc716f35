# 测试环境配置
  
  # 数据库配置（使用H2内存数据库进行测试）
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    database-platform: org.hibernate.dialect.H2Dialect
    
  # Redis配置（使用嵌入式Redis进行测试）
  redis:
    host: localhost
    port: 6379
    timeout: 2000ms
    cluster:
      nodes: localhost:6379
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

# 流量平台FTP配置
traffic:
  sync:
    ftp:
      host: localhost
      port: 21
      username: test
      password: test
      connection-timeout: 5000
      data-timeout: 10000
      control-timeout: 5000
      retry-attempts: 1
      retry-interval: 1000
      passive-mode: true
      binary-mode: true
      pool:
        max-total: 2
        max-idle: 1
        min-idle: 0
        max-wait-millis: 5000
        min-evictable-idle-time-millis: 60000
        time-between-eviction-runs-millis: 30000
        test-on-borrow: true
        test-on-return: false
        test-while-idle: true

# 异步处理配置
user-group:
  data-import:
    async:
      core-pool-size: 2
      max-pool-size: 4
      queue-capacity: 100
      keep-alive: 60

# 日志配置
logging:
  level:
    com.iptv.flux: DEBUG
    org.springframework.web: INFO
    org.hibernate: WARN
    com.alibaba.nacos: WARN
    
# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# Nacos配置（测试环境禁用）
nacos:
  config:
    enabled: false
  discovery:
    enabled: false
