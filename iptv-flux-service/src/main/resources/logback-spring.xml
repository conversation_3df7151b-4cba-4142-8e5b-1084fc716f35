<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProperty scope="context" name="springAppName" source="spring.application.name"/>
    <springProperty scope="context" name="logPath" source="log.file.path"/>
    <springProperty scope="context" name="logName" source="log.file.name"/>
    <springProperty scope="context" name="logSuffix" source="log.file.suffix"/>
    <springProperty scope="context" name="activeProfile" source="spring.profiles.active"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- Logstash TCP Socket Appender - 正确配置 -->
    <appender name="LOGSTASH_TCP" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
        <destination>172.25.199.153:9500</destination>
        <reconnectionDelay>1 second</reconnectionDelay>
        <connectionTimeout>5 seconds</connectionTimeout>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <customFields>{"application":"${springAppName}","environment":"${activeProfile:-unknown}"}</customFields>
            <includeMdc>true</includeMdc>
            <fieldNames>
                <timestamp>timestamp</timestamp>
                <message>log_message</message>
                <logger>logger</logger>
                <thread>thread</thread>
                <level>level</level>
            </fieldNames>
        </encoder>
    </appender>

    <!-- 异步发送日志到Logstash -->
    <appender name="ASYNC_LOGSTASH" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="LOGSTASH_TCP" />
        <queueSize>512</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>false</includeCallerData>
        <!-- 避免阻塞主线程 -->
        <neverBlock>true</neverBlock>
    </appender>

    <!-- 开发环境和测试环境配置 -->
    <springProfile name="dev,test">
        <root level="INFO">
            <appender-ref ref="CONSOLE" />
            <!-- 开发环境可选择是否发送到Logstash -->
            <!-- <appender-ref ref="ASYNC_LOGSTASH" /> -->
        </root>
    </springProfile>

    <!-- 生产环境和预生产环境配置 -->
    <springProfile name="prod,pre">
        <!-- 文件输出 -->
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${logPath}${logName}.${logSuffix}</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>${logPath}${logName}-%d{yyyy-MM-dd}.%i.${logSuffix}.gz</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>10GB</totalSizeCap>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>

        <root level="INFO">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="FILE" />
<!--            <appender-ref ref="ASYNC_LOGSTASH" />-->
        </root>
    </springProfile>

    <!-- 日志级别设置 -->
    <!-- 项目核心日志保持INFO级别 -->
    <logger name="com.iptv.flux" level="INFO" />

    <!-- 第三方库日志调整为WARN级别，减少控制台输出 -->
    <logger name="org.springframework" level="WARN" />
    <logger name="com.alibaba.nacos" level="WARN" />
    <logger name="org.springframework.cloud.gateway" level="WARN" />
    <logger name="org.springframework.cloud.nacos" level="WARN" />
    <logger name="org.springframework.boot" level="WARN" />
    <logger name="org.springframework.context" level="WARN" />
    <logger name="org.springframework.beans" level="WARN" />
    <logger name="org.springframework.data.redis" level="WARN" />
    <logger name="org.redisson" level="WARN" />
    <logger name="com.zaxxer.hikari" level="WARN" />
    <logger name="org.jooq" level="WARN" />
    <logger name="reactor.netty" level="WARN" />

    <!-- 特殊需要的DEBUG日志 -->
    <logger name="com.iptv.flux.service.controller.DataImportController" level="INFO" />
    <logger name="com.iptv.flux.service.service.DataImportService" level="INFO" />
    <logger name="com.iptv.flux.service.service.DataImportProgressService" level="INFO" />
</configuration>