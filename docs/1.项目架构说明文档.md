# IPTV 流量管理平台 - 项目架构说明文档

## 📋 项目概述

**项目名称**: IPTV Flux Manager  
**版本**: 1.0.0  
**描述**: 高性能用户分组服务系统  
**技术栈**: Spring Boot 3.x + Spring Cloud + Redis + MySQL + jOOQ

---

## 🏗️ 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    IPTV 流量管理平台                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │   Gateway   │  │   Service   │  │   Common    │          │
│  │   (7001)    │  │   (7003)    │  │   (共享)    │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │    Nacos    │  │    Redis    │  │    MySQL    │          │
│  │   (注册)    │  │   (缓存)    │  │   (存储)    │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

### 模块结构

#### 1. iptv-flux-gateway (网关模块)
- **端口**: 7001
- **职责**: API网关、路由转发、Swagger聚合
- **主要功能**:
  - 统一入口管理
  - 服务路由和负载均衡
  - API文档聚合展示
  - 跨域配置管理

#### 2. iptv-flux-service (核心服务模块)
- **端口**: 7003
- **职责**: 业务逻辑处理、数据管理
- **主要功能**:
  - 用户分组管理
  - 数据导入处理
  - 仪表盘统计
  - 系统监控
  - 缓存管理

#### 3. iptv-flux-common (公共模块)
- **职责**: 共享组件、工具类、配置
- **主要内容**:
  - 通用DTO和常量
  - 工具类和配置
  - Swagger配置
  - 异常处理

---

## 🔧 技术架构

### 核心技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| Spring Boot | 3.x | 应用框架 |
| Spring Cloud | 2023.x | 微服务框架 |
| Spring Cloud Gateway | 4.x | API网关 |
| Nacos | 2.x | 服务注册与配置中心 |
| Redis | 7.x | 缓存和会话存储 |
| MySQL | 8.x | 主数据库 |
| jOOQ | 3.x | 数据库访问层 |
| Redisson | 3.x | Redis客户端 |
| Caffeine | 3.x | 本地缓存 |
| Micrometer | 1.x | 监控指标 |
| Resilience4j | 2.x | 熔断限流 |

### 架构特性

#### 1. 多级缓存架构
```
请求 → 本地缓存(Caffeine) → Redis缓存 → 数据库(MySQL)
```
- **L1缓存**: Caffeine本地缓存，毫秒级响应
- **L2缓存**: Redis分布式缓存，支持集群
- **布隆过滤器**: 防止缓存穿透

#### 2. 高可用设计
- **熔断保护**: Resilience4j熔断器
- **限流控制**: 令牌桶算法
- **超时控制**: 请求超时保护
- **异步处理**: 大数据量异步导入

#### 3. 监控体系
- **性能监控**: Micrometer指标收集
- **健康检查**: 多维度健康状态监控
- **业务监控**: 自定义业务指标
- **日志管理**: 结构化日志输出

---

## 📊 数据架构

### 数据库设计

#### 核心表结构
```sql
-- 用户分组关系表
user_group_relation
├── id (主键)
├── user_id (用户ID)
├── source (来源: dx/lt/yd)
├── group_ids (分组ID集合)
└── timestamps (时间戳)

-- 分组信息表
group_info
├── group_id (分组ID)
├── business_id (业务ID)
├── group_name (分组名称)
├── description (描述)
└── timestamps (时间戳)

-- 黑名单表
user_blacklist
├── id (主键)
├── user_id (用户ID)
├── source (来源)
├── remark (备注)
└── timestamps (时间戳)

-- 白名单表
user_whitelist
├── id (主键)
├── user_id (用户ID)
├── source (来源)
├── remark (备注)
└── timestamps (时间戳)

-- 数据导入日志表
data_import_log
├── id (主键)
├── task_id (任务ID)
├── file_name (文件名)
├── status (状态)
├── statistics (统计信息)
└── timestamps (时间戳)
```

### 缓存策略

#### Redis缓存结构
```
ug:user:{source}:{userId} → Set<String> (分组ID集合)
ug:group:{groupId} → GroupInfo (分组信息)
ug:progress:{taskId} → ProgressInfo (进度信息)
ug:bloom_filter → BloomFilter (布隆过滤器)
```

#### 缓存更新策略
- **写入策略**: Write-Through (同步写入)
- **失效策略**: TTL + 手动失效
- **预热策略**: 定时任务预热热点数据
- **一致性**: 最终一致性模型

---

## 🚀 部署架构

### 容器化部署

#### Docker配置
```dockerfile
# 基础镜像: Eclipse Temurin 17 JRE
FROM eclipse-temurin:17-jre-alpine

# JVM优化参数
ENV JAVA_OPTS="-XX:+UseG1GC -XX:MaxGCPauseMillis=200 
               -XX:+UseStringDeduplication -XX:+DisableExplicitGC 
               -XX:MaxRAMPercentage=75.0"

# 端口暴露
EXPOSE 7001 7003
```

#### 环境配置
- **开发环境**: 单机部署，本地缓存
- **测试环境**: 容器化部署，Redis集群
- **生产环境**: K8s部署，高可用配置

### 服务发现与配置

#### Nacos配置
```yaml
# 服务注册
nacos:
  server-addr: **************:8848
  namespace: bokong_dev
  group: iptv
  
# 配置管理
shared-configs:
  - iptv-redis-dev.yaml    # Redis配置
  - iptv-mysql-dev.yaml    # MySQL配置
```

---

## 📈 性能设计

### 性能指标

| 指标 | 目标值 | 监控方式 |
|------|--------|----------|
| 响应时间 | P99 < 200ms | Micrometer |
| 吞吐量 | > 10000 QPS | 压力测试 |
| 缓存命中率 | > 95% | Redis监控 |
| 可用性 | > 99.9% | 健康检查 |

### 优化策略

#### 1. 查询优化
- 数据库索引优化
- jOOQ查询优化
- 分页查询限制

#### 2. 缓存优化
- 多级缓存架构
- 缓存预热机制
- 布隆过滤器防穿透

#### 3. 并发优化
- 异步处理机制
- 线程池配置优化
- 连接池参数调优

---

## 🔒 安全架构

### 安全措施

#### 1. 接口安全
- 参数校验 (Jakarta Validation)
- 限流保护 (Rate Limiting)
- 熔断保护 (Circuit Breaker)

#### 2. 数据安全
- SQL注入防护 (jOOQ参数化查询)
- 敏感数据脱敏
- 访问日志记录

#### 3. 系统安全
- CORS跨域配置
- 健康检查端点保护
- 监控指标访问控制

---

## 📝 扩展性设计

### 水平扩展
- 无状态服务设计
- 负载均衡支持
- 数据库读写分离

### 功能扩展
- 插件化架构预留
- 配置化业务规则
- 多租户支持预留

---

## 🔍 核心业务流程

### 用户分组查询流程
```
用户请求 → 参数校验 → 布隆过滤器检查 → 本地缓存查询
    ↓
Redis缓存查询 → 数据库查询 → 缓存更新 → 返回结果
```

### 数据导入流程
```
文件上传 → 格式验证 → 异步任务创建 → Excel解析
    ↓
批量处理 → 数据验证 → 数据库写入 → 进度更新 → 完成通知
```

### 缓存预热流程
```
定时触发 → 热点数据识别 → 批量查询 → 缓存写入
    ↓
布隆过滤器更新 → 统计信息记录 → 预热完成
```

---

## 📊 关键技术决策

### 1. 为什么选择 jOOQ 而不是 MyBatis？
- **类型安全**: 编译时SQL检查
- **性能优势**: 更好的查询优化
- **代码生成**: 自动生成类型安全的代码
- **复杂查询**: 更好的复杂查询支持

### 2. 为什么使用多级缓存架构？
- **性能分层**: 本地缓存毫秒级，Redis缓存10ms级
- **容量平衡**: 本地缓存容量有限，Redis缓存容量大
- **可用性**: 本地缓存作为Redis的备份
- **成本优化**: 减少网络IO和Redis压力

### 3. 为什么选择 Resilience4j？
- **轻量级**: 相比Hystrix更轻量
- **函数式**: 支持函数式编程
- **Spring Boot集成**: 原生支持Spring Boot
- **监控友好**: 丰富的监控指标

---

*文档版本: v1.0*
*最后更新: 2025-01-04*
*维护者: Claude 4.0 sonnet* 🐱
