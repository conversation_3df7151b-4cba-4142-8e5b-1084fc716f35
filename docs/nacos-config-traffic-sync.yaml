# 流量平台数据同步FTP配置
# 配置文件名: iptv-flux-service-dev.yaml (开发环境)
# 配置文件名: iptv-flux-service-prod.yaml (生产环境)
# Nacos配置中心配置项

# FTP连接配置
traffic:
  sync:
    ftp:
      # FTP服务器配置
      host: ***********
      port: 2121
      username: vstorero
      password: iptv_2024at2other
      
      # 超时配置（毫秒）
      connection-timeout: 30000
      data-timeout: 60000
      control-timeout: 30000
      
      # 重试配置
      retry-attempts: 3
      retry-interval: 1000
      
      # 传输模式配置
      passive-mode: true
      binary-mode: true
      
      # 连接池配置
      pool:
        max-total: 10
        max-idle: 5
        min-idle: 1
        max-wait-millis: 30000
        min-evictable-idle-time-millis: 300000  # 5分钟
        time-between-eviction-runs-millis: 60000  # 1分钟
        test-on-borrow: true
        test-on-return: false
        test-while-idle: true

# 数据导入异步处理配置
user-group:
  data-import:
    async:
      core-pool-size: 10
      max-pool-size: 20
      queue-capacity: 500
      keep-alive: 300

# 监控指标配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: iptv-flux-service
      environment: ${spring.profiles.active}

# 日志配置
logging:
  level:
    com.iptv.flux.service.service.TrafficSyncService: INFO
    com.iptv.flux.service.util.FTPFileProcessor: INFO
    com.iptv.flux.service.util.GroupsFileParser: INFO
    com.iptv.flux.service.util.UserGroupFileParser: INFO
    com.iptv.flux.service.repository.DataImportLogRepository: DEBUG

---
# 生产环境配置覆盖
spring:
  config:
    activate:
      on-profile: prod

traffic:
  sync:
    ftp:
      # 生产环境FTP服务器配置
      host: **************
      port: 21
      username: prod_user
      password: ${FTP_PASSWORD:prod_password}
      
      # 生产环境连接池配置
      pool:
        max-total: 20
        max-idle: 10
        min-idle: 2

# 生产环境日志级别
logging:
  level:
    com.iptv.flux.service.service.TrafficSyncService: WARN
    com.iptv.flux.service.util.FTPFileProcessor: WARN
    com.iptv.flux.service.util.GroupsFileParser: WARN
    com.iptv.flux.service.util.UserGroupFileParser: WARN
    com.iptv.flux.service.repository.DataImportLogRepository: WARN

---
# 测试环境配置覆盖
spring:
  config:
    activate:
      on-profile: test

traffic:
  sync:
    ftp:
      # 测试环境FTP服务器配置
      host: **************
      port: 21
      username: test_user
      password: test_password
      
      # 测试环境连接池配置
      pool:
        max-total: 5
        max-idle: 3
        min-idle: 1

# 测试环境日志级别
logging:
  level:
    com.iptv.flux.service.service.TrafficSyncService: DEBUG
    com.iptv.flux.service.util.FTPFileProcessor: DEBUG
    com.iptv.flux.service.util.GroupsFileParser: DEBUG
    com.iptv.flux.service.util.UserGroupFileParser: DEBUG
    com.iptv.flux.service.repository.DataImportLogRepository: DEBUG
