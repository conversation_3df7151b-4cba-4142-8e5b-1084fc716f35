# 🚀 IPTV 流量管理平台

[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.x-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![Spring Cloud](https://img.shields.io/badge/Spring%20Cloud-2023.x-blue.svg)](https://spring.io/projects/spring-cloud)
[![Java](https://img.shields.io/badge/Java-17-orange.svg)](https://openjdk.java.net/)
[![Redis](https://img.shields.io/badge/Redis-7.x-red.svg)](https://redis.io/)
[![MySQL](https://img.shields.io/badge/MySQL-8.x-blue.svg)](https://www.mysql.com/)

> 高性能、高可用的 IPTV 用户分组管理系统，支持千万级用户数据处理和毫秒级响应

## 📋 项目简介

IPTV 流量管理平台是一个专为 IPTV 业务设计的用户分组管理系统，提供高性能的用户分组查询、数据导入、统计分析等功能。系统采用微服务架构，支持多运营商数据管理，具备完善的监控和运维能力。

### 🎯 核心特性

- **🚄 高性能**: 多级缓存架构，P99响应时间 < 200ms
- **📊 大数据**: 支持千万级用户数据，异步批处理
- **🛡️ 高可用**: 熔断限流、健康检查、故障自愈
- **📈 可观测**: 完善的监控指标和性能统计
- **🔧 易运维**: 容器化部署，配置中心管理
- **📱 多端支持**: RESTful API，完整的 Swagger 文档

---

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    前端应用 / 第三方系统                      │
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTP/HTTPS
┌─────────────────────▼───────────────────────────────────────┐
│                 API Gateway (7001)                         │
│              路由转发 | 负载均衡 | API聚合                    │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                Core Service (7003)                         │
│     用户分组 | 数据导入 | 仪表盘 | 监控 | 缓存管理              │
└─────┬───────────────┬───────────────┬───────────────────────┘
      │               │               │
┌─────▼─────┐  ┌─────▼─────┐  ┌─────▼─────┐
│   Nacos   │  │   Redis   │  │   MySQL   │
│  注册中心  │  │ 分布式缓存 │  │  主数据库  │
└───────────┘  └───────────┘  └───────────┘
```

---

## 🚀 快速开始

### 环境要求

- **Java**: 17+
- **Maven**: 3.8+
- **Docker**: 20.10+ (可选)
- **MySQL**: 8.0+
- **Redis**: 7.0+
- **Nacos**: 2.2+

### 本地开发

#### 1. 克隆项目
```bash
git clone <repository-url>
cd iptv-flux-manager
```

#### 2. 数据库初始化
```bash
# 执行数据库脚本
mysql -u root -p < blacklist_whitelist_tables.sql
mysql -u root -p < data_import_log_table.sql
```

#### 3. 配置环境
```bash
# 复制配置文件
cp iptv-flux-service/src/main/resources/bootstrap-dev.yml.example \
   iptv-flux-service/src/main/resources/bootstrap-dev.yml

# 修改配置
vim iptv-flux-service/src/main/resources/bootstrap-dev.yml
```

#### 4. 启动服务
```bash
# 启动核心服务
cd iptv-flux-service
mvn spring-boot:run -Dspring.profiles.active=dev

# 启动网关服务 (可选)
cd iptv-flux-gateway
mvn spring-boot:run -Dspring.profiles.active=dev
```

#### 5. 验证服务
```bash
# 健康检查
curl http://localhost:7003/api/usergroup/health

# 查看 API 文档
open http://localhost:7003/swagger-ui/index.html
```

### Docker 部署

#### 1. 构建镜像
```bash
# 构建服务镜像
mvn clean package -DskipTests
docker build -t iptv-flux-service:latest iptv-flux-service/
docker build -t iptv-flux-gateway:latest iptv-flux-gateway/
```

#### 2. 启动容器
```bash
# 使用 docker-compose (推荐)
docker-compose up -d

# 或单独启动
docker run -d -p 7003:7003 \
  -e SPRING_PROFILES_ACTIVE=dev \
  iptv-flux-service:latest
```

---

## 📡 API 接口

### 核心接口概览

| 模块 | 接口路径 | 功能描述 |
|------|----------|----------|
| 用户分组 | `/api/usergroup` | 用户分组管理 |
| 数据导入 | `/api/data-import` | Excel数据导入 |
| 仪表盘 | `/api/dashboard` | 统计数据展示 |
| 监控 | `/api/monitor` | 系统监控指标 |
| 缓存管理 | `/api/cache` | 缓存操作管理 |

### 主要接口示例

#### 1. 获取用户分组
```bash
GET /api/usergroup/{source}/{userId}

# 示例
curl "http://localhost:7003/api/usergroup/dx/user123"
```

#### 2. 数据导入
```bash
POST /api/data-import/upload

# 示例
curl -X POST "http://localhost:7003/api/data-import/upload" \
  -F "file=@users.xlsx" \
  -F "source=dx" \
  -F "async=true"
```

#### 3. 仪表盘统计
```bash
GET /api/dashboard/statistics

# 示例
curl "http://localhost:7003/api/dashboard/statistics"
```

### 完整 API 文档

- **Swagger UI**: http://localhost:7003/swagger-ui/index.html
- **OpenAPI JSON**: http://localhost:7003/v3/api-docs

---

## 🔧 配置说明

### 核心配置项

#### Nacos 配置
```yaml
nacos:
  server-addr: **************:8848
  namespace: bokong_dev
  username: nacos
  password: LCW6KLEAHa_vectvznfv
  group: iptv
```

#### Redis 配置
```yaml
spring:
  redis:
    cluster:
      nodes: **************:7001,**************:7002
    password: your_redis_password
    timeout: 3000ms
```

#### 数据库配置
```yaml
spring:
  datasource:
    url: *************************************
    username: your_db_username
    password: your_db_password
```

### 性能调优配置

#### JVM 参数
```bash
JAVA_OPTS="-XX:+UseG1GC -XX:MaxGCPauseMillis=200 
           -XX:+UseStringDeduplication -XX:MaxRAMPercentage=75.0"
```

#### 缓存配置
```yaml
cache:
  caffeine:
    initial-capacity: 50000
    maximum-size: 1000000
    expire-after-write: 72000
```

---

## 📊 监控运维

### 监控指标

#### 系统指标
- **响应时间**: P50, P95, P99 百分位数
- **吞吐量**: QPS, TPS 统计
- **错误率**: 4xx, 5xx 错误统计
- **缓存命中率**: 本地缓存和 Redis 命中率

#### 业务指标
- **用户查询**: 成功/失败次数
- **数据导入**: 处理速度和成功率
- **分组统计**: 热门分组排行

### 健康检查

```bash
# 系统健康状态
curl http://localhost:7003/api/monitor/health

# 详细监控指标
curl http://localhost:7003/api/monitor/metrics

# 性能指标
curl http://localhost:7003/api/monitor/performance
```

### 日志管理

#### 日志级别
- **生产环境**: INFO 级别
- **开发环境**: DEBUG 级别
- **第三方库**: WARN 级别

#### 日志路径
```
/iptv/bokong/data/applogs/
├── iptv-flux-service/
│   ├── iptv-flux-service.log
│   └── iptv-flux-service-error.log
└── iptv-flux-gateway/
    ├── iptv-flux-gateway.log
    └── iptv-flux-gateway-error.log
```

---

## 🧪 测试

### 功能测试
```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn verify

# API 功能测试
./test-blacklist-whitelist-api.sh
./test-group-query-api.sh
./test-data-format-consistency.sh
```

### 性能测试
```bash
# 压力测试
ab -n 10000 -c 100 http://localhost:7003/api/usergroup/dx/user123

# 并发测试
./test-concurrent-import.sh
```

---

## 📚 开发指南

### 代码规范
- **Java**: 遵循 Google Java Style Guide
- **命名**: 驼峰命名法，见名知意
- **注释**: 类和方法必须有 Javadoc
- **日志**: 使用 SLF4J，结构化日志

### 提交规范
```bash
# 功能开发
git commit -m "feat: 新增用户分组查询接口"

# 问题修复
git commit -m "fix: 修复缓存穿透问题"

# 性能优化
git commit -m "perf: 优化数据库查询性能"
```

### 分支管理
- **master**: 生产环境分支
- **develop**: 开发环境分支
- **feature/***: 功能开发分支
- **hotfix/***: 紧急修复分支

---

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'feat: add amazing feature'`)
4. 推送分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

---

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

---

## 📞 联系我们

- **项目维护者**: Claude 4.0 sonnet 🐱
- **技术支持**: [技术支持邮箱]
- **问题反馈**: [GitHub Issues]

---

*最后更新: 2025-01-04*
