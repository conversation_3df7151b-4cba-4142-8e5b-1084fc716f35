# 🛠️ IPTV 流量管理平台 - 系统操作手册

## 📋 手册概述

本手册为 IPTV 流量管理平台的系统操作指南，涵盖日常运维、故障处理、性能优化等操作流程。

**适用人员**: 系统管理员、运维工程师、技术支持人员  
**系统版本**: v1.0.0  
**更新日期**: 2025-01-04

---

## 🚀 系统启动与停止

### 启动流程

#### 1. 环境检查
```bash
# 检查 Java 版本
java -version  # 需要 Java 17+

# 检查端口占用
netstat -tlnp | grep -E ":(7001|7003)"

# 检查依赖服务
ping **************  # Nacos 服务器
redis-cli -h ************** -p 7001 ping  # Redis 集群
mysql -h localhost -u root -p -e "SELECT 1"  # MySQL 数据库
```

#### 2. 启动核心服务
```bash
# 方式一: 直接启动 JAR
cd /app/iptv-flux-service
nohup java $JAVA_OPTS -jar iptv-flux-service.jar \
  --spring.profiles.active=prod > logs/app.log 2>&1 &

# 方式二: 使用 systemd
sudo systemctl start iptv-flux-service

# 方式三: Docker 容器
docker start iptv-flux-service
```

#### 3. 启动网关服务 (可选)
```bash
# 启动网关
cd /app/iptv-flux-gateway
nohup java $JAVA_OPTS -jar iptv-flux-gateway.jar \
  --spring.profiles.active=prod > logs/gateway.log 2>&1 &
```

#### 4. 验证启动状态
```bash
# 检查进程
ps aux | grep iptv-flux

# 检查端口监听
netstat -tlnp | grep -E ":(7001|7003)"

# 健康检查
curl http://localhost:7003/api/usergroup/health
curl http://localhost:7003/api/monitor/health
```

### 停止流程

#### 1. 优雅停止
```bash
# 发送 SIGTERM 信号
kill -TERM $(pgrep -f iptv-flux-service)

# 等待进程结束 (最多等待 30 秒)
timeout 30 tail --pid=$(pgrep -f iptv-flux-service) -f /dev/null

# 使用 systemd
sudo systemctl stop iptv-flux-service

# Docker 容器
docker stop iptv-flux-service
```

#### 2. 强制停止 (紧急情况)
```bash
# 强制杀死进程
kill -KILL $(pgrep -f iptv-flux-service)

# Docker 强制停止
docker kill iptv-flux-service
```

---

## 📊 系统监控

### 实时监控

#### 1. 系统状态监控
```bash
# 系统健康状态
curl -s http://localhost:7003/api/monitor/health | jq '.'

# 系统关键指标
curl -s http://localhost:7003/api/monitor/metrics | jq '.'

# 性能指标
curl -s http://localhost:7003/api/monitor/performance | jq '.'
```

#### 2. 业务监控
```bash
# 仪表盘统计
curl -s http://localhost:7003/api/dashboard/statistics | jq '.'

# 系统状态
curl -s http://localhost:7003/api/dashboard/system-status | jq '.'

# 热门分组
curl -s http://localhost:7003/api/dashboard/top-groups?limit=10 | jq '.'
```

#### 3. 缓存监控
```bash
# 缓存状态
curl -s http://localhost:7003/api/cache/status | jq '.'

# 缓存统计
curl -s http://localhost:7003/api/monitor/cache | jq '.'
```

### 监控指标说明

#### 关键性能指标 (KPI)
| 指标名称 | 正常范围 | 告警阈值 | 说明 |
|----------|----------|----------|------|
| 响应时间 P99 | < 200ms | > 500ms | 99%请求响应时间 |
| 缓存命中率 | > 95% | < 90% | 本地+Redis缓存命中率 |
| QPS | 1000-10000 | > 15000 | 每秒查询数 |
| 错误率 | < 0.1% | > 1% | 4xx+5xx错误比例 |
| 内存使用率 | < 80% | > 90% | JVM堆内存使用率 |
| CPU使用率 | < 70% | > 85% | 系统CPU使用率 |

#### 业务指标
| 指标名称 | 监控方式 | 说明 |
|----------|----------|------|
| 用户查询成功率 | API监控 | 用户分组查询成功比例 |
| 数据导入处理速度 | 进度监控 | 每秒处理记录数 |
| 分组数据完整性 | 定时检查 | 分组关系数据一致性 |

---

## 🔧 日常运维操作

### 缓存管理

#### 1. 缓存预热
```bash
# 手动触发缓存预热
curl -X POST "http://localhost:7003/api/cache/warmup?fullLoad=true"

# 检查预热状态
curl -s http://localhost:7003/api/cache/status | jq '.warmupJobExists'
```

#### 2. 缓存清理
```bash
# 清理本地缓存 (重启服务)
sudo systemctl restart iptv-flux-service

# 清理 Redis 缓存 (谨慎操作)
redis-cli -h ************** -p 7001 --scan --pattern "ug:*" | xargs redis-cli -h ************** -p 7001 del
```

#### 3. 缓存调度管理
```bash
# 暂停缓存调度器
curl -X POST http://localhost:7003/api/cache/pause

# 恢复缓存调度器
curl -X POST http://localhost:7003/api/cache/resume

# 查看调度状态
curl -s http://localhost:7003/api/cache/status | jq '.schedulerRunning'
```

### 数据导入管理

#### 1. 监控导入任务
```bash
# 查看导入日志列表
curl -s "http://localhost:7003/api/data-import/logs?page=1&pageSize=10" | jq '.'

# 查询特定任务进度
curl -s "http://localhost:7003/api/data-import/progress/{taskId}" | jq '.'
```

#### 2. 处理异常导入
```bash
# 查看失败的导入任务
curl -s "http://localhost:7003/api/data-import/logs" | jq '.data.items[] | select(.status == "FAILED")'

# 删除异常导入日志
curl -X DELETE "http://localhost:7003/api/data-import/logs/{logId}"
```

### 用户数据管理

#### 1. 黑名单管理
```bash
# 查询黑名单
curl -X POST "http://localhost:7003/api/usergroup/blacklist" \
  -H "Content-Type: application/json" \
  -d '{"page": 1, "pageSize": 10}'

# 添加黑名单用户
curl -X POST "http://localhost:7003/api/usergroup/blacklist/add" \
  -H "Content-Type: application/json" \
  -d '{"userId": "user123", "source": "dx", "remark": "违规用户"}'

# 删除黑名单用户
curl -X DELETE "http://localhost:7003/api/usergroup/blacklist/{id}"
```

#### 2. 白名单管理
```bash
# 查询白名单
curl -X POST "http://localhost:7003/api/usergroup/whitelist" \
  -H "Content-Type: application/json" \
  -d '{"page": 1, "pageSize": 10}'

# 添加白名单用户
curl -X POST "http://localhost:7003/api/usergroup/whitelist/add" \
  -H "Content-Type: application/json" \
  -d '{"userId": "test001", "source": "dx", "remark": "测试用户"}'
```

---

## 🚨 故障处理

### 常见故障及解决方案

#### 1. 服务无法启动

**症状**: 服务启动失败或启动后立即退出

**排查步骤**:
```bash
# 1. 检查日志
tail -f /iptv/bokong/data/applogs/iptv-flux-service/iptv-flux-service.log

# 2. 检查端口占用
netstat -tlnp | grep 7003

# 3. 检查配置文件
cat iptv-flux-service/src/main/resources/bootstrap-dev.yml

# 4. 检查依赖服务
curl -s http://**************:8848/nacos/v1/ns/operator/servers
redis-cli -h ************** -p 7001 ping
```

**解决方案**:
- 检查 Nacos 配置是否正确
- 确认 Redis 和 MySQL 连接正常
- 检查 JVM 内存设置
- 验证配置文件语法

#### 2. 响应时间过长

**症状**: API 响应时间超过 500ms

**排查步骤**:
```bash
# 1. 检查系统负载
top
iostat -x 1

# 2. 检查缓存命中率
curl -s http://localhost:7003/api/monitor/cache | jq '.cacheHitRate'

# 3. 检查数据库连接
curl -s http://localhost:7003/api/monitor/performance | jq '.database'

# 4. 检查 GC 情况
jstat -gc $(pgrep -f iptv-flux-service)
```

**解决方案**:
- 触发缓存预热
- 优化数据库查询
- 调整 JVM 参数
- 增加服务实例

#### 3. 缓存穿透

**症状**: 大量请求直接访问数据库

**排查步骤**:
```bash
# 1. 检查布隆过滤器状态
redis-cli -h ************** -p 7001 exists ug:bloom_filter

# 2. 检查缓存命中率
curl -s http://localhost:7003/api/monitor/metrics | jq '.cacheHitRate'

# 3. 查看异常日志
grep "cache miss" /iptv/bokong/data/applogs/iptv-flux-service/iptv-flux-service.log
```

**解决方案**:
- 重建布隆过滤器
- 增加缓存预热
- 调整缓存策略

#### 4. 数据导入失败

**症状**: Excel 文件导入处理失败

**排查步骤**:
```bash
# 1. 查看导入日志
curl -s "http://localhost:7003/api/data-import/logs" | jq '.data.items[] | select(.status == "FAILED")'

# 2. 检查文件格式
curl -s "http://localhost:7003/api/data-import/formats"

# 3. 查看进度详情
curl -s "http://localhost:7003/api/data-import/progress/{taskId}"
```

**解决方案**:
- 检查文件格式和大小
- 验证数据内容格式
- 调整批处理大小
- 重新提交导入任务

---

## 🔄 备份与恢复

### 数据备份

#### 1. 数据库备份
```bash
# 全量备份
mysqldump -h localhost -u root -p iptv_flux > backup_$(date +%Y%m%d_%H%M%S).sql

# 增量备份 (基于 binlog)
mysqlbinlog --start-datetime="2025-01-04 00:00:00" \
            --stop-datetime="2025-01-04 23:59:59" \
            mysql-bin.000001 > incremental_backup.sql
```

#### 2. Redis 备份
```bash
# RDB 快照备份
redis-cli -h ************** -p 7001 BGSAVE

# 复制 RDB 文件
cp /var/lib/redis/dump.rdb backup/redis_$(date +%Y%m%d_%H%M%S).rdb
```

#### 3. 配置备份
```bash
# 备份配置文件
tar -czf config_backup_$(date +%Y%m%d_%H%M%S).tar.gz \
    iptv-flux-service/src/main/resources/ \
    iptv-flux-gateway/src/main/resources/
```

### 数据恢复

#### 1. 数据库恢复
```bash
# 全量恢复
mysql -h localhost -u root -p iptv_flux < backup_20250104_120000.sql

# 增量恢复
mysql -h localhost -u root -p iptv_flux < incremental_backup.sql
```

#### 2. Redis 恢复
```bash
# 停止 Redis 服务
sudo systemctl stop redis

# 恢复 RDB 文件
cp backup/redis_20250104_120000.rdb /var/lib/redis/dump.rdb

# 启动 Redis 服务
sudo systemctl start redis
```

---

## 📈 性能优化

### JVM 调优

#### 1. 内存参数优化
```bash
# 生产环境推荐参数
JAVA_OPTS="-Xms4g -Xmx8g \
           -XX:+UseG1GC \
           -XX:MaxGCPauseMillis=200 \
           -XX:+UseStringDeduplication \
           -XX:+DisableExplicitGC \
           -XX:MaxRAMPercentage=75.0"
```

#### 2. GC 调优
```bash
# 监控 GC 情况
jstat -gc $(pgrep -f iptv-flux-service) 5s

# GC 日志分析
-XX:+PrintGC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps \
-Xloggc:/iptv/bokong/data/applogs/gc.log
```

### 数据库优化

#### 1. 索引优化
```sql
-- 检查慢查询
SHOW PROCESSLIST;
SELECT * FROM information_schema.PROCESSLIST WHERE TIME > 5;

-- 分析查询计划
EXPLAIN SELECT * FROM user_group_relation WHERE user_id = 'user123';

-- 添加必要索引
CREATE INDEX idx_user_source ON user_group_relation(user_id, source);
```

#### 2. 连接池优化
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
```

### 缓存优化

#### 1. 缓存策略调整
```yaml
cache:
  caffeine:
    initial-capacity: 100000    # 增加初始容量
    maximum-size: 2000000       # 增加最大容量
    expire-after-write: 86400   # 调整过期时间
```

#### 2. Redis 优化
```bash
# Redis 内存优化
redis-cli CONFIG SET maxmemory 8gb
redis-cli CONFIG SET maxmemory-policy allkeys-lru

# 连接池优化
spring.redis.lettuce.pool.max-active=20
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=2
```

---

## 📋 运维检查清单

### 日常检查 (每日)
- [ ] 检查服务运行状态
- [ ] 查看系统资源使用情况
- [ ] 检查错误日志
- [ ] 验证关键接口可用性
- [ ] 检查缓存命中率

### 周期检查 (每周)
- [ ] 分析性能趋势
- [ ] 检查数据库性能
- [ ] 清理过期日志文件
- [ ] 更新监控报表
- [ ] 检查备份完整性

### 月度检查 (每月)
- [ ] 系统容量规划评估
- [ ] 安全漏洞扫描
- [ ] 配置文件审查
- [ ] 性能基准测试
- [ ] 灾难恢复演练

---

*操作手册版本: v1.0*  
*最后更新: 2025-01-04*  
*维护者: Claude 4.0 sonnet* 🐱
