# 流量平台数据同步配置说明

## 概述

本文档说明如何在Nacos配置中心配置流量平台数据同步相关的FTP连接参数和其他配置项。

## Nacos配置中心配置

### 配置文件位置

- **开发环境**: `iptv-flux-service-dev.yaml`
- **测试环境**: `iptv-flux-service-test.yaml`
- **生产环境**: `iptv-flux-service-prod.yaml`

### 配置组和命名空间

- **Group**: `iptv`
- **Namespace**: 
  - 开发环境: `bokong_dev`
  - 测试环境: `test`
  - 生产环境: `prod`

## 主要配置项说明

### 1. FTP连接配置

```yaml
traffic:
  sync:
    ftp:
      host: ***********          # FTP服务器地址
      port: 2121                 # FTP服务器端口
      username: vstorero         # FTP用户名
      password: iptv_2024at2other # FTP密码
```

### 2. 超时配置

```yaml
traffic:
  sync:
    ftp:
      connection-timeout: 30000  # 连接超时时间（毫秒）
      data-timeout: 60000        # 数据传输超时时间（毫秒）
      control-timeout: 30000     # 控制连接超时时间（毫秒）
```

### 3. 重试配置

```yaml
traffic:
  sync:
    ftp:
      retry-attempts: 3          # 重试次数
      retry-interval: 1000       # 重试间隔（毫秒）
```

### 4. 传输模式配置

```yaml
traffic:
  sync:
    ftp:
      passive-mode: true         # 是否使用被动模式
      binary-mode: true          # 是否使用二进制传输模式
```

### 5. 连接池配置

```yaml
traffic:
  sync:
    ftp:
      pool:
        max-total: 10                           # 连接池最大连接数
        max-idle: 5                             # 最大空闲连接数
        min-idle: 1                             # 最小空闲连接数
        max-wait-millis: 30000                  # 获取连接最大等待时间
        min-evictable-idle-time-millis: 300000  # 连接空闲时间（5分钟）
        time-between-eviction-runs-millis: 60000 # 空闲连接检测间隔（1分钟）
        test-on-borrow: true                    # 获取连接时测试
        test-on-return: false                   # 归还连接时测试
        test-while-idle: true                   # 空闲时测试连接
```

### 6. 异步处理配置

```yaml
user-group:
  data-import:
    async:
      core-pool-size: 10         # 核心线程数
      max-pool-size: 20          # 最大线程数
      queue-capacity: 500        # 队列容量
      keep-alive: 300            # 线程保持活跃时间（秒）
```

## 环境特定配置

### 开发环境

- FTP服务器: `***********:2121`
- 连接池大小: 较小（适合开发调试）
- 日志级别: `DEBUG`（详细日志）

### 测试环境

- FTP服务器: `172.25.234.100:21`
- 连接池大小: 中等（适合功能测试）
- 日志级别: `DEBUG`（详细日志）

### 生产环境

- FTP服务器: `**************:21`
- 连接池大小: 较大（适合高并发）
- 日志级别: `WARN`（只记录警告和错误）
- 密码: 使用环境变量 `${FTP_PASSWORD}`

## 配置更新步骤

### 1. 登录Nacos控制台

访问Nacos控制台：`http://nacos-server:8848/nacos`

### 2. 选择命名空间

根据环境选择对应的命名空间：
- 开发环境: `bokong_dev`
- 测试环境: `test`
- 生产环境: `prod`

### 3. 创建或更新配置

- **Data ID**: `iptv-flux-service-{环境}.yaml`
- **Group**: `iptv`
- **配置格式**: `YAML`
- **配置内容**: 复制对应环境的配置内容

### 4. 发布配置

点击"发布"按钮，配置将自动推送到对应的服务实例。

## 配置验证

### 1. 检查服务启动日志

服务启动时会输出FTP连接池初始化日志：

```
流量平台-----> FTP连接池初始化完成，最大连接数: 10，最大空闲: 5，最小空闲: 1
```

### 2. 健康检查接口

访问健康检查接口验证配置是否正确：

```bash
curl http://localhost:7003/api/traffic/sync/health
```

### 3. 监控指标

通过Prometheus监控指标查看FTP连接池状态：

```bash
curl http://localhost:7003/actuator/metrics/ftp.pool.active
curl http://localhost:7003/actuator/metrics/ftp.pool.idle
```

## 常见问题

### 1. FTP连接失败

**问题**: 服务启动时FTP连接失败
**解决**: 
- 检查FTP服务器地址和端口是否正确
- 检查用户名和密码是否正确
- 检查网络连通性

### 2. 连接池耗尽

**问题**: 获取FTP连接超时
**解决**: 
- 增加 `max-total` 连接池大小
- 减少 `max-wait-millis` 等待时间
- 检查是否有连接泄漏

### 3. 文件下载超时

**问题**: 大文件下载超时
**解决**: 
- 增加 `data-timeout` 数据传输超时时间
- 检查网络带宽和稳定性
- 考虑分片下载大文件

## 安全注意事项

1. **密码安全**: 生产环境密码使用环境变量，不要明文配置
2. **网络安全**: 确保FTP服务器网络访问控制正确
3. **权限控制**: FTP用户只授予必要的文件访问权限
4. **日志安全**: 生产环境避免记录敏感信息到日志

## 性能调优建议

1. **连接池大小**: 根据并发量调整连接池大小
2. **超时时间**: 根据网络环境调整超时时间
3. **重试策略**: 根据网络稳定性调整重试次数和间隔
4. **监控告警**: 设置连接池使用率和错误率告警
