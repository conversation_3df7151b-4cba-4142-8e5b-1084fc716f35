# Git Commit Message

## 主要提交信息
```
feat: 完善用户管理系统和数据导入功能

- 新增黑名单白名单管理API（查询/添加/删除）
- 优化数据导入异步处理和进度跟踪
- 完善监控指标和性能统计
- 统一数据格式和来源标识（dx/lt/yd）
- 新增网关模块和配置优化
- 迁移到Jakarta验证框架
- 优化日志配置和CORS设置
```

## 详细变更说明

### 🆕 新增功能
- **黑名单白名单管理**：完整的CRUD接口和业务逻辑
- **数据导入优化**：增强异步处理能力和进度监控
- **网关模块**：新增iptv-flux-gateway模块
- **监控指标**：完善性能监控和业务指标收集

### 🔧 技术改进
- **Jakarta迁移**：从javax.validation迁移到jakarta.validation
- **异步处理优化**：调整线程池参数支持大数据量处理
- **Redis配置优化**：改进连接池和序列化配置
- **日志优化**：调整第三方库日志级别，减少控制台输出

### 📝 配置变更
- **Nacos配置**：更新服务器地址和命名空间
- **CORS设置**：调整跨域配置安全策略
- **数据格式统一**：来源字段标准化为dx/lt/yd
- **文件上传支持**：增强大文件上传配置

### 📚 文档和测试
- **API文档**：完善接口文档和使用说明
- **测试脚本**：新增功能测试和验证脚本
- **数据库脚本**：黑名单白名单表结构
- **部署文档**：系统架构和配置说明

### 🐛 修复和优化
- **文件结构**：修正BatchProcessResult类位置
- **包扫描**：扩展组件扫描范围
- **性能优化**：改进缓存策略和查询效率
- **错误处理**：完善异常处理和用户反馈
