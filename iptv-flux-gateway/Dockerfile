FROM eclipse-temurin:17-jre-alpine
WORKDIR /app

# 创建日志目录
RUN mkdir -p /iptv/bokong/data/applogs/iptv-flux-gateway

# 优化JVM参数
ENV JAVA_OPTS="-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication -XX:+DisableExplicitGC -XX:+ParallelRefProcEnabled -XX:MaxRAMPercentage=75.0 -XX:+ExitOnOutOfMemoryError"

# 传入构建参数
ARG JAR_NAME
ARG SERVER_PORT=7000

# 复制已构建的jar文件
COPY target/${JAR_NAME} app.jar

# 使用构建参数设置EXPOSE端口
EXPOSE ${SERVER_PORT}

# 设置环境变量SERVER_PORT
ENV SERVER_PORT=${SERVER_PORT}

ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]