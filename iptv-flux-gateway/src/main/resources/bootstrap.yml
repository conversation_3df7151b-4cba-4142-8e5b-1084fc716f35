spring:
  application:
    name: @project.artifactId@
    deleteBeforeInit: true
  profiles:
    active: @profiles.active@
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      discovery:
        metadata:
          management:
            context-path: ${server.servlet.context-path:}/actuator
        server-addr: ${nacos.server-addr}
        username: ${nacos.username}
        password: ${nacos.password}
        namespace: ${nacos.namespace}
        group: ${nacos.group}
        enabled: true
      config:
        server-addr: ${nacos.server-addr}
        username: ${nacos.username}
        password: ${nacos.password}
        file-extension: ${nacos.file-extension}
        namespace: ${nacos.namespace}
        group: ${nacos.group}
        refresh-enabled: true
        extension-configs:
          - data-id: ${spring.application.name}-${spring.profiles.active}.${nacos.file-extension}
            group: ${nacos.group}
            refresh: true
        shared-configs:
          - data-id: iptv-redis-${spring.profiles.active}.${nacos.file-extension}
            group: ${nacos.group}
            refresh: true
# 日志配置
log:
  file:
    path: /iptv/bokong/data/applogs/${spring.application.name}/
    name: ${spring.application.name}
    suffix: log
logging:
  level:
    root: INFO
    com.iptv.flux: INFO
    org.springframework: WARN
    org.springframework.cloud.gateway: WARN
    org.springframework.cloud.nacos: WARN
    org.springframework.boot: WARN
    org.springframework.context: WARN
    org.springframework.beans: WARN
    com.alibaba.nacos: WARN
    org.springframework.data.redis: WARN
    reactor.netty: WARN