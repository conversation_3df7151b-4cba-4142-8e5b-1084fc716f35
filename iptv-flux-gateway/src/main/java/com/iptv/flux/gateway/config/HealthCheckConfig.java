package com.iptv.flux.gateway.config;

import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.ReactiveHealthIndicator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import reactor.core.publisher.Mono;

/**
 * @projectName:    iptv-flux-manager 
 * @package:        com.iptv.flux.gateway.config
 * @className:      HealthCheckConfig
 * @author:     chiron
 * @description:  TODO  
 * @date:    2025/6/12 14:21
 * @version:    1.0
 */
@Configuration
public class HealthCheckConfig {
    @Bean
    @Primary
    @ConditionalOnProperty(name = "management.health.redis-custom.enabled", havingValue = "true", matchIfMissing = true)
    public ReactiveHealthIndicator redisHealthIndicator() {
        return () -> Mono.just(Health.up().withDetail("redis", "configured").build());
    }
}
