package com.iptv.flux.gateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.cloud.gateway.filter.ratelimit.RedisRateLimiter;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @description 自定义路由配置
 * @date 2025/3/24 14:35
 */
@Configuration
@Slf4j
public class RouteConfig {

    private final RedisRateLimiter redisRateLimiter;
    private final KeyResolver keyResolver;

    // 构造函数注入
    public RouteConfig(RedisRateLimiter userGroupRateLimiter, KeyResolver ipKeyResolver) {
        this.redisRateLimiter = userGroupRateLimiter;
        this.keyResolver = ipKeyResolver;
        log.info("初始化RouteConfig，注入RedisRateLimiter和KeyResolver");
    }

    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        log.info("创建自定义路由规则，启用Redis限流");
        return builder.routes()
                .route("user_group_route", r -> r
                        .path("/api/usergroup/**")
                        .filters(f -> f
                                .rewritePath("/api/usergroup/(?<segment>.*)", "/api/usergroup/${segment}")
                                // 添加限流过滤器
                                .requestRateLimiter(c -> c
                                        .setRateLimiter(redisRateLimiter)
                                        .setKeyResolver(keyResolver))
                                .retry(config -> config
                                        .setRetries(3)
                                        .setStatuses(HttpStatus.INTERNAL_SERVER_ERROR,
                                                HttpStatus.BAD_GATEWAY,
                                                HttpStatus.SERVICE_UNAVAILABLE,
                                                HttpStatus.GATEWAY_TIMEOUT)
                                        .setMethods(HttpMethod.GET, HttpMethod.POST)))
                        .uri("lb://iptv-flux-service"))
                .route("cache_management_route", r -> r
                        .path("/api/cache/**")
                        .filters(f -> f
                                .rewritePath("/api/cache/(?<segment>.*)", "/api/cache/${segment}")
                                .retry(config -> config
                                        .setRetries(3)
                                        .setStatuses(HttpStatus.INTERNAL_SERVER_ERROR,
                                                HttpStatus.BAD_GATEWAY)
                                        .setMethods(HttpMethod.GET, HttpMethod.POST)))
                        .uri("lb://iptv-flux-service"))
                .route("data_import_route", r -> r
                        .path("/api/data-import/**")
                        .filters(f -> f
                                .rewritePath("/api/data-import/(?<segment>.*)", "/api/data-import/${segment}")
                                // 数据导入接口不限流，因为文件上传可能需要较长时间
                                .retry(config -> config
                                        .setRetries(2) // 减少重试次数，避免大文件重复上传
                                        .setStatuses(HttpStatus.INTERNAL_SERVER_ERROR,
                                                HttpStatus.BAD_GATEWAY,
                                                HttpStatus.SERVICE_UNAVAILABLE)
                                        .setMethods(HttpMethod.GET, HttpMethod.POST)))
                        .uri("lb://iptv-flux-service"))
                .build();
    }
}