package com.iptv.flux.gateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.cloud.gateway.filter.ratelimit.RedisRateLimiter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.core.publisher.Mono;

import java.util.Objects;

/**
 * @projectName: flux-manager
 * @package: com.iptv.flux.gateway.config
 * @className: RateLimiterConfig
 * @author: chiron
 * @description: Redis限流配置
 * @date: 2025/3/24 14:09
 * @version: 1.0
 */
@Configuration
@Slf4j
public class RateLimiterConfig {
    /**
     * 基于请求IP的限流键解析器
     */
    @Bean
    public KeyResolver ipKeyResolver() {
        return exchange -> {
            String ip = exchange.getRequest().getRemoteAddress().getAddress().getHostAddress();
            log.debug("IP限流键解析: {}", ip);
            return Mono.just(ip);
        };
    }

    /**
     * 用户分组服务限流器
     */
    @Bean
    public RedisRateLimiter userGroupRateLimiter() {
        // 每秒10000个请求, 突发可以达到20000个请求
        return new RedisRateLimiter(10000, 20000, 1);
    }
}
