package com.iptv.flux.gateway.error;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.gateway.error
 * @className: SpecialPathController
 * @author: chiron
 * @description: TODO
 * @date: 2025/6/12 14:23
 * @version: 1.0
 */
@Deprecated
@RestController
public class SpecialPathController {
    @GetMapping({"/swagger-ui.html", "/swagger-ui/**", "/v3/api-docs/**", "/webjars/**"})
    public Mono<Map<String, String>> handleSwaggerPaths() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "DISABLED");
        response.put("message", "Swagger UI is disabled in this environment");
        return Mono.just(response);
    }
}
