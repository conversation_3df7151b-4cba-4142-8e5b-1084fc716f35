package com.iptv.flux.gateway.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.gateway.config
 * @className: GatewayConfig
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 12:53
 * @version: 1.0
 */
@Configuration
public class GatewayConfig {

    @Bean
    public ObjectMapper objectMapper() {
        return new ObjectMapper();
    }
}
