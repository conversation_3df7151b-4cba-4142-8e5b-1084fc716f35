package com.iptv.flux.gateway;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@Slf4j
public class GatewayApplication {
    public static void main(String[] args) {
        long startTime = System.currentTimeMillis();
        log.info("流量平台-----> 开始启动 GatewayApplication");

        SpringApplication.run(GatewayApplication.class, args);

        long endTime = System.currentTimeMillis();
        log.info("流量平台-----> GatewayApplication 启动完成，总耗时: {}ms", endTime - startTime);
    }
}