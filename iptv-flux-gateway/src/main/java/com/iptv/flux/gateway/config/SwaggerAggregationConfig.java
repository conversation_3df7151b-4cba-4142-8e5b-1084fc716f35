package com.iptv.flux.gateway.config;

import org.springdoc.core.properties.SwaggerUiConfigParameters;
import org.springdoc.core.properties.SwaggerUiConfigProperties;
import org.springframework.cloud.gateway.route.RouteDefinitionLocator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
public class SwaggerAggregationConfig {

    @Bean
    @Primary
    public SwaggerUiConfigParameters swaggerUiConfigParameters(
            SwaggerUiConfigProperties swaggerUiConfigProperties,
            RouteDefinitionLocator routeDefinitionLocator) {

        SwaggerUiConfigParameters parameters = new SwaggerUiConfigParameters(swaggerUiConfigProperties);

        List<String> routes = new ArrayList<>();
        routeDefinitionLocator.getRouteDefinitions()
                .subscribe(routeDefinition -> {
                    String name = routeDefinition.getId();
                    if (name.matches(".*-service")) {
                        routes.add(name);
                        parameters.addGroup(name);
                    }
                });

        return parameters;
    }
}