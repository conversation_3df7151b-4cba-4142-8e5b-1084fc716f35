package com.iptv.flux.gateway.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RateLimitingFilter implements GlobalFilter, Ordered {
    // Per instance
    private static final int MAX_REQUESTS_PER_SECOND = 10000;
    // seconds
    private static final int CLEANUP_INTERVAL = 5;

    private final ConcurrentMap<String, AtomicInteger> requestCounts = new ConcurrentHashMap<>();
    private volatile long lastCleanupTime = System.currentTimeMillis();

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        String clientIp = Objects.requireNonNull(exchange.getRequest().getRemoteAddress()).getAddress().getHostAddress();
        long now = System.currentTimeMillis();

        // Clean up old entries periodically
        if (now - lastCleanupTime > CLEANUP_INTERVAL * 1000) {
            synchronized (this) {
                if (now - lastCleanupTime > CLEANUP_INTERVAL * 1000) {
                    requestCounts.clear();
                    lastCleanupTime = now;
                }
            }
        }

        // Count requests
        AtomicInteger count = requestCounts.computeIfAbsent(clientIp, k -> new AtomicInteger(0));
        // Check if rate limit is exceeded
        if (count.incrementAndGet() > MAX_REQUESTS_PER_SECOND) {
            log.warn("Rate limit exceeded for client IP: {}", clientIp);
            exchange.getResponse().setStatusCode(HttpStatus.TOO_MANY_REQUESTS);
            return exchange.getResponse().setComplete();
        }

        return chain.filter(exchange);
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 100;
    }
}