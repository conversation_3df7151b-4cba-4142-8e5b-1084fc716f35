#!/bin/bash

# 流量平台数据同步功能测试脚本
# 作者: Claude 4.0 sonnet
# 日期: 2025/8/14

set -e

echo "=========================================="
echo "流量平台数据同步功能测试"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)
SERVICE_DIR="$PROJECT_ROOT/iptv-flux-service"

echo -e "${BLUE}项目根目录: $PROJECT_ROOT${NC}"
echo -e "${BLUE}服务目录: $SERVICE_DIR${NC}"

# 检查Maven是否安装
if ! command -v mvn &> /dev/null; then
    echo -e "${RED}错误: Maven未安装或不在PATH中${NC}"
    exit 1
fi

# 检查Java版本
JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 17 ]; then
    echo -e "${RED}错误: 需要Java 17或更高版本，当前版本: $JAVA_VERSION${NC}"
    exit 1
fi

echo -e "${GREEN}Java版本检查通过: $JAVA_VERSION${NC}"

# 进入服务目录
cd "$SERVICE_DIR"

echo ""
echo "=========================================="
echo "1. 编译项目"
echo "=========================================="

mvn clean compile -DskipTests
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 项目编译成功${NC}"
else
    echo -e "${RED}✗ 项目编译失败${NC}"
    exit 1
fi

echo ""
echo "=========================================="
echo "2. 运行单元测试"
echo "=========================================="

echo -e "${YELLOW}运行FTPFileProcessor测试...${NC}"
mvn test -Dtest=FTPFileProcessorTest
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ FTPFileProcessor测试通过${NC}"
else
    echo -e "${RED}✗ FTPFileProcessor测试失败${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}运行GroupsFileParser测试...${NC}"
mvn test -Dtest=GroupsFileParserTest
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ GroupsFileParser测试通过${NC}"
else
    echo -e "${RED}✗ GroupsFileParser测试失败${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}运行TrafficSyncService测试...${NC}"
mvn test -Dtest=TrafficSyncServiceTest
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ TrafficSyncService测试通过${NC}"
else
    echo -e "${RED}✗ TrafficSyncService测试失败${NC}"
    exit 1
fi

echo ""
echo "=========================================="
echo "3. 运行所有流量同步相关测试"
echo "=========================================="

mvn test -Dtest="*Traffic*Test,*FTP*Test,*Groups*Test,*UserGroup*Test"
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 所有流量同步测试通过${NC}"
else
    echo -e "${RED}✗ 部分流量同步测试失败${NC}"
    exit 1
fi

echo ""
echo "=========================================="
echo "4. 生成测试报告"
echo "=========================================="

mvn surefire-report:report
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 测试报告生成成功${NC}"
    echo -e "${BLUE}测试报告位置: $SERVICE_DIR/target/site/surefire-report.html${NC}"
else
    echo -e "${YELLOW}⚠ 测试报告生成失败，但测试已通过${NC}"
fi

echo ""
echo "=========================================="
echo "5. 代码覆盖率检查"
echo "=========================================="

mvn jacoco:report
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 代码覆盖率报告生成成功${NC}"
    echo -e "${BLUE}覆盖率报告位置: $SERVICE_DIR/target/site/jacoco/index.html${NC}"
else
    echo -e "${YELLOW}⚠ 代码覆盖率报告生成失败${NC}"
fi

echo ""
echo "=========================================="
echo "6. 集成测试准备检查"
echo "=========================================="

# 检查配置文件
CONFIG_FILE="$PROJECT_ROOT/docs/nacos-config-traffic-sync.yaml"
if [ -f "$CONFIG_FILE" ]; then
    echo -e "${GREEN}✓ 配置文件存在: $CONFIG_FILE${NC}"
else
    echo -e "${RED}✗ 配置文件不存在: $CONFIG_FILE${NC}"
fi

# 检查文档
DOC_FILE="$PROJECT_ROOT/docs/流量平台数据同步配置说明.md"
if [ -f "$DOC_FILE" ]; then
    echo -e "${GREEN}✓ 配置说明文档存在: $DOC_FILE${NC}"
else
    echo -e "${RED}✗ 配置说明文档不存在: $DOC_FILE${NC}"
fi

echo ""
echo "=========================================="
echo "测试总结"
echo "=========================================="

echo -e "${GREEN}✓ 所有单元测试通过${NC}"
echo -e "${GREEN}✓ 代码编译成功${NC}"
echo -e "${GREEN}✓ 流量平台数据同步功能开发完成${NC}"

echo ""
echo "=========================================="
echo "下一步操作建议"
echo "=========================================="

echo "1. 部署到测试环境进行集成测试"
echo "2. 配置Nacos配置中心的FTP连接参数"
echo "3. 准备测试数据文件（groups.txt, userGroup.txt等）"
echo "4. 测试FTP服务器连接和文件下载功能"
echo "5. 验证数据库数据同步结果"

echo ""
echo -e "${GREEN}流量平台数据同步功能测试完成！${NC}"

# 返回项目根目录
cd "$PROJECT_ROOT"
