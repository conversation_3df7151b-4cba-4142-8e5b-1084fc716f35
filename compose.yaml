#version: '3.8'
#services:
#  # MySQL服务
#  mysql:
#    image: mysql:8.0
#    container_name: flux-manager-mysql
#    environment:
#      MYSQL_ROOT_PASSWORD: ${MYSQL_PASSWORD:-password}
#      MYSQL_DATABASE: flux_manager
#    volumes:
#      - mysql-data:/var/lib/mysql
#      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
#    ports:
#      - "3306:3306"
#    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci --innodb_buffer_pool_size=4G --max_connections=2000
#    networks:
#      - flux-manager-net
#    restart: always
#    deploy:
#      resources:
#        limits:
#          cpus: '4'
#          memory: 8G
#    healthcheck:
#      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_PASSWORD:-password}"]
#      interval: 10s
#      timeout: 5s
#      retries: 5
#
#  # Redis集群
#  redis1:
#    image: redis:6.2-alpine
#    container_name: redis1
#    ports:
#      - "8001:8001"
#    volumes:
#      - redis1-data:/data
#    command: redis-server --port 8001 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru --requirepass ${REDIS_PASSWORD:-BnndxDaqTnxQimXU_bgH}
#    networks:
#      - flux-manager-net
#    restart: always
#    deploy:
#      resources:
#        limits:
#          cpus: '2'
#          memory: 3G
#    healthcheck:
#      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-BnndxDaqTnxQimXU_bgH}", "ping"]
#      interval: 5s
#      timeout: 3s
#      retries: 5
#
#  redis2:
#    image: redis:6.2-alpine
#    container_name: redis2
#    ports:
#      - "8002:8002"
#    volumes:
#      - redis2-data:/data
#    command: redis-server --port 8002 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru --requirepass ${REDIS_PASSWORD:-BnndxDaqTnxQimXU_bgH}
#    networks:
#      - flux-manager-net
#    restart: always
#    deploy:
#      resources:
#        limits:
#          cpus: '2'
#          memory: 3G
#
#  redis3:
#    image: redis:6.2-alpine
#    container_name: redis3
#    ports:
#      - "8003:8003"
#    volumes:
#      - redis3-data:/data
#    command: redis-server --port 8003 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru --requirepass ${REDIS_PASSWORD:-BnndxDaqTnxQimXU_bgH}
#    networks:
#      - flux-manager-net
#    restart: always
#    deploy:
#      resources:
#        limits:
#          cpus: '2'
#          memory: 3G
#
#  redis4:
#    image: redis:6.2-alpine
#    container_name: redis4
#    ports:
#      - "8004:8004"
#    volumes:
#      - redis4-data:/data
#    command: redis-server --port 8004 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru --requirepass ${REDIS_PASSWORD:-BnndxDaqTnxQimXU_bgH}
#    networks:
#      - flux-manager-net
#    restart: always
#    deploy:
#      resources:
#        limits:
#          cpus: '2'
#          memory: 3G
#
#  redis5:
#    image: redis:6.2-alpine
#    container_name: redis5
#    ports:
#      - "8005:8005"
#    volumes:
#      - redis5-data:/data
#    command: redis-server --port 8005 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru --requirepass ${REDIS_PASSWORD:-BnndxDaqTnxQimXU_bgH}
#    networks:
#      - flux-manager-net
#    restart: always
#    deploy:
#      resources:
#        limits:
#          cpus: '2'
#          memory: 3G
#
#  redis6:
#    image: redis:6.2-alpine
#    container_name: redis6
#    ports:
#      - "8006:8006"
#    volumes:
#      - redis6-data:/data
#    command: redis-server --port 8006 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru --requirepass ${REDIS_PASSWORD:-BnndxDaqTnxQimXU_bgH}
#    networks:
#      - flux-manager-net
#    restart: always
#    deploy:
#      resources:
#        limits:
#          cpus: '2'
#          memory: 3G
#
#  # Redis集群初始化
#  redis-cluster-init:
#    image: redis:6.2-alpine
#    depends_on:
#      - redis1
#      - redis2
#      - redis3
#      - redis4
#      - redis5
#      - redis6
#    command: >
#      sh -c "sleep 10 && redis-cli -a ${REDIS_PASSWORD:-BnndxDaqTnxQimXU_bgH} --cluster create redis1:8001 redis2:8002 redis3:8003 redis4:8004 redis5:8005 redis6:8006 --cluster-replicas 1 --cluster-yes"
#    networks:
#      - flux-manager-net
#
#  # Nacos注册中心
#  nacos:
#    image: nacos/nacos-server:v2.1.1
#    container_name: nacos-server
#    environment:
#      - MODE=standalone
#      - SPRING_DATASOURCE_PLATFORM=mysql
#      - MYSQL_SERVICE_HOST=mysql
#      - MYSQL_SERVICE_PORT=3306
#      - MYSQL_SERVICE_DB_NAME=nacos
#      - MYSQL_SERVICE_USER=root
#      - MYSQL_SERVICE_PASSWORD=${MYSQL_PASSWORD:-password}
#      - JVM_XMS=1g
#      - JVM_XMX=1g
#    volumes:
#      - nacos-data:/home/<USER>/data
#    ports:
#      - "8848:8848"
#      - "9848:9848"
#    networks:
#      - flux-manager-net
#    restart: always
#    depends_on:
#      - mysql
#    healthcheck:
#      test: ["CMD", "curl", "-f", "http://localhost:8848/nacos/"]
#      interval: 10s
#      timeout: 5s
#      retries: 3
#
#  # 网关服务
#  iptv-flux-gateway:
#    build:
#      context: .
#      dockerfile: Dockerfile-gateway
#    container_name: iptv-flux-gateway
#    depends_on:
#      - nacos
#    environment:
#      - NACOS_SERVER=nacos:8848
#      - NACOS_NAMESPACE=public
#      - PROFILE=prod
#      - LOG_PATH=/iptv/bokong/data/applogs/iptv-flux-gateway/
#    volumes:
#      - /iptv/bokong/data/applogs/iptv-flux-gateway:/iptv/bokong/data/applogs/iptv-flux-gateway
#    ports:
#      - "7000:7000"
#    networks:
#      - flux-manager-net
#    restart: always
#    deploy:
#      replicas: 2
#      resources:
#        limits:
#          cpus: '2'
#          memory: 4G
#    healthcheck:
#      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
#      interval: 10s
#      timeout: 5s
#      retries: 3
#
#  # 用户分组服务
#  iptv-flux-service:
#    build:
#      context: .
#      dockerfile: Dockerfile-service
#    container_name: iptv-flux-service
#    depends_on:
#      - mysql
#      - redis1
#      - redis2
#      - redis3
#      - redis4
#      - redis5
#      - redis6
#      - redis-cluster-init
#      - nacos
#    environment:
#      - MYSQL_HOST=mysql
#      - MYSQL_PORT=3306
#      - MYSQL_DB=flux-manager
#      - MYSQL_USER=root
#      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-password}
#      - REDIS_NODES=redis1:8001,redis2:8002,redis3:8003,redis4:8004,redis5:8005,redis6:8006
#      - REDIS_PASSWORD=${REDIS_PASSWORD:-BnndxDaqTnxQimXU_bgH}
#      - NACOS_SERVER=nacos:8848
#      - NACOS_NAMESPACE=public
#      - PROFILE=prod
#      - LOG_PATH=/iptv/bokong/data/applogs/iptv-flux-service/
#    volumes:
#      - /iptv/bokong/data/applogs/iptv-flux-service:/iptv/bokong/data/applogs/iptv-flux-service
#    ports:
#      - "7003:7003"
#    networks:
#      - flux-manager-net
#    restart: always
#    deploy:
#      replicas: 4
#      resources:
#        limits:
#          cpus: '4'
#          memory: 8G
#    healthcheck:
#      test: ["CMD", "curl", "-f", "http://localhost:8081/actuator/health"]
#      interval: 10s
#      timeout: 5s
#      retries: 3
#
#networks:
#  flux-manager-net:
#    driver: bridge
#
#volumes:
#  mysql-data:
#  redis1-data:
#  redis2-data:
#  redis3-data:
#  redis4-data:
#  redis5-data:
#  redis6-data:
#  nacos-data: