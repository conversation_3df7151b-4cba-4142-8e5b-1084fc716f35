# Maven构建目录
target/
*/target/
**/target/

# 编译后的类文件
*.class

# 日志文件
*.log
../logs/
logs/

# 包文件
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Maven相关
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# IntelliJ IDEA文件
.idea/
*.iml
*.iws
*.ipr
.idea_modules/

# Eclipse文件
.classpath
.project
.settings/
.metadata/

# VS Code文件
.vscode/

# Mac系统文件
.DS_Store

# Windows系统文件
Thumbs.db
ehthumbs.db
Desktop.ini

# 本地配置文件
application-local.yml
application-local.properties