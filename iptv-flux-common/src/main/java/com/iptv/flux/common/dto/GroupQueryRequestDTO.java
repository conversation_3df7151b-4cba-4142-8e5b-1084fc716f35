package com.iptv.flux.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分组查询请求DTO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "分组查询请求参数")
public class GroupQueryRequestDTO {

    @Schema(description = "分组名称", example = "VIP用户组")
    private String groupName;

    @Schema(description = "策略ID", example = "strategy001")
    private String strategyId;

    @Schema(description = "平台标识", example = "dx")
    private String platform;

    @Schema(description = "来源标识", example = "dx",
            allowableValues = {"dx", "yd", "lt", "other"})
    private String source;

    @Schema(description = "是否激活", example = "true")
    private Boolean active;

    @Schema(description = "最小覆盖度", example = "1000")
    private Long minCoverage;

    @Schema(description = "最大覆盖度", example = "10000")
    private Long maxCoverage;

    @Schema(description = "生成时间开始", example = "2025-01-01 00:00:00")
    private java.time.LocalDateTime generateTimeStart;

    @Schema(description = "生成时间结束", example = "2025-12-31 23:59:59")
    private java.time.LocalDateTime generateTimeEnd;

    @Min(value = 1, message = "页码必须大于0")
    @Schema(description = "当前页码", example = "1")
    private Integer page;

    @Min(value = 1, message = "每页条数必须大于0")
    @Max(value = 100, message = "每页条数不能超过100")
    @Schema(description = "每页条数", example = "10")
    private Integer pageSize;

    /**
     * 检查是否为空查询（所有参数都为空）
     */
    public boolean isEmpty() {
        return (groupName == null || groupName.trim().isEmpty()) &&
               (strategyId == null || strategyId.trim().isEmpty()) &&
               (platform == null || platform.trim().isEmpty()) &&
               (source == null || source.trim().isEmpty()) &&
               active == null &&
               minCoverage == null &&
               maxCoverage == null &&
               generateTimeStart == null &&
               generateTimeEnd == null;
    }

    /**
     * 获取页码，默认为1
     */
    public int getPageOrDefault() {
        return page != null && page > 0 ? page : 1;
    }

    /**
     * 获取每页大小，默认为10
     */
    public int getPageSizeOrDefault() {
        return pageSize != null && pageSize > 0 ? pageSize : 10;
    }

    /**
     * 获取非空的分组名称
     */
    public String getTrimmedGroupName() {
        return groupName != null ? groupName.trim() : null;
    }

    /**
     * 获取非空的策略ID
     */
    public String getTrimmedStrategyId() {
        return strategyId != null ? strategyId.trim() : null;
    }

    /**
     * 获取非空的平台标识
     */
    public String getTrimmedPlatform() {
        return platform != null ? platform.trim() : null;
    }

    /**
     * 获取非空的来源标识
     */
    public String getTrimmedSource() {
        return source != null ? source.trim() : null;
    }

    /**
     * 验证覆盖度范围
     */
    public boolean isValidCoverageRange() {
        if (minCoverage != null && maxCoverage != null) {
            return minCoverage <= maxCoverage;
        }
        return true;
    }

    /**
     * 验证时间范围
     */
    public boolean isValidTimeRange() {
        if (generateTimeStart != null && generateTimeEnd != null) {
            return generateTimeStart.isBefore(generateTimeEnd) || generateTimeStart.isEqual(generateTimeEnd);
        }
        return true;
    }
}
