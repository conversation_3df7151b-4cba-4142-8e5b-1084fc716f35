package com.iptv.flux.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


import java.time.LocalDateTime;

/**
 * 黑名单用户数据传输对象
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "黑名单用户信息")
public class BlacklistUserDTO {

    @Schema(description = "黑名单记录ID", example = "1")
    private String id;

    @NotBlank(message = "用户ID不能为空")
    @Schema(description = "用户ID", example = "user123", required = true)
    private String userId;

    @NotBlank(message = "来源不能为空")
    @Pattern(regexp = "^(dx|lt|yd|other)$", message = "来源必须是 telecom、unicom、mobile 或 other")
    @Schema(description = "来源", example = "telecom", required = true,
            allowableValues = {"dx", "lt", "yd", "other"})
    private String source;

    @Schema(description = "备注（违规原因）", example = "恶意刷量")
    private String remark;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "添加时间", example = "2025-07-07 10:30:00")
    private LocalDateTime addTime;

    /**
     * 来源枚举
     */
    public enum Source {
        TELECOM("dx", "电信"),
        UNICOM("lt", "联通"),
        MOBILE("yd", "移动"),
        OTHER("other", "其他");

        private final String code;
        private final String name;

        Source(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static Source fromCode(String code) {
            for (Source source : values()) {
                if (source.code.equals(code)) {
                    return source;
                }
            }
            throw new IllegalArgumentException("未知的来源代码: " + code);
        }
    }
}
