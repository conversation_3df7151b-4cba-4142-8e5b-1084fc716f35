package com.iptv.flux.common.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.common.config
 * @className: CommonSwaggerConfig
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 13:52
 * @version: 1.0
 */
@Configuration
@ConditionalOnProperty(name = "springdoc.api-docs.enabled", havingValue = "true", matchIfMissing = true)
public class CommonSwaggerConfig {

    @Bean
    public OpenAPI fluxOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("IPTV Flux 服务 API")
                        .description("高并发用户分组查询系统 API 文档")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("IPTV开发团队")
                                .email("<EMAIL>")
                                .url("https://www.iptv.com"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("https://www.apache.org/licenses/LICENSE-2.0.html")))
                .components(new Components()
                        .addSecuritySchemes("bearer-jwt",
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .in(SecurityScheme.In.HEADER)
                                        .name("Authorization")));
    }

    /**
     * 创建分组配置工具方法
     *
     * @param groupName 分组名称
     * @param packageName 扫描的包名
     * @param paths API路径前缀
     * @return GroupedOpenApi 配置
     */
    public static GroupedOpenApi buildGroupedApi(String groupName, String packageName, String... paths) {
        return GroupedOpenApi.builder()
                .group(groupName)
                .packagesToScan(packageName)
                .pathsToMatch(paths)
                .build();
    }
}
