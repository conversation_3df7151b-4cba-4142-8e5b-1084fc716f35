package com.iptv.flux.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.common.dto
 * @className: ResultDTO
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 12:49
 * @version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResultDTO<T> {
    private Integer code;
    private String message;
    private T data;
    private Long timestamp;

    public static <T> ResultDTO<T> success(T data) {
        return ResultDTO.<T>builder()
                .code(200)
                .message("Success")
                .data(data)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    public static <T> ResultDTO<T> fail(int code, String message) {
        return ResultDTO.<T>builder()
                .code(code)
                .message(message)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    public static <T> ResultDTO<T> fail(String message) {
        return fail(500, message);
    }
}
