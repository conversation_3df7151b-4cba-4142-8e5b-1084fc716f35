package com.iptv.flux.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.common.dto
 * @className: DataImportResponseDTO
 * @author: chiron
 * @description: 数据导入响应DTO
 * @date: 2025/1/21 16:00
 * @version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataImportResponseDTO {
    
    /**
     * 导入任务ID
     */
    private String taskId;
    
    /**
     * 任务状态：PENDING, PROCESSING, COMPLETED, FAILED
     */
    private String status;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 总记录数
     */
    private Integer totalRecords;

    /**
     * 预估用户数（与totalRecords相同，为前端兼容性保留）
     */
    private Integer estimatedUserCount;

    /**
     * 已处理记录数
     */
    private Integer processedRecords;
    
    /**
     * 成功插入记录数
     */
    private Integer insertedRecords;
    
    /**
     * 成功更新记录数
     */
    private Integer updatedRecords;
    
    /**
     * 失败记录数
     */
    private Integer failedRecords;
    
    /**
     * 处理进度百分比
     */
    private Double progressPercentage;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 耗时（毫秒）
     */
    private Long durationMs;
    
    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 来源标识
     */
    private String source;
}
