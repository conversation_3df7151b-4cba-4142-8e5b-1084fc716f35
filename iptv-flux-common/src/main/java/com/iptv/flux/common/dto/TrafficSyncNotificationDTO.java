package com.iptv.flux.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.common.dto
 * @className: TrafficSyncNotificationDTO
 * @author: Claude 4.0 sonnet
 * @description: 流量平台数据同步通知请求DTO
 * @date: 2025/8/14 12:00
 * @version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "流量平台数据同步通知请求")
public class TrafficSyncNotificationDTO {

    /**
     * 更新时间，格式：yyyyMMddHHmmss
     */
    @NotBlank(message = "更新时间不能为空")
    @Pattern(regexp = "\\d{14}", message = "更新时间格式必须为yyyyMMddHHmmss")
    @Schema(description = "更新时间", example = "20250814120000", required = true)
    private String updateTime;

    /**
     * groups.txt文件FTP路径
     */
    @NotBlank(message = "groups.txt文件路径不能为空")
    @Schema(description = "groups.txt文件FTP路径", 
            example = "*************************************************/20250814/groups.txt", 
            required = true)
    private String groupsFileURL;

    /**
     * strategy.txt文件FTP路径
     */
    @Schema(description = "strategy.txt文件FTP路径", 
            example = "*************************************************/20250814/strategy.txt")
    private String strategysFileURL;

    /**
     * userGroup.txt文件FTP路径
     */
    @NotBlank(message = "userGroup.txt文件路径不能为空")
    @Schema(description = "userGroup.txt文件FTP路径", 
            example = "*************************************************/20250814/userGroup.txt", 
            required = true)
    private String userGroupFileURL;

    /**
     * 三大运营商分群文件路径列表
     */
    @NotEmpty(message = "运营商文件列表不能为空")
    @Valid
    @Schema(description = "三大运营商分群文件路径列表", required = true)
    private List<OperatorFileInfo> operatorFiles;

    /**
     * 运营商文件信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "运营商文件信息")
    public static class OperatorFileInfo {

        /**
         * 运营商系统ID
         */
        @NotBlank(message = "运营商系统ID不能为空")
        @Pattern(regexp = "^(dx|lt|yd)$", message = "运营商系统ID必须是dx、lt或yd")
        @Schema(description = "运营商系统ID", example = "dx", required = true,
                allowableValues = {"dx", "lt", "yd"})
        private String sysID;

        /**
         * 文件FTP路径
         */
        @NotBlank(message = "文件路径不能为空")
        @Schema(description = "文件FTP路径", 
                example = "*************************************************/dx/20250814/dx_platform.txt", 
                required = true)
        private String fileURL;

        /**
         * 文件MD5校验值（可选）
         */
        @Schema(description = "文件MD5校验值", example = "d41d8cd98f00b204e9800998ecf8427e")
        private String md5sum;

        /**
         * 文件描述（可选）
         */
        @Schema(description = "文件描述", example = "电信用户分群数据")
        private String description;
    }

    /**
     * 处理配置选项
     */
    @Valid
    @Schema(description = "处理配置选项")
    private ProcessingOptions options;

    /**
     * 处理配置选项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "处理配置选项")
    public static class ProcessingOptions {

        /**
         * 是否异步处理
         */
        @Builder.Default
        @Schema(description = "是否异步处理", example = "true")
        private Boolean async = true;

        /**
         * 批处理大小
         */
        @Builder.Default
        @Schema(description = "批处理大小", example = "1000")
        private Integer batchSize = 1000;

        /**
         * 是否覆盖已存在的数据
         */
        @Builder.Default
        @Schema(description = "是否覆盖已存在的数据", example = "true")
        private Boolean overwrite = true;

        /**
         * 是否验证MD5校验值
         */
        @Builder.Default
        @Schema(description = "是否验证MD5校验值", example = "true")
        private Boolean verifyMD5 = true;

        /**
         * 超时时间（秒）
         */
        @Builder.Default
        @Schema(description = "处理超时时间（秒）", example = "3600")
        private Integer timeoutSeconds = 3600;

        /**
         * 重试次数
         */
        @Builder.Default
        @Schema(description = "失败重试次数", example = "3")
        private Integer retryAttempts = 3;
    }

    /**
     * 获取运营商名称
     */
    public static String getOperatorName(String sysID) {
        switch (sysID) {
            case "dx":
                return "电信";
            case "lt":
                return "联通";
            case "yd":
                return "移动";
            default:
                return "未知";
        }
    }

    /**
     * 验证运营商系统ID
     */
    public static boolean isValidSysID(String sysID) {
        return "dx".equals(sysID) || "lt".equals(sysID) || "yd".equals(sysID);
    }
}
