package com.iptv.flux.common.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.common.utils
 * @className: JsonUtils
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 12:49
 * @version: 1.0
 */
@Slf4j
public final class JsonUtils {
    private static final ObjectMapper OBJECT_MAPPER;

    static {
        OBJECT_MAPPER = new ObjectMapper();
        OBJECT_MAPPER.registerModule(new JavaTimeModule());
        OBJECT_MAPPER.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    private JsonUtils() {}

    public static String toJson(Object object) {
        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("将对象转换为JSON失败", e);
            throw new RuntimeException("Convert object to JSON failed", e);
        }
    }

    public static <T> T fromJson(String json, Class<T> clazz) {
        try {
            if (json == null || json.isEmpty()) {
                return null;
            }
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (IOException e) {
            log.error("解析JSON为对象失败. JSON: {}, Class: {}", json, clazz.getName(), e);
            throw new RuntimeException("Parse JSON to object failed", e);
        }
    }

    public static <T> T fromJson(String json, TypeReference<T> typeReference) {
        try {
            if (json == null || json.isEmpty()) {
                return null;
            }
            return OBJECT_MAPPER.readValue(json, typeReference);
        } catch (IOException e) {
            log.error("解析JSON对象失败. JSON: {}", json, e);
            throw new RuntimeException("Parse JSON to object failed", e);
        }
    }
}
