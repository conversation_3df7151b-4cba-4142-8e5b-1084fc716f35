package com.iptv.flux.common.exception;

import lombok.Getter;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.common.exception
 * @className: ServiceException
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 12:50
 * @version: 1.0
 */
@Getter
public class ServiceException extends RuntimeException {
    private final int code;

    public ServiceException(String message) {
        super(message);
        this.code = 500;
    }

    public ServiceException(String message, Throwable cause) {
        super(message, cause);
        this.code = 500;
    }

    public ServiceException(int code, String message) {
        super(message);
        this.code = code;
    }

    public ServiceException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }
}
