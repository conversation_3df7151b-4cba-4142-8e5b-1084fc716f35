package com.iptv.flux.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.common.config
 * @className: CorsConfig
 * @author: chiron
 * @description: CORS配置类，用于处理跨域请求
 * @date: 2025/5/30 16:33
 * @version: 1.0
 */
@Configuration
@Slf4j
public class CorsConfig {

    @Bean
    public CorsFilter corsFilter() {
        log.info("初始化 CORS 配置，允许跨域请求...");

        CorsConfiguration config = new CorsConfiguration();
        // 允许所有来源
        config.addAllowedOriginPattern("*");
        // 允许所有头信息
        config.addAllowedHeader("*");
        // 允许所有请求方法
        config.addAllowedMethod("*");
        // 允许携带认证信息(cookies等)
        config.setAllowCredentials(false);
        // 预检请求的有效期，单位为秒
        config.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        // 为所有API路径配置CORS
        source.registerCorsConfiguration("/**", config);

        log.info("CORS 配置完成，支持跨域请求");
        return new CorsFilter(source);
    }
}
