package com.iptv.flux.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Size;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.common.dto
 * @className: DataImportRequestDTO
 * @author: chiron
 * @description: 数据导入请求DTO
 * @date: 2025/1/21 16:00
 * @version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataImportRequestDTO {
    
    /**
     * 来源标识，默认为dx
     */
    @Builder.Default
    private String source = "dx";
    
    /**
     * 导入任务描述
     */
    @Size(max = 200, message = "描述长度不能超过200个字符")
    private String description;
    
    /**
     * 是否覆盖已存在的数据
     */
    @Builder.Default
    private Boolean overwrite = false;
    
    /**
     * 批处理大小
     */
    @Builder.Default
    private Integer batchSize = 1000;
    
    /**
     * 是否异步处理
     */
    @Builder.Default
    private Boolean async = true;

    /**
     * 分组策略ID（可选，如果不提供则使用默认值）
     */
    private String strategyId;

    /**
     * 分组名称（可选，如果不提供则从文件名生成）
     */
    @Size(max = 128, message = "分组名称长度不能超过128个字符")
    private String groupName;

    /**
     * 分组描述（可选）
     */
    @Size(max = 512, message = "分组描述长度不能超过512个字符")
    private String groupDescription;

    /**
     * 是否自动创建分组信息
     */
    @Builder.Default
    private Boolean autoCreateGroup = true;
}
