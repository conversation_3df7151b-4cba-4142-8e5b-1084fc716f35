package com.iptv.flux.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.common.dto
 * @className: DataImportProgressDTO
 * @author: chiron
 * @description: 数据导入进度DTO
 * @date: 2025/1/21 16:00
 * @version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataImportProgressDTO {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 当前状态
     */
    private String status;
    
    /**
     * 当前批次
     */
    private Integer currentBatch;
    
    /**
     * 总批次数
     */
    private Integer totalBatches;
    
    /**
     * 已处理记录数
     */
    private Integer processedRecords;
    
    /**
     * 总记录数
     */
    private Integer totalRecords;
    
    /**
     * 成功记录数
     */
    private Integer successRecords;
    
    /**
     * 失败记录数
     */
    private Integer failedRecords;
    
    /**
     * 进度百分比
     */
    private Double progressPercentage;
    
    /**
     * 预计剩余时间（秒）
     */
    private Long estimatedRemainingSeconds;
    
    /**
     * 当前处理速度（记录/秒）
     */
    private Double processingSpeed;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    /**
     * 错误信息
     */
    private String errorMessage;
}
