package com.iptv.flux.common.swagger;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
public final class SwaggerAnnotations {

    private SwaggerAnnotations() {}

    /**
     * 标准查询操作注解
     */
    @Target({ElementType.METHOD})
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    @Operation(description = "查询操作")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "成功"),
            @ApiResponse(responseCode = "404", description = "数据不存在"),
            @ApiResponse(responseCode = "500", description = "服务器错误")
    })
    public @interface QueryOperation {
        String summary();
        String description() default "";
        Class<?> responseType() default Void.class;
    }

    /**
     * 标准保存操作注解
     */
    @Target({ElementType.METHOD})
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    @Operation(description = "保存操作")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "保存成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器错误")
    })
    public @interface SaveOperation {
        String summary();
        String description() default "";
    }
}