package com.iptv.flux.common.utils;

import com.iptv.flux.common.constants.UserGroupConstants;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.common.utils
 * @className: CacheKeyBuilder
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 12:49
 * @version: 1.0
 */
public final class CacheKeyBuilder {

    private CacheKeyBuilder() {}

    public static String buildUserGroupCompositeKey(String source, String userId) {
        return String.format("%s:%s", source, userId);
    }

    public static String buildUserGroupRedisKey(String compositeKey) {
        return String.format("{%s}:%s", UserGroupConstants.USER_GROUP_HASH_KEY_PREFIX, compositeKey);
    }

    public static String buildGroupInfoRedisKey(String groupId) {
        return String.format("{%s}:%s", UserGroupConstants.GROUP_INFO_HASH_KEY_PREFIX, groupId);
    }

    public static String buildLockKey(String redisKey) {
        return redisKey + UserGroupConstants.LOCK_SUFFIX;
    }
}
