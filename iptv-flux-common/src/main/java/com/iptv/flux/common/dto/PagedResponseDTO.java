package com.iptv.flux.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 分页响应DTO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "分页响应数据")
public class PagedResponseDTO<T> {

    @Schema(description = "数据列表")
    private List<T> list;

    @Schema(description = "总记录数", example = "100")
    private Long total;

    @Schema(description = "当前页码", example = "1")
    private Integer page;

    @Schema(description = "每页条数", example = "10")
    private Integer pageSize;

    @Schema(description = "总页数", example = "10")
    private Integer totalPages;

    /**
     * 计算总页数
     */
    public void calculateTotalPages() {
        if (total != null && pageSize != null && pageSize > 0) {
            this.totalPages = (int) Math.ceil((double) total / pageSize);
        } else {
            this.totalPages = 0;
        }
    }

    /**
     * 创建分页响应
     */
    public static <T> PagedResponseDTO<T> of(List<T> list, Long total, Integer page, Integer pageSize) {
        PagedResponseDTO<T> response = PagedResponseDTO.<T>builder()
                .list(list)
                .total(total)
                .page(page)
                .pageSize(pageSize)
                .build();
        response.calculateTotalPages();
        return response;
    }
}
