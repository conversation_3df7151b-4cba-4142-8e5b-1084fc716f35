package com.iptv.flux.common.utils;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.Timer.Builder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.function.ToDoubleFunction;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.common.utils
 * @className: MetricsRegistryUtil
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 12:50
 * @version: 1.0
 */
@Slf4j
@RequiredArgsConstructor
public class MetricsRegistryUtil {

    private final MeterRegistry meterRegistry;
    private final ConcurrentHashMap<String, Timer> timers = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Counter> counters = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Gauge> gauges = new ConcurrentHashMap<>();

    public Timer createTimer(String name) {
        return timers.computeIfAbsent(name, k -> {
            log.info("创建 {} 的计时器", name);
            return Timer.builder(name)
                    .publishPercentiles(0.5, 0.95, 0.99)
                    .publishPercentileHistogram()
                    .register(meterRegistry);
        });
    }

    public Timer createTimer(String name, String... tags) {
        String key = buildKey(name, tags);
        return timers.computeIfAbsent(key, k -> {
            log.info("创建 {} 的计时器并添加标签 {}", name, tags);
            Builder builder = Timer.builder(name);
            for (int i = 0; i < tags.length; i += 2) {
                if (i + 1 < tags.length) {
                    builder.tag(tags[i], tags[i + 1]);
                }
            }
            return builder
                    .publishPercentiles(0.5, 0.95, 0.99)
                    .publishPercentileHistogram()
                    .register(meterRegistry);
        });
    }

    public Counter createCounter(String name) {
        return counters.computeIfAbsent(name, k -> {
            log.info("创建计数器并命名: {}", name);
            return Counter.builder(name).register(meterRegistry);
        });
    }

    public Counter createCounter(String name, String... tags) {
        String key = buildKey(name, tags);
        return counters.computeIfAbsent(key, k -> {
            log.info("创建计数器，名称为：{}，标签为：{}", name, tags);
            io.micrometer.core.instrument.Counter.Builder builder = Counter.builder(name);
            for (int i = 0; i < tags.length; i += 2) {
                if (i + 1 < tags.length) {
                    builder.tag(tags[i], tags[i + 1]);
                }
            }
            return builder.register(meterRegistry);
        });
    }

    public <T> Gauge createGauge(String name, T obj, java.util.function.ToDoubleFunction<T> valueFunction) {
        log.info("创建量表: {}", name);
        return Gauge.builder(name, obj, valueFunction)
                .register(meterRegistry);
    }

    public <T> T recordTimerMetric(String name, Supplier<T> supplier) {
        Timer timer = createTimer(name);
        return recordTimerMetric(timer, supplier);
    }

    public <T> T recordTimerMetric(Timer timer, Supplier<T> supplier) {
        return timer.record(supplier);
    }

    public void incrementCounter(String name) {
        Counter counter = createCounter(name);
        counter.increment();
    }

    public void incrementCounter(String name, String... tags) {
        Counter counter = createCounter(name, tags);
        counter.increment();
    }

    public void incrementCounter(String name, double amount) {
        Counter counter = createCounter(name);
        counter.increment(amount);
    }

    public void incrementCounter(String name, double amount, String... tags) {
        Counter counter = createCounter(name, tags);
        counter.increment(amount);
    }

    public void recordExecutionTime(String name, long startTime) {
        Timer timer = createTimer(name);
        timer.record(System.currentTimeMillis() - startTime, TimeUnit.MILLISECONDS);
    }

    private String buildKey(String name, String... tags) {
        StringBuilder sb = new StringBuilder(name);
        for (String tag : tags) {
            sb.append(':').append(tag);
        }
        return sb.toString();
    }


    /**
     * 获取计数器值
     */
    public double getCounterValue(String name, String... tags) {
        try {
            return meterRegistry.find(name).tags(tags).counter().count();
        } catch (Exception e) {
            return 0;
        }
    }


    /**
     * 记录计时器耗时
     */
    public <T> T recordTimerMetric(String name, Supplier<T> supplier, String... tags) {
        return meterRegistry.timer(name, tags).record(supplier);
    }

    /**
     * 记录执行时间
     */
    public void recordExecutionTime(String name, long startTimeMillis, String... tags) {
        long duration = System.currentTimeMillis() - startTimeMillis;
        meterRegistry.timer(name, tags).record(duration, TimeUnit.MILLISECONDS);
    }

    /**
     * 获取计时器平均时间（毫秒）
     */
    public double getTimerMean(String name, String... tags) {
        try {
            return meterRegistry.find(name).tags(tags).timer().mean(TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 获取计时器最大时间（毫秒）
     */
    public double getTimerMax(String name, String... tags) {
        try {
            return meterRegistry.find(name).tags(tags).timer().max(TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 获取计时器百分位数（毫秒）
     */
    public double getTimerPercentile(String name, double percentile, String... tags) {
        try {
            return meterRegistry.find(name).tags(tags).timer().percentile(percentile, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 获取计时器调用次数
     */
    public long getTimerCount(String name, String... tags) {
        try {
            return meterRegistry.find(name).tags(tags).timer().count();
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 创建gauge指标
     */
    public <T> Gauge createGauge(String name, T obj, ToDoubleFunction<T> valueFunction, String... tags) {
        return Gauge.builder(name, obj, valueFunction)
                .tags(tags)
                .register(meterRegistry);
    }

    /**
     * 获取最近时间段的计数器值
     */
    public double getCounterValueForLast(String name, TimeUnit unit, long amount) {
        try {
            return getCounterValue(name);
        } catch (Exception e) {
            return 0;
        }
    }
}
