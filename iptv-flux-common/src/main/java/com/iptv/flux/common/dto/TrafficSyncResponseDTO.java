package com.iptv.flux.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.common.dto
 * @className: TrafficSyncResponseDTO
 * @author: Claude 4.0 sonnet
 * @description: 流量平台数据同步响应DTO
 * @date: 2025/8/14 12:00
 * @version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "流量平台数据同步响应")
public class TrafficSyncResponseDTO {

    /**
     * 同步任务ID
     */
    @Schema(description = "同步任务ID", example = "sync_1723622400000_abc123")
    private String taskId;

    /**
     * 处理状态
     */
    @Schema(description = "处理状态", example = "COMPLETED", 
            allowableValues = {"PENDING", "PROCESSING", "COMPLETED", "FAILED", "PARTIAL_SUCCESS"})
    private String status;

    /**
     * 处理开始时间
     */
    @Schema(description = "处理开始时间", example = "2025-08-14T12:00:00")
    private LocalDateTime processTime;

    /**
     * 处理结束时间
     */
    @Schema(description = "处理结束时间", example = "2025-08-14T12:05:30")
    private LocalDateTime completedTime;

    /**
     * 处理耗时（毫秒）
     */
    @Schema(description = "处理耗时（毫秒）", example = "330000")
    private Long durationMs;

    /**
     * 分组信息处理结果
     */
    @Schema(description = "分组信息处理结果")
    private GroupProcessResult groupsProcessed;

    /**
     * 用户分组关系处理结果
     */
    @Schema(description = "用户分组关系处理结果")
    private UserGroupProcessResult usersProcessed;

    /**
     * 运营商文件处理结果列表
     */
    @Schema(description = "运营商文件处理结果列表")
    private List<OperatorProcessResult> operatorResults;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 处理消息
     */
    @Schema(description = "处理消息", example = "数据同步成功完成")
    private String message;

    /**
     * 分组信息处理结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "分组信息处理结果")
    public static class GroupProcessResult {

        /**
         * 文件名
         */
        @Schema(description = "文件名", example = "groups.txt")
        private String fileName;

        /**
         * 文件大小（字节）
         */
        @Schema(description = "文件大小（字节）", example = "1024000")
        private Long fileSize;

        /**
         * 总记录数
         */
        @Schema(description = "总记录数", example = "150")
        private Integer totalRecords;

        /**
         * 成功处理记录数
         */
        @Schema(description = "成功处理记录数", example = "148")
        private Integer processedRecords;

        /**
         * 插入记录数
         */
        @Schema(description = "插入记录数", example = "50")
        private Integer insertedRecords;

        /**
         * 更新记录数
         */
        @Schema(description = "更新记录数", example = "98")
        private Integer updatedRecords;

        /**
         * 失败记录数
         */
        @Schema(description = "失败记录数", example = "2")
        private Integer failedRecords;

        /**
         * 处理状态
         */
        @Schema(description = "处理状态", example = "SUCCESS")
        private String status;

        /**
         * 错误信息
         */
        @Schema(description = "错误信息")
        private String errorMessage;
    }

    /**
     * 用户分组关系处理结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "用户分组关系处理结果")
    public static class UserGroupProcessResult {

        /**
         * 文件名
         */
        @Schema(description = "文件名", example = "userGroup.txt")
        private String fileName;

        /**
         * 文件大小（字节）
         */
        @Schema(description = "文件大小（字节）", example = "5120000")
        private Long fileSize;

        /**
         * 总用户数
         */
        @Schema(description = "总用户数", example = "100000")
        private Integer totalUsers;

        /**
         * 成功处理用户数
         */
        @Schema(description = "成功处理用户数", example = "99950")
        private Integer processedUsers;

        /**
         * 插入用户数
         */
        @Schema(description = "插入用户数", example = "30000")
        private Integer insertedUsers;

        /**
         * 更新用户数
         */
        @Schema(description = "更新用户数", example = "69950")
        private Integer updatedUsers;

        /**
         * 失败用户数
         */
        @Schema(description = "失败用户数", example = "50")
        private Integer failedUsers;

        /**
         * 处理状态
         */
        @Schema(description = "处理状态", example = "SUCCESS")
        private String status;

        /**
         * 错误信息
         */
        @Schema(description = "错误信息")
        private String errorMessage;
    }

    /**
     * 运营商文件处理结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "运营商文件处理结果")
    public static class OperatorProcessResult {

        /**
         * 运营商系统ID
         */
        @Schema(description = "运营商系统ID", example = "dx")
        private String sysID;

        /**
         * 运营商名称
         */
        @Schema(description = "运营商名称", example = "电信")
        private String operatorName;

        /**
         * 文件名
         */
        @Schema(description = "文件名", example = "dx_platform.txt")
        private String fileName;

        /**
         * 文件大小（字节）
         */
        @Schema(description = "文件大小（字节）", example = "2048000")
        private Long fileSize;

        /**
         * 用户数量
         */
        @Schema(description = "用户数量", example = "35000")
        private Integer userCount;

        /**
         * 处理状态
         */
        @Schema(description = "处理状态", example = "SUCCESS")
        private String status;

        /**
         * 错误信息
         */
        @Schema(description = "错误信息")
        private String errorMessage;

        /**
         * MD5校验结果
         */
        @Schema(description = "MD5校验结果", example = "true")
        private Boolean md5Verified;
    }

    /**
     * 创建成功响应
     */
    public static TrafficSyncResponseDTO success(String taskId, String message) {
        return TrafficSyncResponseDTO.builder()
                .taskId(taskId)
                .status("COMPLETED")
                .processTime(LocalDateTime.now())
                .completedTime(LocalDateTime.now())
                .message(message)
                .build();
    }

    /**
     * 创建失败响应
     */
    public static TrafficSyncResponseDTO failure(String taskId, String errorMessage) {
        return TrafficSyncResponseDTO.builder()
                .taskId(taskId)
                .status("FAILED")
                .processTime(LocalDateTime.now())
                .completedTime(LocalDateTime.now())
                .errorMessage(errorMessage)
                .message("数据同步失败")
                .build();
    }

    /**
     * 创建处理中响应
     */
    public static TrafficSyncResponseDTO processing(String taskId) {
        return TrafficSyncResponseDTO.builder()
                .taskId(taskId)
                .status("PROCESSING")
                .processTime(LocalDateTime.now())
                .message("数据同步正在处理中")
                .build();
    }
}
